<?php
/**
 * API para reportes y consultas del inventario
 */

require_once 'config/Database.php';
require_once 'config/ApiResponse.php';

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $path_info = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
    
    if ($method !== 'GET') {
        ApiResponse::error("Solo se permiten consultas GET", 405);
    }
    
    // Extraer acción de la URL
    $action = '';
    if ($path_info) {
        $parts = explode('/', trim($path_info, '/'));
        $action = $parts[0] ?? '';
    }
    
    switch ($action) {
        case 'stock-bajo':
            getStockBajo($db);
            break;
        case 'resumen-inventario':
            getResumenInventario($db);
            break;
        case 'movimientos':
            getMovimientos($db);
            break;
        case 'alertas':
            getAlertas($db);
            break;
        case 'produccion':
            getReporteProduccion($db);
            break;
        case 'costos':
            getReporteCostos($db);
            break;
        default:
            ApiResponse::error("Reporte no válido. Disponibles: stock-bajo, resumen-inventario, movimientos, alertas, produccion, costos", 400);
    }
    
} catch (Exception $e) {
    error_log("Error en API reportes: " . $e->getMessage());
    ApiResponse::serverError();
}

/**
 * Obtener componentes con stock bajo
 */
function getStockBajo($db) {
    try {
        $stockBajo = [];
        
        // Líquidos con stock bajo
        $queryLiquidos = "SELECT 'liquido' as tipo, id, nombre, stock_ml as stock_actual, stock_minimo_ml as stock_minimo, 
                                 ROUND((stock_ml / stock_minimo_ml) * 100, 2) as porcentaje_stock
                          FROM liquidos 
                          WHERE stock_ml <= stock_minimo_ml AND activo = TRUE
                          ORDER BY porcentaje_stock ASC";
        $stmt = $db->prepare($queryLiquidos);
        $stmt->execute();
        $stockBajo = array_merge($stockBajo, $stmt->fetchAll());
        
        // Botellas con stock bajo
        $queryBotellas = "SELECT 'botella' as tipo, id, nombre, stock_cantidad as stock_actual, stock_minimo, 
                                 ROUND((stock_cantidad / stock_minimo) * 100, 2) as porcentaje_stock
                          FROM botellas 
                          WHERE stock_cantidad <= stock_minimo AND activo = TRUE
                          ORDER BY porcentaje_stock ASC";
        $stmt = $db->prepare($queryBotellas);
        $stmt->execute();
        $stockBajo = array_merge($stockBajo, $stmt->fetchAll());
        
        // Tapones con stock bajo
        $queryTapones = "SELECT 'tapon' as tipo, id, nombre, stock_cantidad as stock_actual, stock_minimo, 
                                ROUND((stock_cantidad / stock_minimo) * 100, 2) as porcentaje_stock
                         FROM tapones 
                         WHERE stock_cantidad <= stock_minimo AND activo = TRUE
                         ORDER BY porcentaje_stock ASC";
        $stmt = $db->prepare($queryTapones);
        $stmt->execute();
        $stockBajo = array_merge($stockBajo, $stmt->fetchAll());
        
        // Cápsulas con stock bajo
        $queryCapsulas = "SELECT 'capsula' as tipo, id, nombre, stock_cantidad as stock_actual, stock_minimo, 
                                 ROUND((stock_cantidad / stock_minimo) * 100, 2) as porcentaje_stock
                          FROM capsulas 
                          WHERE stock_cantidad <= stock_minimo AND activo = TRUE
                          ORDER BY porcentaje_stock ASC";
        $stmt = $db->prepare($queryCapsulas);
        $stmt->execute();
        $stockBajo = array_merge($stockBajo, $stmt->fetchAll());
        
        // Etiquetas con stock bajo
        $queryEtiquetas = "SELECT 'etiqueta' as tipo, id, nombre, stock_cantidad as stock_actual, stock_minimo, 
                                  ROUND((stock_cantidad / stock_minimo) * 100, 2) as porcentaje_stock
                           FROM etiquetas 
                           WHERE stock_cantidad <= stock_minimo AND activo = TRUE
                           ORDER BY porcentaje_stock ASC";
        $stmt = $db->prepare($queryEtiquetas);
        $stmt->execute();
        $stockBajo = array_merge($stockBajo, $stmt->fetchAll());
        
        // Ordenar por porcentaje de stock
        usort($stockBajo, function($a, $b) {
            return $a['porcentaje_stock'] <=> $b['porcentaje_stock'];
        });
        
        ApiResponse::success($stockBajo, "Reporte de stock bajo generado");
        
    } catch (Exception $e) {
        error_log("Error generando reporte stock bajo: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Obtener resumen general del inventario
 */
function getResumenInventario($db) {
    try {
        $resumen = [];
        
        // Resumen de líquidos
        $queryLiquidos = "SELECT COUNT(*) as total, SUM(stock_ml) as stock_total_ml, 
                                 SUM(CASE WHEN stock_ml <= stock_minimo_ml THEN 1 ELSE 0 END) as con_stock_bajo
                          FROM liquidos WHERE activo = TRUE";
        $stmt = $db->prepare($queryLiquidos);
        $stmt->execute();
        $resumen['liquidos'] = $stmt->fetch();
        
        // Resumen de botellas
        $queryBotellas = "SELECT COUNT(*) as total, SUM(stock_cantidad) as stock_total, 
                                 SUM(CASE WHEN stock_cantidad <= stock_minimo THEN 1 ELSE 0 END) as con_stock_bajo
                          FROM botellas WHERE activo = TRUE";
        $stmt = $db->prepare($queryBotellas);
        $stmt->execute();
        $resumen['botellas'] = $stmt->fetch();
        
        // Resumen de tapones
        $queryTapones = "SELECT COUNT(*) as total, SUM(stock_cantidad) as stock_total, 
                                SUM(CASE WHEN stock_cantidad <= stock_minimo THEN 1 ELSE 0 END) as con_stock_bajo
                         FROM tapones WHERE activo = TRUE";
        $stmt = $db->prepare($queryTapones);
        $stmt->execute();
        $resumen['tapones'] = $stmt->fetch();
        
        // Resumen de cápsulas
        $queryCapsulas = "SELECT COUNT(*) as total, SUM(stock_cantidad) as stock_total, 
                                 SUM(CASE WHEN stock_cantidad <= stock_minimo THEN 1 ELSE 0 END) as con_stock_bajo
                          FROM capsulas WHERE activo = TRUE";
        $stmt = $db->prepare($queryCapsulas);
        $stmt->execute();
        $resumen['capsulas'] = $stmt->fetch();
        
        // Resumen de etiquetas
        $queryEtiquetas = "SELECT COUNT(*) as total, SUM(stock_cantidad) as stock_total, 
                                  SUM(CASE WHEN stock_cantidad <= stock_minimo THEN 1 ELSE 0 END) as con_stock_bajo
                           FROM etiquetas WHERE activo = TRUE";
        $stmt = $db->prepare($queryEtiquetas);
        $stmt->execute();
        $resumen['etiquetas'] = $stmt->fetch();
        
        // Estadísticas de embotellado (últimos 30 días)
        $queryEmbotellado = "SELECT COUNT(*) as lotes_total, SUM(cantidad_botellas) as botellas_embotelladas, 
                                    SUM(ml_total_usado) as ml_total_usado
                             FROM lotes_embotellado 
                             WHERE fecha_embotellado >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
        $stmt = $db->prepare($queryEmbotellado);
        $stmt->execute();
        $resumen['embotellado_30_dias'] = $stmt->fetch();
        
        ApiResponse::success($resumen, "Resumen de inventario generado");
        
    } catch (Exception $e) {
        error_log("Error generando resumen inventario: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Obtener movimientos de inventario
 */
function getMovimientos($db) {
    try {
        $limite = (int)($_GET['limite'] ?? 100);
        $tipoComponente = $_GET['tipo_componente'] ?? null;
        $tipoMovimiento = $_GET['tipo_movimiento'] ?? null;
        $fechaDesde = $_GET['fecha_desde'] ?? null;
        $fechaHasta = $_GET['fecha_hasta'] ?? null;
        
        $filtros = [];
        $params = [];
        
        if ($tipoComponente) {
            $filtros[] = "tipo_componente = :tipo_componente";
            $params[':tipo_componente'] = $tipoComponente;
        }
        
        if ($tipoMovimiento) {
            $filtros[] = "tipo_movimiento = :tipo_movimiento";
            $params[':tipo_movimiento'] = $tipoMovimiento;
        }
        
        if ($fechaDesde) {
            $filtros[] = "DATE(created_at) >= :fecha_desde";
            $params[':fecha_desde'] = $fechaDesde;
        }
        
        if ($fechaHasta) {
            $filtros[] = "DATE(created_at) <= :fecha_hasta";
            $params[':fecha_hasta'] = $fechaHasta;
        }
        
        $whereClause = !empty($filtros) ? 'WHERE ' . implode(' AND ', $filtros) : '';
        
        $query = "SELECT * FROM movimientos_inventario 
                  $whereClause 
                  ORDER BY created_at DESC 
                  LIMIT :limite";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':limite', $limite, PDO::PARAM_INT);
        
        foreach ($params as $key => $value) {
            $stmt->bindParam($key, $value);
        }
        
        $stmt->execute();
        $movimientos = $stmt->fetchAll();
        
        ApiResponse::success($movimientos, "Movimientos obtenidos exitosamente");
        
    } catch (Exception $e) {
        error_log("Error obteniendo movimientos: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Obtener alertas activas
 */
function getAlertas($db) {
    try {
        // Generar alertas automáticamente
        generarAlertas($db);
        
        $query = "SELECT * FROM alertas_stock WHERE estado = 'activa' ORDER BY created_at DESC";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $alertas = $stmt->fetchAll();
        
        ApiResponse::success($alertas, "Alertas obtenidas exitosamente");
        
    } catch (Exception $e) {
        error_log("Error obteniendo alertas: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Generar alertas de stock bajo automáticamente
 */
function generarAlertas($db) {
    try {
        // Limpiar alertas resueltas automáticamente
        $db->exec("UPDATE alertas_stock SET estado = 'resuelta', resolved_at = NOW() 
                   WHERE estado = 'activa' AND (
                       (tipo_componente = 'liquido' AND componente_id IN (SELECT id FROM liquidos WHERE stock_ml > stock_minimo_ml)) OR
                       (tipo_componente = 'botella' AND componente_id IN (SELECT id FROM botellas WHERE stock_cantidad > stock_minimo)) OR
                       (tipo_componente = 'tapon' AND componente_id IN (SELECT id FROM tapones WHERE stock_cantidad > stock_minimo)) OR
                       (tipo_componente = 'capsula' AND componente_id IN (SELECT id FROM capsulas WHERE stock_cantidad > stock_minimo)) OR
                       (tipo_componente = 'etiqueta' AND componente_id IN (SELECT id FROM etiquetas WHERE stock_cantidad > stock_minimo))
                   )");
        
        // Generar nuevas alertas para líquidos
        $db->exec("INSERT IGNORE INTO alertas_stock (tipo_componente, componente_id, nombre_componente, stock_actual, stock_minimo)
                   SELECT 'liquido', id, nombre, stock_ml, stock_minimo_ml
                   FROM liquidos 
                   WHERE stock_ml <= stock_minimo_ml AND activo = TRUE
                   AND id NOT IN (SELECT componente_id FROM alertas_stock WHERE tipo_componente = 'liquido' AND estado = 'activa')");
        
        // Generar nuevas alertas para otros componentes
        $componentes = ['botella' => 'botellas', 'tapon' => 'tapones', 'capsula' => 'capsulas', 'etiqueta' => 'etiquetas'];
        
        foreach ($componentes as $tipo => $tabla) {
            $db->exec("INSERT IGNORE INTO alertas_stock (tipo_componente, componente_id, nombre_componente, stock_actual, stock_minimo)
                       SELECT '$tipo', id, nombre, stock_cantidad, stock_minimo
                       FROM $tabla 
                       WHERE stock_cantidad <= stock_minimo AND activo = TRUE
                       AND id NOT IN (SELECT componente_id FROM alertas_stock WHERE tipo_componente = '$tipo' AND estado = 'activa')");
        }
        
    } catch (Exception $e) {
        error_log("Error generando alertas: " . $e->getMessage());
    }
}

/**
 * Reporte de producción
 */
function getReporteProduccion($db) {
    try {
        $fechaDesde = $_GET['fecha_desde'] ?? date('Y-m-01'); // Primer día del mes actual
        $fechaHasta = $_GET['fecha_hasta'] ?? date('Y-m-d'); // Hoy
        
        $query = "SELECT le.*, l.nombre as liquido_nombre, l.categoria, ce.nombre_configuracion,
                         ce.capacidad_ml, b.nombre as botella_nombre
                  FROM lotes_embotellado le
                  JOIN liquidos l ON le.liquido_id = l.id
                  JOIN configuracion_embotellado ce ON le.configuracion_id = ce.id
                  JOIN botellas b ON ce.botella_id = b.id
                  WHERE le.fecha_embotellado BETWEEN :fecha_desde AND :fecha_hasta
                  ORDER BY le.fecha_embotellado DESC, le.created_at DESC";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':fecha_desde', $fechaDesde);
        $stmt->bindParam(':fecha_hasta', $fechaHasta);
        $stmt->execute();
        
        $lotes = $stmt->fetchAll();
        
        // Calcular totales
        $totales = [
            'lotes_total' => count($lotes),
            'botellas_total' => array_sum(array_column($lotes, 'cantidad_botellas')),
            'ml_total' => array_sum(array_column($lotes, 'ml_total_usado')),
            'por_categoria' => [],
            'por_capacidad' => []
        ];
        
        foreach ($lotes as $lote) {
            // Por categoría
            if (!isset($totales['por_categoria'][$lote['categoria']])) {
                $totales['por_categoria'][$lote['categoria']] = ['lotes' => 0, 'botellas' => 0, 'ml' => 0];
            }
            $totales['por_categoria'][$lote['categoria']]['lotes']++;
            $totales['por_categoria'][$lote['categoria']]['botellas'] += $lote['cantidad_botellas'];
            $totales['por_categoria'][$lote['categoria']]['ml'] += $lote['ml_total_usado'];
            
            // Por capacidad
            $capacidad = $lote['capacidad_ml'] . 'ml';
            if (!isset($totales['por_capacidad'][$capacidad])) {
                $totales['por_capacidad'][$capacidad] = ['lotes' => 0, 'botellas' => 0, 'ml' => 0];
            }
            $totales['por_capacidad'][$capacidad]['lotes']++;
            $totales['por_capacidad'][$capacidad]['botellas'] += $lote['cantidad_botellas'];
            $totales['por_capacidad'][$capacidad]['ml'] += $lote['ml_total_usado'];
        }
        
        $reporte = [
            'periodo' => ['desde' => $fechaDesde, 'hasta' => $fechaHasta],
            'totales' => $totales,
            'lotes' => $lotes
        ];
        
        ApiResponse::success($reporte, "Reporte de producción generado");
        
    } catch (Exception $e) {
        error_log("Error generando reporte producción: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Reporte de costos
 */
function getReporteCostos($db) {
    try {
        $query = "SELECT 
                    'liquido' as tipo,
                    nombre,
                    stock_ml as stock,
                    costo_por_ml as costo_unitario,
                    (stock_ml * costo_por_ml) as valor_inventario
                  FROM liquidos WHERE activo = TRUE
                  UNION ALL
                  SELECT 
                    'botella' as tipo,
                    nombre,
                    stock_cantidad as stock,
                    costo_unitario,
                    (stock_cantidad * costo_unitario) as valor_inventario
                  FROM botellas WHERE activo = TRUE
                  UNION ALL
                  SELECT 
                    'tapon' as tipo,
                    nombre,
                    stock_cantidad as stock,
                    costo_unitario,
                    (stock_cantidad * costo_unitario) as valor_inventario
                  FROM tapones WHERE activo = TRUE
                  UNION ALL
                  SELECT 
                    'capsula' as tipo,
                    nombre,
                    stock_cantidad as stock,
                    costo_unitario,
                    (stock_cantidad * costo_unitario) as valor_inventario
                  FROM capsulas WHERE activo = TRUE
                  UNION ALL
                  SELECT 
                    'etiqueta' as tipo,
                    nombre,
                    stock_cantidad as stock,
                    costo_unitario,
                    (stock_cantidad * costo_unitario) as valor_inventario
                  FROM etiquetas WHERE activo = TRUE
                  ORDER BY valor_inventario DESC";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $componentes = $stmt->fetchAll();
        
        // Calcular totales por tipo
        $totalesPorTipo = [];
        $valorTotal = 0;
        
        foreach ($componentes as $componente) {
            $tipo = $componente['tipo'];
            if (!isset($totalesPorTipo[$tipo])) {
                $totalesPorTipo[$tipo] = ['items' => 0, 'valor_total' => 0];
            }
            $totalesPorTipo[$tipo]['items']++;
            $totalesPorTipo[$tipo]['valor_total'] += $componente['valor_inventario'];
            $valorTotal += $componente['valor_inventario'];
        }
        
        $reporte = [
            'valor_total_inventario' => $valorTotal,
            'totales_por_tipo' => $totalesPorTipo,
            'componentes' => $componentes
        ];
        
        ApiResponse::success($reporte, "Reporte de costos generado");
        
    } catch (Exception $e) {
        error_log("Error generando reporte costos: " . $e->getMessage());
        ApiResponse::serverError();
    }
}
?>
