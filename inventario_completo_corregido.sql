-- =====================================================
-- SISTEMA COMPLETO DE INVENTARIO Y VENTAS UVAMAYU
-- ARCHIVO ÚNICO CORREGIDO
-- =====================================================

-- Eliminar base de datos si existe y crear nueva
-- DROP DATABASE IF EXISTS uvamayu_inventario;
-- CREATE DATABASE uvamayu_inventario CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE uvamayu_inventario;

-- =====================================================
-- TABLAS DE INVENTARIO
-- =====================================================

-- Tabla de tipos de líquidos (7 productos base) CON PRECIOS
CREATE TABLE liquidos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(50) NOT NULL UNIQUE,
    categoria ENUM('pisco', 'vino') NOT NULL,
    descripcion TEXT,
    stock_ml DECIMAL(10,2) DEFAULT 0,
    stock_minimo_ml DECIMAL(10,2) DEFAULT 1000,
    costo_por_ml DECIMAL(8,4) DEFAULT 0,
    precio_750ml DECIMAL(8,2) DEFAULT 0,
    precio_caja_12 DECIMAL(8,2) DEFAULT 0,
    precio_4000ml DECIMAL(8,2) DEFAULT 0,
    precio_50ml DECIMAL(8,2) DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de tipos de botellas
CREATE TABLE botellas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(100) NOT NULL,
    capacidad_ml INT NOT NULL,
    color ENUM('transparente', 'verde') NOT NULL,
    tipo_uso ENUM('pisco', 'vino', 'ambos') NOT NULL,
    stock_cantidad INT DEFAULT 0,
    stock_minimo INT DEFAULT 50,
    costo_unitario DECIMAL(8,2) DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de tipos de tapones
CREATE TABLE tapones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(100) NOT NULL,
    tipo ENUM('corcho_sintetico', 'tapa_rosca_negra', 'tapa_porron') NOT NULL,
    compatible_con TEXT,
    stock_cantidad INT DEFAULT 0,
    stock_minimo INT DEFAULT 50,
    costo_unitario DECIMAL(8,2) DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de tipos de cápsulas
CREATE TABLE capsulas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(100) NOT NULL,
    capacidad_botella INT NOT NULL,
    color ENUM('negro', 'dorado') NOT NULL,
    tipo_producto ENUM('pisco', 'vino') NOT NULL,
    stock_cantidad INT DEFAULT 0,
    stock_minimo INT DEFAULT 50,
    costo_unitario DECIMAL(8,2) DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de etiquetas (28 tipos: 7 productos × 4 tipos)
CREATE TABLE etiquetas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(200) NOT NULL,
    tipo_etiqueta ENUM('porron', 'botella_750ml_adelante', 'botella_750ml_atras', 'botella_regalo_50ml') NOT NULL,
    producto_liquido ENUM('acholado', 'quebranta', 'italia', 'naranja', 'mistela', 'ciruela', 'perfecto_amor') NOT NULL,
    categoria_producto ENUM('pisco', 'vino') NOT NULL,
    stock_cantidad INT DEFAULT 0,
    stock_minimo INT DEFAULT 100,
    costo_unitario DECIMAL(8,2) DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de configuraciones de embotellado
CREATE TABLE configuracion_embotellado (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre_configuracion VARCHAR(100) NOT NULL,
    capacidad_ml INT NOT NULL,
    botella_id INT NOT NULL,
    tapon_id INT NOT NULL,
    capsula_id INT NULL,
    requiere_etiqueta_atras BOOLEAN DEFAULT FALSE,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (botella_id) REFERENCES botellas(id),
    FOREIGN KEY (tapon_id) REFERENCES tapones(id),
    FOREIGN KEY (capsula_id) REFERENCES capsulas(id)
);

-- Tabla de lotes de embotellado
CREATE TABLE lotes_embotellado (
    id INT PRIMARY KEY AUTO_INCREMENT,
    codigo_lote VARCHAR(50) UNIQUE NOT NULL,
    liquido_id INT NOT NULL,
    configuracion_id INT NOT NULL,
    cantidad_botellas INT NOT NULL,
    ml_por_botella INT NOT NULL,
    ml_total_usado DECIMAL(10,2) NOT NULL,
    fecha_embotellado DATE NOT NULL,
    usuario VARCHAR(100),
    notas TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (liquido_id) REFERENCES liquidos(id),
    FOREIGN KEY (configuracion_id) REFERENCES configuracion_embotellado(id)
);

-- Tabla de movimientos de inventario (auditoría)
CREATE TABLE movimientos_inventario (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tipo_componente ENUM('liquido', 'botella', 'tapon', 'capsula', 'etiqueta') NOT NULL,
    componente_id INT NOT NULL,
    tipo_movimiento ENUM('entrada', 'salida') NOT NULL,
    cantidad_anterior DECIMAL(10,2),
    cantidad_movimiento DECIMAL(10,2) NOT NULL,
    cantidad_nueva DECIMAL(10,2),
    motivo VARCHAR(200),
    lote_embotellado_id INT,
    usuario VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lote_embotellado_id) REFERENCES lotes_embotellado(id)
);

-- =====================================================
-- TABLAS DE VENTAS Y PRESUPUESTO
-- =====================================================

-- Tabla de inversores
CREATE TABLE inversores (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(100) NOT NULL UNIQUE,
    tipo ENUM('persona', 'empresa') NOT NULL,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de presupuesto/inversiones
CREATE TABLE inversiones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    inversor_id INT NOT NULL,
    monto DECIMAL(10,2) NOT NULL,
    concepto VARCHAR(200) NOT NULL,
    fecha_inversion DATE NOT NULL,
    tipo_inversion ENUM('capital_inicial', 'reposicion_stock', 'gastos_operativos', 'otros') NOT NULL,
    notas TEXT,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inversor_id) REFERENCES inversores(id)
);

-- Tabla de gastos por categorías
CREATE TABLE gastos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    categoria ENUM('etiquetas', 'botellas_tapones_capsulas', 'producto_liquido', 'otros') NOT NULL,
    subcategoria VARCHAR(100),
    monto DECIMAL(10,2) NOT NULL,
    cantidad INT,
    precio_unitario DECIMAL(8,2),
    proveedor VARCHAR(200),
    fecha_gasto DATE NOT NULL,
    descripcion TEXT,
    inversor_id INT,
    comprobante VARCHAR(200),
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inversor_id) REFERENCES inversores(id)
);

-- Tabla de ventas
CREATE TABLE ventas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    codigo_venta VARCHAR(50) UNIQUE NOT NULL,
    cliente_nombre VARCHAR(200),
    cliente_telefono VARCHAR(20),
    cliente_email VARCHAR(200),
    fecha_venta DATE NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    descuento DECIMAL(10,2) DEFAULT 0,
    total DECIMAL(10,2) NOT NULL,
    es_regalo BOOLEAN DEFAULT FALSE,
    motivo_regalo VARCHAR(200),
    metodo_pago ENUM('efectivo', 'transferencia', 'tarjeta', 'yape', 'plin', 'otro') DEFAULT 'efectivo',
    estado ENUM('pendiente', 'pagado', 'entregado', 'cancelado') DEFAULT 'pendiente',
    notas TEXT,
    vendedor VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de detalle de ventas
CREATE TABLE detalle_ventas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    venta_id INT NOT NULL,
    lote_embotellado_id INT,
    producto_nombre VARCHAR(200) NOT NULL,
    capacidad_ml INT NOT NULL,
    cantidad INT NOT NULL,
    precio_unitario DECIMAL(8,2) NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    es_regalo BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (venta_id) REFERENCES ventas(id),
    FOREIGN KEY (lote_embotellado_id) REFERENCES lotes_embotellado(id)
);

-- Tabla de stock de productos terminados (después del embotellado)
CREATE TABLE stock_productos_terminados (
    id INT PRIMARY KEY AUTO_INCREMENT,
    lote_embotellado_id INT NOT NULL,
    producto_nombre VARCHAR(200) NOT NULL,
    capacidad_ml INT NOT NULL,
    cantidad_inicial INT NOT NULL,
    cantidad_disponible INT NOT NULL,
    precio_750ml DECIMAL(8,2),
    precio_caja_12 DECIMAL(8,2),
    precio_4000ml DECIMAL(8,2),
    precio_50ml DECIMAL(8,2),
    fecha_embotellado DATE NOT NULL,
    fecha_vencimiento DATE,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (lote_embotellado_id) REFERENCES lotes_embotellado(id)
);

-- =====================================================
-- DATOS INICIALES
-- =====================================================

-- Insertar líquidos base CON PRECIOS
INSERT INTO liquidos (nombre, categoria, descripcion, stock_ml, stock_minimo_ml, precio_750ml, precio_caja_12, precio_4000ml, precio_50ml) VALUES
('Pisco Acholado', 'pisco', 'Pisco Acholado artesanal UVAMAYU', 0, 5000, 23.00, 250.00, 100.00, 2.50),
('Pisco Quebranta', 'pisco', 'Pisco Quebranta artesanal UVAMAYU', 0, 5000, 23.00, 245.00, 100.00, 2.50),
('Pisco Italia', 'pisco', 'Pisco Italia artesanal UVAMAYU', 0, 5000, 30.00, 320.00, 140.00, 3.00),
('Vino Naranja', 'vino', 'Vino de Naranja artesanal UVAMAYU', 0, 3000, 34.00, 355.00, 165.00, 3.50),
('Vino Mistela', 'vino', 'Vino Mistela artesanal UVAMAYU', 0, 3000, 34.00, 355.00, 165.00, 3.50),
('Vino Ciruela', 'vino', 'Vino de Ciruela artesanal UVAMAYU', 0, 3000, 34.00, 355.00, 152.00, 3.50),
('Vino Perfecto Amor', 'vino', 'Vino Perfecto Amor artesanal UVAMAYU', 0, 3000, 22.00, 230.00, 100.00, 2.50);

-- Insertar inversores iniciales
INSERT INTO inversores (nombre, tipo) VALUES
('Sebastian', 'persona'),
('Papa', 'persona'),
('Empresa UVAMAYU', 'empresa');

-- Insertar tipos de botellas (4 tipos: porrón es 4000ml)
INSERT INTO botellas (nombre, capacidad_ml, color, tipo_uso, stock_cantidad, stock_minimo) VALUES
('Botella Transparente 750ml', 750, 'transparente', 'pisco', 0, 100),
('Botella Verde 750ml', 750, 'verde', 'vino', 0, 100),
('Botella Transparente 50ml', 50, 'transparente', 'ambos', 0, 200),
('Porrón 4000ml', 4000, 'transparente', 'ambos', 0, 50);

-- Insertar tipos de tapones
INSERT INTO tapones (nombre, tipo, compatible_con, stock_cantidad, stock_minimo) VALUES
('Corcho Sintético', 'corcho_sintetico', 'Botellas 750ml', 0, 200),
('Tapa Rosca Negra', 'tapa_rosca_negra', 'Botellas 50ml', 0, 300),
('Tapa Porrón', 'tapa_porron', 'Porrón 4000ml', 0, 100);

-- Insertar tipos de cápsulas
INSERT INTO capsulas (nombre, capacidad_botella, color, tipo_producto, stock_cantidad, stock_minimo) VALUES
('Cápsula 750ml Negro', 750, 'negro', 'pisco', 0, 200),
('Cápsula 750ml Dorado', 750, 'dorado', 'vino', 0, 200),
('Cápsula 4000ml Negro', 4000, 'negro', 'pisco', 0, 100),
('Cápsula 4000ml Dorado', 4000, 'dorado', 'vino', 0, 100);

-- Insertar las 28 etiquetas (7 productos × 4 tipos)
INSERT INTO etiquetas (nombre, tipo_etiqueta, producto_liquido, categoria_producto, stock_cantidad, stock_minimo) VALUES
-- Pisco Acholado (4 tipos)
('Etiqueta Porrón Pisco Acholado', 'porron', 'acholado', 'pisco', 0, 50),
('Etiqueta 750ml Adelante Pisco Acholado', 'botella_750ml_adelante', 'acholado', 'pisco', 0, 100),
('Etiqueta 750ml Atrás Pisco Acholado', 'botella_750ml_atras', 'acholado', 'pisco', 0, 100),
('Etiqueta 50ml Regalo Pisco Acholado', 'botella_regalo_50ml', 'acholado', 'pisco', 0, 200),

-- Pisco Quebranta (4 tipos)
('Etiqueta Porrón Pisco Quebranta', 'porron', 'quebranta', 'pisco', 0, 50),
('Etiqueta 750ml Adelante Pisco Quebranta', 'botella_750ml_adelante', 'quebranta', 'pisco', 0, 100),
('Etiqueta 750ml Atrás Pisco Quebranta', 'botella_750ml_atras', 'quebranta', 'pisco', 0, 100),
('Etiqueta 50ml Regalo Pisco Quebranta', 'botella_regalo_50ml', 'quebranta', 'pisco', 0, 200),

-- Pisco Italia (4 tipos)
('Etiqueta Porrón Pisco Italia', 'porron', 'italia', 'pisco', 0, 50),
('Etiqueta 750ml Adelante Pisco Italia', 'botella_750ml_adelante', 'italia', 'pisco', 0, 100),
('Etiqueta 750ml Atrás Pisco Italia', 'botella_750ml_atras', 'italia', 'pisco', 0, 100),
('Etiqueta 50ml Regalo Pisco Italia', 'botella_regalo_50ml', 'italia', 'pisco', 0, 200),

-- Vino Naranja (4 tipos)
('Etiqueta Porrón Vino Naranja', 'porron', 'naranja', 'vino', 0, 50),
('Etiqueta 750ml Adelante Vino Naranja', 'botella_750ml_adelante', 'naranja', 'vino', 0, 100),
('Etiqueta 750ml Atrás Vino Naranja', 'botella_750ml_atras', 'naranja', 'vino', 0, 100),
('Etiqueta 50ml Regalo Vino Naranja', 'botella_regalo_50ml', 'naranja', 'vino', 0, 200),

-- Vino Mistela (4 tipos)
('Etiqueta Porrón Vino Mistela', 'porron', 'mistela', 'vino', 0, 50),
('Etiqueta 750ml Adelante Vino Mistela', 'botella_750ml_adelante', 'mistela', 'vino', 0, 100),
('Etiqueta 750ml Atrás Vino Mistela', 'botella_750ml_atras', 'mistela', 'vino', 0, 100),
('Etiqueta 50ml Regalo Vino Mistela', 'botella_regalo_50ml', 'mistela', 'vino', 0, 200),

-- Vino Ciruela (4 tipos)
('Etiqueta Porrón Vino Ciruela', 'porron', 'ciruela', 'vino', 0, 50),
('Etiqueta 750ml Adelante Vino Ciruela', 'botella_750ml_adelante', 'ciruela', 'vino', 0, 100),
('Etiqueta 750ml Atrás Vino Ciruela', 'botella_750ml_atras', 'ciruela', 'vino', 0, 100),
('Etiqueta 50ml Regalo Vino Ciruela', 'botella_regalo_50ml', 'ciruela', 'vino', 0, 200),

-- Vino Perfecto Amor (4 tipos)
('Etiqueta Porrón Vino Perfecto Amor', 'porron', 'perfecto_amor', 'vino', 0, 50),
('Etiqueta 750ml Adelante Vino Perfecto Amor', 'botella_750ml_adelante', 'perfecto_amor', 'vino', 0, 100),
('Etiqueta 750ml Atrás Vino Perfecto Amor', 'botella_750ml_atras', 'perfecto_amor', 'vino', 0, 100),
('Etiqueta 50ml Regalo Vino Perfecto Amor', 'botella_regalo_50ml', 'perfecto_amor', 'vino', 0, 200);

-- Insertar configuraciones de embotellado
INSERT INTO configuracion_embotellado (nombre_configuracion, capacidad_ml, botella_id, tapon_id, capsula_id, requiere_etiqueta_atras) VALUES
-- Pisco 750ml: Botella transparente + Corcho + Cápsula negra + 2 etiquetas
('Pisco 750ml', 750, 1, 1, 1, TRUE),
-- Vino 750ml: Botella verde + Corcho + Cápsula dorada + 2 etiquetas
('Vino 750ml', 750, 2, 1, 2, TRUE),
-- Regalo 50ml: Botella 50ml + Tapa rosca + Sin cápsula + 1 etiqueta
('Regalo 50ml', 50, 3, 2, NULL, FALSE),
-- Porrón 4000ml: Porrón + Tapa porrón + Cápsula + 1 etiqueta
('Porrón 4000ml', 4000, 4, 3, 3, FALSE);

-- =====================================================
-- TRIGGERS AUTOMÁTICOS
-- =====================================================

-- Trigger para crear productos terminados automáticamente después del embotellado
DELIMITER //

CREATE TRIGGER after_embotellado_insert
AFTER INSERT ON lotes_embotellado
FOR EACH ROW
BEGIN
    DECLARE producto_nombre_var VARCHAR(200);
    DECLARE precio_750_var, precio_caja_var, precio_4000_var, precio_50_var DECIMAL(8,2);

    -- Obtener nombre del producto y precios
    SELECT l.nombre, l.precio_750ml, l.precio_caja_12, l.precio_4000ml, l.precio_50ml
    INTO producto_nombre_var, precio_750_var, precio_caja_var, precio_4000_var, precio_50_var
    FROM liquidos l
    WHERE l.id = NEW.liquido_id;

    -- Insertar en stock de productos terminados
    INSERT INTO stock_productos_terminados (
        lote_embotellado_id, producto_nombre, capacidad_ml,
        cantidad_inicial, cantidad_disponible,
        precio_750ml, precio_caja_12, precio_4000ml, precio_50ml,
        fecha_embotellado
    ) VALUES (
        NEW.id, producto_nombre_var, NEW.ml_por_botella,
        NEW.cantidad_botellas, NEW.cantidad_botellas,
        precio_750_var, precio_caja_var, precio_4000_var, precio_50_var,
        NEW.fecha_embotellado
    );
END//

-- Trigger para descontar stock automáticamente en ventas
CREATE TRIGGER after_venta_insert
AFTER INSERT ON detalle_ventas
FOR EACH ROW
BEGIN
    -- Descontar del stock de productos terminados
    UPDATE stock_productos_terminados
    SET cantidad_disponible = cantidad_disponible - NEW.cantidad
    WHERE lote_embotellado_id = NEW.lote_embotellado_id;
END//

DELIMITER ;

-- =====================================================
-- ÍNDICES PARA OPTIMIZACIÓN
-- =====================================================

CREATE INDEX idx_ventas_fecha ON ventas(fecha_venta);
CREATE INDEX idx_ventas_estado ON ventas(estado);
CREATE INDEX idx_gastos_categoria ON gastos(categoria, fecha_gasto);
CREATE INDEX idx_inversiones_fecha ON inversiones(fecha_inversion);
CREATE INDEX idx_stock_terminados_disponible ON stock_productos_terminados(cantidad_disponible);
CREATE INDEX idx_movimientos_fecha ON movimientos_inventario(created_at);
CREATE INDEX idx_lotes_fecha ON lotes_embotellado(fecha_embotellado);

-- =====================================================
-- VISTA PARA DASHBOARD FINANCIERO
-- =====================================================

CREATE VIEW vista_dashboard_financiero AS
SELECT
    YEAR(CURDATE()) as año_actual,
    MONTH(CURDATE()) as mes_actual,

    -- Inversiones totales
    (SELECT COALESCE(SUM(monto), 0) FROM inversiones WHERE activo = TRUE) as total_inversiones,

    -- Gastos por categoría (mes actual)
    (SELECT COALESCE(SUM(monto), 0) FROM gastos WHERE categoria = 'etiquetas' AND YEAR(fecha_gasto) = YEAR(CURDATE()) AND MONTH(fecha_gasto) = MONTH(CURDATE())) as gastos_etiquetas_mes,
    (SELECT COALESCE(SUM(monto), 0) FROM gastos WHERE categoria = 'botellas_tapones_capsulas' AND YEAR(fecha_gasto) = YEAR(CURDATE()) AND MONTH(fecha_gasto) = MONTH(CURDATE())) as gastos_botellas_mes,
    (SELECT COALESCE(SUM(monto), 0) FROM gastos WHERE categoria = 'producto_liquido' AND YEAR(fecha_gasto) = YEAR(CURDATE()) AND MONTH(fecha_gasto) = MONTH(CURDATE())) as gastos_producto_mes,

    -- Ventas (mes actual)
    (SELECT COALESCE(SUM(total), 0) FROM ventas WHERE YEAR(fecha_venta) = YEAR(CURDATE()) AND MONTH(fecha_venta) = MONTH(CURDATE()) AND es_regalo = FALSE) as ventas_mes,
    (SELECT COALESCE(SUM(total), 0) FROM ventas WHERE YEAR(fecha_venta) = YEAR(CURDATE()) AND MONTH(fecha_venta) = MONTH(CURDATE()) AND es_regalo = TRUE) as regalos_mes,

    -- Stock disponible valorizado
    (SELECT COALESCE(SUM(
        CASE
            WHEN capacidad_ml = 750 THEN cantidad_disponible * precio_750ml
            WHEN capacidad_ml = 4000 THEN cantidad_disponible * precio_4000ml
            WHEN capacidad_ml = 50 THEN cantidad_disponible * precio_50ml
            ELSE cantidad_disponible * precio_750ml
        END
    ), 0) FROM stock_productos_terminados WHERE activo = TRUE) as valor_stock_disponible;

-- =====================================================
-- VISTA PARA ALERTAS DE STOCK
-- =====================================================

CREATE VIEW vista_alertas_stock AS
SELECT
    'liquido' as tipo_componente,
    id as componente_id,
    nombre as nombre_componente,
    stock_ml as stock_actual,
    stock_minimo_ml as stock_minimo,
    ROUND((stock_ml / stock_minimo_ml) * 100, 2) as porcentaje_stock,
    CASE
        WHEN stock_ml = 0 THEN 'critico'
        WHEN stock_ml <= (stock_minimo_ml * 0.5) THEN 'muy_bajo'
        WHEN stock_ml <= stock_minimo_ml THEN 'bajo'
        ELSE 'normal'
    END as nivel_alerta,
    'ml' as unidad
FROM liquidos WHERE activo = TRUE

UNION ALL

SELECT
    'botella' as tipo_componente,
    id as componente_id,
    nombre as nombre_componente,
    stock_cantidad as stock_actual,
    stock_minimo as stock_minimo,
    ROUND((stock_cantidad / stock_minimo) * 100, 2) as porcentaje_stock,
    CASE
        WHEN stock_cantidad = 0 THEN 'critico'
        WHEN stock_cantidad <= (stock_minimo * 0.5) THEN 'muy_bajo'
        WHEN stock_cantidad <= stock_minimo THEN 'bajo'
        ELSE 'normal'
    END as nivel_alerta,
    'unidades' as unidad
FROM botellas WHERE activo = TRUE

UNION ALL

SELECT
    'tapon' as tipo_componente,
    id as componente_id,
    nombre as nombre_componente,
    stock_cantidad as stock_actual,
    stock_minimo as stock_minimo,
    ROUND((stock_cantidad / stock_minimo) * 100, 2) as porcentaje_stock,
    CASE
        WHEN stock_cantidad = 0 THEN 'critico'
        WHEN stock_cantidad <= (stock_minimo * 0.5) THEN 'muy_bajo'
        WHEN stock_cantidad <= stock_minimo THEN 'bajo'
        ELSE 'normal'
    END as nivel_alerta,
    'unidades' as unidad
FROM tapones WHERE activo = TRUE

UNION ALL

SELECT
    'capsula' as tipo_componente,
    id as componente_id,
    nombre as nombre_componente,
    stock_cantidad as stock_actual,
    stock_minimo as stock_minimo,
    ROUND((stock_cantidad / stock_minimo) * 100, 2) as porcentaje_stock,
    CASE
        WHEN stock_cantidad = 0 THEN 'critico'
        WHEN stock_cantidad <= (stock_minimo * 0.5) THEN 'muy_bajo'
        WHEN stock_cantidad <= stock_minimo THEN 'bajo'
        ELSE 'normal'
    END as nivel_alerta,
    'unidades' as unidad
FROM capsulas WHERE activo = TRUE

UNION ALL

SELECT
    'etiqueta' as tipo_componente,
    id as componente_id,
    nombre as nombre_componente,
    stock_cantidad as stock_actual,
    stock_minimo as stock_minimo,
    ROUND((stock_cantidad / stock_minimo) * 100, 2) as porcentaje_stock,
    CASE
        WHEN stock_cantidad = 0 THEN 'critico'
        WHEN stock_cantidad <= (stock_minimo * 0.5) THEN 'muy_bajo'
        WHEN stock_cantidad <= stock_minimo THEN 'bajo'
        ELSE 'normal'
    END as nivel_alerta,
    'unidades' as unidad
FROM etiquetas WHERE activo = TRUE

ORDER BY porcentaje_stock ASC;

-- =====================================================
-- ¡SISTEMA COMPLETO Y CORREGIDO! 🎉
-- =====================================================
