-- =====================================================
-- SISTEMA COMPLETO DE INVENTARIO Y VENTAS UVAMAYU
-- ARCHIVO ÚNICO CORREGIDO
-- =====================================================

-- Eliminar base de datos si existe y crear nueva
DROP DATABASE IF EXISTS uvamayu_inventario;
CREATE DATABASE uvamayu_inventario CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE uvamayu_inventario;

-- =====================================================
-- TABLAS DE INVENTARIO
-- =====================================================

-- Tabla de tipos de líquidos (7 productos base) CON PRECIOS
CREATE TABLE liquidos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(50) NOT NULL UNIQUE,
    categoria ENUM('pisco', 'vino') NOT NULL,
    descripcion TEXT,
    stock_ml DECIMAL(10,2) DEFAULT 0,
    stock_minimo_ml DECIMAL(10,2) DEFAULT 1000,
    costo_por_ml DECIMAL(8,4) DEFAULT 0,
    precio_750ml DECIMAL(8,2) DEFAULT 0,
    precio_caja_12 DECIMAL(8,2) DEFAULT 0,
    precio_4000ml DECIMAL(8,2) DEFAULT 0,
    precio_50ml DECIMAL(8,2) DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de tipos de botellas
CREATE TABLE botellas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(100) NOT NULL,
    capacidad_ml INT NOT NULL,
    color ENUM('transparente', 'verde') NOT NULL,
    tipo_uso ENUM('pisco', 'vino', 'ambos') NOT NULL,
    stock_cantidad INT DEFAULT 0,
    stock_minimo INT DEFAULT 50,
    costo_unitario DECIMAL(8,2) DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de tipos de tapones
CREATE TABLE tapones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(100) NOT NULL,
    tipo ENUM('corcho_sintetico', 'tapa_rosca_negra', 'tapa_porron') NOT NULL,
    compatible_con TEXT,
    stock_cantidad INT DEFAULT 0,
    stock_minimo INT DEFAULT 50,
    costo_unitario DECIMAL(8,2) DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de tipos de cápsulas
CREATE TABLE capsulas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(100) NOT NULL,
    capacidad_botella INT NOT NULL,
    color ENUM('negro', 'dorado') NOT NULL,
    tipo_producto ENUM('pisco', 'vino') NOT NULL,
    stock_cantidad INT DEFAULT 0,
    stock_minimo INT DEFAULT 50,
    costo_unitario DECIMAL(8,2) DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de etiquetas (28 tipos: 7 productos × 4 tipos)
CREATE TABLE etiquetas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(200) NOT NULL,
    tipo_etiqueta ENUM('porron', 'botella_750ml_adelante', 'botella_750ml_atras', 'botella_regalo_50ml') NOT NULL,
    producto_liquido ENUM('acholado', 'quebranta', 'italia', 'naranja', 'mistela', 'ciruela', 'perfecto_amor') NOT NULL,
    categoria_producto ENUM('pisco', 'vino') NOT NULL,
    stock_cantidad INT DEFAULT 0,
    stock_minimo INT DEFAULT 100,
    costo_unitario DECIMAL(8,2) DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de configuraciones de embotellado
CREATE TABLE configuracion_embotellado (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre_configuracion VARCHAR(100) NOT NULL,
    capacidad_ml INT NOT NULL,
    botella_id INT NOT NULL,
    tapon_id INT NOT NULL,
    capsula_id INT NULL,
    requiere_etiqueta_atras BOOLEAN DEFAULT FALSE,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (botella_id) REFERENCES botellas(id),
    FOREIGN KEY (tapon_id) REFERENCES tapones(id),
    FOREIGN KEY (capsula_id) REFERENCES capsulas(id)
);

-- Tabla de lotes de embotellado
CREATE TABLE lotes_embotellado (
    id INT PRIMARY KEY AUTO_INCREMENT,
    codigo_lote VARCHAR(50) UNIQUE NOT NULL,
    liquido_id INT NOT NULL,
    configuracion_id INT NOT NULL,
    cantidad_botellas INT NOT NULL,
    ml_por_botella INT NOT NULL,
    ml_total_usado DECIMAL(10,2) NOT NULL,
    fecha_embotellado DATE NOT NULL,
    usuario VARCHAR(100),
    notas TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (liquido_id) REFERENCES liquidos(id),
    FOREIGN KEY (configuracion_id) REFERENCES configuracion_embotellado(id)
);

-- Tabla de movimientos de inventario (auditoría)
CREATE TABLE movimientos_inventario (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tipo_componente ENUM('liquido', 'botella', 'tapon', 'capsula', 'etiqueta') NOT NULL,
    componente_id INT NOT NULL,
    tipo_movimiento ENUM('entrada', 'salida') NOT NULL,
    cantidad_anterior DECIMAL(10,2),
    cantidad_movimiento DECIMAL(10,2) NOT NULL,
    cantidad_nueva DECIMAL(10,2),
    motivo VARCHAR(200),
    lote_embotellado_id INT,
    usuario VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lote_embotellado_id) REFERENCES lotes_embotellado(id)
);

-- =====================================================
-- TABLAS DE VENTAS Y PRESUPUESTO
-- =====================================================

-- Tabla de inversores
CREATE TABLE inversores (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(100) NOT NULL UNIQUE,
    tipo ENUM('persona', 'empresa') NOT NULL,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de presupuesto/inversiones
CREATE TABLE inversiones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    inversor_id INT NOT NULL,
    monto DECIMAL(10,2) NOT NULL,
    concepto VARCHAR(200) NOT NULL,
    fecha_inversion DATE NOT NULL,
    tipo_inversion ENUM('capital_inicial', 'reposicion_stock', 'gastos_operativos', 'otros') NOT NULL,
    notas TEXT,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inversor_id) REFERENCES inversores(id)
);

-- Tabla de gastos por categorías
CREATE TABLE gastos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    categoria ENUM('etiquetas', 'botellas_tapones_capsulas', 'producto_liquido', 'otros') NOT NULL,
    subcategoria VARCHAR(100),
    monto DECIMAL(10,2) NOT NULL,
    cantidad INT,
    precio_unitario DECIMAL(8,2),
    proveedor VARCHAR(200),
    fecha_gasto DATE NOT NULL,
    descripcion TEXT,
    inversor_id INT,
    comprobante VARCHAR(200),
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inversor_id) REFERENCES inversores(id)
);

-- Tabla de ventas
CREATE TABLE ventas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    codigo_venta VARCHAR(50) UNIQUE NOT NULL,
    cliente_nombre VARCHAR(200),
    cliente_telefono VARCHAR(20),
    cliente_email VARCHAR(200),
    fecha_venta DATE NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    descuento DECIMAL(10,2) DEFAULT 0,
    total DECIMAL(10,2) NOT NULL,
    es_regalo BOOLEAN DEFAULT FALSE,
    motivo_regalo VARCHAR(200),
    metodo_pago ENUM('efectivo', 'transferencia', 'tarjeta', 'yape', 'plin', 'otro') DEFAULT 'efectivo',
    estado ENUM('pendiente', 'pagado', 'entregado', 'cancelado') DEFAULT 'pendiente',
    notas TEXT,
    vendedor VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de detalle de ventas
CREATE TABLE detalle_ventas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    venta_id INT NOT NULL,
    lote_embotellado_id INT,
    producto_nombre VARCHAR(200) NOT NULL,
    capacidad_ml INT NOT NULL,
    cantidad INT NOT NULL,
    precio_unitario DECIMAL(8,2) NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    es_regalo BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (venta_id) REFERENCES ventas(id),
    FOREIGN KEY (lote_embotellado_id) REFERENCES lotes_embotellado(id)
);

-- Tabla de stock de productos terminados (después del embotellado)
CREATE TABLE stock_productos_terminados (
    id INT PRIMARY KEY AUTO_INCREMENT,
    lote_embotellado_id INT NOT NULL,
    producto_nombre VARCHAR(200) NOT NULL,
    capacidad_ml INT NOT NULL,
    cantidad_inicial INT NOT NULL,
    cantidad_disponible INT NOT NULL,
    precio_750ml DECIMAL(8,2),
    precio_caja_12 DECIMAL(8,2),
    precio_4000ml DECIMAL(8,2),
    precio_50ml DECIMAL(8,2),
    fecha_embotellado DATE NOT NULL,
    fecha_vencimiento DATE,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (lote_embotellado_id) REFERENCES lotes_embotellado(id)
);

-- =====================================================
-- DATOS INICIALES
-- =====================================================

-- Insertar líquidos base CON PRECIOS
INSERT INTO liquidos (nombre, categoria, descripcion, stock_ml, stock_minimo_ml, precio_750ml, precio_caja_12, precio_4000ml, precio_50ml) VALUES
('Pisco Acholado', 'pisco', 'Pisco Acholado artesanal UVAMAYU', 0, 5000, 23.00, 250.00, 100.00, 2.50),
('Pisco Quebranta', 'pisco', 'Pisco Quebranta artesanal UVAMAYU', 0, 5000, 23.00, 245.00, 100.00, 2.50),
('Pisco Italia', 'pisco', 'Pisco Italia artesanal UVAMAYU', 0, 5000, 30.00, 320.00, 140.00, 3.00),
('Vino Naranja', 'vino', 'Vino de Naranja artesanal UVAMAYU', 0, 3000, 34.00, 355.00, 165.00, 3.50),
('Vino Mistela', 'vino', 'Vino Mistela artesanal UVAMAYU', 0, 3000, 34.00, 355.00, 165.00, 3.50),
('Vino Ciruela', 'vino', 'Vino de Ciruela artesanal UVAMAYU', 0, 3000, 34.00, 355.00, 152.00, 3.50),
('Vino Perfecto Amor', 'vino', 'Vino Perfecto Amor artesanal UVAMAYU', 0, 3000, 22.00, 230.00, 100.00, 2.50);

-- Insertar inversores iniciales
INSERT INTO inversores (nombre, tipo) VALUES
('Sebastian', 'persona'),
('Papa', 'persona'),
('Empresa UVAMAYU', 'empresa');

-- Insertar tipos de botellas
INSERT INTO botellas (nombre, capacidad_ml, color, tipo_uso, stock_cantidad, stock_minimo) VALUES
('Botella Transparente 750ml', 750, 'transparente', 'pisco', 0, 100),
('Botella Verde 750ml', 750, 'verde', 'vino', 0, 100),
('Botella Transparente 4000ml', 4000, 'transparente', 'ambos', 0, 50),
('Botella Transparente 50ml', 50, 'transparente', 'ambos', 0, 200),
('Porrón', 1000, 'transparente', 'ambos', 0, 50);

-- Insertar tipos de tapones
INSERT INTO tapones (nombre, tipo, compatible_con, stock_cantidad, stock_minimo) VALUES
('Corcho Sintético', 'corcho_sintetico', 'Botellas 750ml y 4000ml', 0, 200),
('Tapa Rosca Negra', 'tapa_rosca_negra', 'Botellas 50ml', 0, 300),
('Tapa Porrón', 'tapa_porron', 'Porrón 1000ml', 0, 100);

-- Insertar tipos de cápsulas
INSERT INTO capsulas (nombre, capacidad_botella, color, tipo_producto, stock_cantidad, stock_minimo) VALUES
('Cápsula 750ml Negro', 750, 'negro', 'pisco', 0, 200),
('Cápsula 750ml Dorado', 750, 'dorado', 'vino', 0, 200),
('Cápsula 4000ml Negro', 4000, 'negro', 'pisco', 0, 100),
('Cápsula 4000ml Dorado', 4000, 'dorado', 'vino', 0, 100);

-- ¡SISTEMA COMPLETO Y CORREGIDO! 🎉
