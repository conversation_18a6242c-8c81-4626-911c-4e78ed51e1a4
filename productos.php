<?php
$page_title = "Productos";
include 'includes/header.php';
?>

<section class="products-hero">
    <div class="container">
        <div class="hero-content">
            <h1 class="page-title">Nuestros Productos 🍇</h1>
            <p class="page-subtitle">Descubre nuestra selección premium de piscos y vinos artesanales</p>
        </div>
    </div>
</section>

<section class="products-section">
    <div class="container">
        <!-- Filtros -->
        <div class="filters-container">
            <h3 class="filters-title">Filtrar por:</h3>
            <div class="filters">
                <button class="filter-btn active" data-filter="all">Todos 📦</button>
                <button class="filter-btn" data-filter="Pisco">Piscos 🥃</button>
                <button class="filter-btn" data-filter="Vino">Vinos 🍷</button>
                <button class="filter-btn" data-filter="750ml">750ml</button>
                <button class="filter-btn" data-filter="100ml">100ml</button>
                <button class="filter-btn" data-filter="3750ml">3750ml</button>
            </div>
        </div>
        
        <!-- Grid de productos -->
        <div class="products-grid" id="products-grid">
            <!-- Los productos se cargarán dinámicamente -->
        </div>
        
        <!-- Loading -->
        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <p>Cargando productos... 🍇</p>
        </div>
        
        <!-- No results -->
        <div class="no-results" id="no-results" style="display: none;">
            <h3>No se encontraron productos 😔</h3>
            <p>Intenta con otros filtros</p>
        </div>
    </div>
</section>

<style>
    .products-hero {
        background: linear-gradient(135deg, var(--gris-oscuro) 0%, var(--negro) 100%);
        padding: 8rem 0 4rem;
        text-align: center;
    }
    
    .page-title {
        font-family: 'Playfair Display', serif;
        font-size: 3rem;
        color: var(--dorado);
        margin-bottom: 1rem;
        animation: fadeInUp 0.6s ease-out;
    }
    
    .page-subtitle {
        font-size: 1.3rem;
        color: #ccc;
        max-width: 600px;
        margin: 0 auto;
        animation: fadeInUp 0.6s ease-out 0.2s both;
    }
    
    .products-section {
        padding: 4rem 0;
        background: var(--negro);
    }
    
    .filters-container {
        margin-bottom: 3rem;
        text-align: center;
        animation: fadeInUp 0.6s ease-out 0.4s both;
    }
    
    .filters-title {
        color: var(--dorado);
        margin-bottom: 1rem;
        font-size: 1.2rem;
    }
    
    .filters {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }
    
    .filter-btn {
        background: var(--gris-medio);
        color: var(--dorado);
        border: 2px solid transparent;
        padding: 0.8rem 1.5rem;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
    }
    
    .filter-btn:hover {
        background: var(--guinda);
        transform: translateY(-2px);
    }
    
    .filter-btn.active {
        background: var(--dorado);
        color: var(--negro);
        border-color: var(--dorado);
    }
    
    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 2rem;
        animation: fadeInUp 0.6s ease-out 0.6s both;
    }
    
    .product-card {
        background: var(--gris-medio);
        border-radius: 15px;
        overflow: hidden;
        transition: all 0.3s ease;
        position: relative;
        animation: fadeInUp 0.6s ease-out;
    }
    
    .product-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(205, 170, 88, 0.3);
    }
    
    .product-image {
        width: 100%;
        height: 280px;
        background: linear-gradient(135deg, var(--gris-oscuro) 0%, var(--negro) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--dorado);
        font-size: 1.2rem;
        position: relative;
        overflow: hidden;
    }
    
    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .product-card:hover .product-image img {
        transform: scale(1.1);
    }
    
    .product-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: var(--guinda);
        color: white;
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .product-info {
        padding: 2rem;
    }
    
    .product-category {
        color: var(--verde);
        font-size: 0.9rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
    
    .product-name {
        font-size: 1.4rem;
        font-weight: bold;
        color: var(--dorado);
        margin-bottom: 0.8rem;
        line-height: 1.3;
    }
    
    .product-description {
        color: #ccc;
        margin-bottom: 1rem;
        font-size: 0.95rem;
        line-height: 1.5;
    }
    
    .product-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
        margin-bottom: 1.5rem;
        font-size: 0.9rem;
    }
    
    .product-detail {
        color: #aaa;
    }
    
    .product-detail strong {
        color: var(--dorado);
    }
    
    .product-pricing {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .product-price {
        font-size: 1.8rem;
        font-weight: bold;
        color: var(--guinda);
    }
    
    .product-price-caja {
        font-size: 1rem;
        color: var(--verde);
    }
    
    .product-actions {
        display: flex;
        gap: 1rem;
    }
    
    .btn-contact {
        flex: 1;
        background: linear-gradient(45deg, var(--dorado), var(--guinda));
        color: var(--negro);
        text-decoration: none;
        padding: 0.8rem 1.5rem;
        border-radius: 8px;
        text-align: center;
        font-weight: bold;
        transition: all 0.3s ease;
        text-transform: uppercase;
        font-size: 0.9rem;
    }
    
    .btn-contact:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(205, 170, 88, 0.4);
    }
    
    .loading {
        text-align: center;
        padding: 3rem;
        color: var(--dorado);
    }
    
    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid var(--gris-medio);
        border-top: 3px solid var(--dorado);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .no-results {
        text-align: center;
        padding: 3rem;
        color: var(--dorado);
    }
    
    @media (max-width: 768px) {
        .page-title {
            font-size: 2.5rem;
        }
        
        .filters {
            justify-content: center;
        }
        
        .filter-btn {
            padding: 0.6rem 1rem;
            font-size: 0.9rem;
        }
        
        .products-grid {
            grid-template-columns: 1fr;
        }
        
        .product-details {
            grid-template-columns: 1fr;
        }
        
        .product-actions {
            flex-direction: column;
        }
    }
</style>

<script>
let allProducts = [];
let currentFilter = 'all';

document.addEventListener('DOMContentLoaded', function() {
    loadProducts();
    initFilters();
});

async function loadProducts() {
    try {
        const response = await fetch('/productos.json');
        const data = await response.json();
        allProducts = data.productos.filter(p => p.activo);
        
        document.getElementById('loading').style.display = 'none';
        displayProducts(allProducts);
    } catch (error) {
        console.error('Error cargando productos:', error);
        document.getElementById('loading').innerHTML = '<p>Error cargando productos 😔</p>';
    }
}

function displayProducts(products) {
    const grid = document.getElementById('products-grid');
    const noResults = document.getElementById('no-results');
    
    if (products.length === 0) {
        grid.style.display = 'none';
        noResults.style.display = 'block';
        return;
    }
    
    grid.style.display = 'grid';
    noResults.style.display = 'none';
    
    grid.innerHTML = products.map(product => `
        <div class="product-card" data-category="${product.categoria}" data-size="${product.tamanio}">
            ${product.destacado ? '<div class="product-badge">⭐ Destacado</div>' : ''}
            <div class="product-image image-container">
                <div class="image-placeholder">
                    <div class="placeholder-icon">🍇</div>
                </div>
                <img class="lazy-image" data-src="${product.imagen_principal}" alt="${product.nombre} - ${product.categoria} artesanal peruano UVAMAYU"
                     data-width="300" data-height="280">
            </div>
            <div class="product-info">
                <div class="product-category">${product.categoria} • ${product.tipo}</div>
                <h3 class="product-name">${product.nombre}</h3>
                <p class="product-description">${product.descripcion}</p>
                
                <div class="product-details">
                    <div class="product-detail"><strong>Graduación:</strong> ${product.graduacion}</div>
                    <div class="product-detail"><strong>Aroma:</strong> ${product.aroma}</div>
                    <div class="product-detail"><strong>Maridaje:</strong> ${product.maridaje}</div>
                    <div class="product-detail"><strong>Stock:</strong> ${product.stock} unidades</div>
                </div>
                
                <div class="product-pricing">
                    <div>
                        <div class="product-price">S/ ${product.precio_unitario.toFixed(2)}</div>
                        ${product.precio_caja ? `<div class="product-price-caja">Caja: S/ ${product.precio_caja.toFixed(2)}</div>` : ''}
                    </div>
                </div>
                
                <div class="product-actions">
                    <a href="/contacto.php?producto=${encodeURIComponent(product.nombre)}" class="btn-contact">
                        💬 Consultar
                    </a>
                </div>
            </div>
        </div>
    `).join('');

    // Reinicializar lazy loading para las nuevas imágenes
    if (window.uvamayuLoader) {
        window.uvamayuLoader.setupLazyLoading();
    }
}

function initFilters() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Update active button
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Filter products
            const filter = this.dataset.filter;
            currentFilter = filter;
            
            let filteredProducts = allProducts;
            
            if (filter !== 'all') {
                filteredProducts = allProducts.filter(product => {
                    return product.categoria === filter || product.tamanio === filter;
                });
            }
            
            displayProducts(filteredProducts);
        });
    });
}
</script>

<?php include 'includes/footer.php'; ?>
