<?php
require 'conexion.php';
include 'header.php';
?>
<main style="max-width:1200px;margin:120px auto 40px auto;padding:0 20px;animation:fadeIn 1.2s;">
  <h1 class="text-center">Nuestros Productos</h1>
  <form method="get" style="display:flex;gap:16px;flex-wrap:wrap;justify-content:center;margin:32px 0;">
    <select name="categoria" style="padding:8px 16px;">
      <option value="">Todos</option>
      <option value="Pisco" <?php if(@$_GET['categoria']==='Pisco') echo 'selected';?>>Pisco</option>
      <option value="Vino" <?php if(@$_GET['categoria']==='Vino') echo 'selected';?>>Vino</option>
    </select>
    <select name="tamanio" style="padding:8px 16px;">
      <option value="">Tamaño</option>
      <option value="100" <?php if(@$_GET['tamanio']==='100') echo 'selected';?>>100 ml</option>
      <option value="750" <?php if(@$_GET['tamanio']==='750') echo 'selected';?>>750 ml</option>
      <option value="3750" <?php if(@$_GET['tamanio']==='3750') echo 'selected';?>>3750 ml</option>
    </select>
    <select name="por_mayor" style="padding:8px 16px;">
      <option value="">Venta</option>
      <option value="unidad" <?php if(@$_GET['por_mayor']==='unidad') echo 'selected';?>>Por unidad</option>
      <option value="caja" <?php if(@$_GET['por_mayor']==='caja') echo 'selected';?>>Por caja (12)</option>
    </select>
    <input type="number" name="precio_min" placeholder="Precio mínimo" value="<?php echo @$_GET['precio_min']; ?>" style="padding:8px 16px;width:120px;">
    <input type="number" name="precio_max" placeholder="Precio máximo" value="<?php echo @$_GET['precio_max']; ?>" style="padding:8px 16px;width:120px;">
    <button class="btn" type="submit">Filtrar</button>
  </form>
  <div style="display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:30px;margin-top:40px;">
    <?php
    // Construir consulta dinámica
    $where = [];
    $params = [];
    if (!empty($_GET['categoria'])) {
      $where[] = 'categoria = :categoria';
      $params[':categoria'] = $_GET['categoria'];
    }
    if (!empty($_GET['tamanio'])) {
      $where[] = 'tamanio = :tamanio';
      $params[':tamanio'] = $_GET['tamanio'];
    }
    if (!empty($_GET['precio_min'])) {
      $where[] = 'precio_unitario >= :precio_min';
      $params[':precio_min'] = $_GET['precio_min'];
    }
    if (!empty($_GET['precio_max'])) {
      $where[] = 'precio_unitario <= :precio_max';
      $params[':precio_max'] = $_GET['precio_max'];
    }
    $sql = 'SELECT * FROM productos WHERE activo = 1';
    if ($where) {
      $sql .= ' AND ' . implode(' AND ', $where);
    }
    $sql .= ' ORDER BY destacado DESC, nombre ASC';
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($productos as $prod):
    ?>
    <div class="producto-card animate-fade-in"
         data-producto-id="<?php echo $prod['id']; ?>"
         data-producto-nombre="<?php echo htmlspecialchars($prod['nombre']); ?>"
         data-producto-precio="<?php echo $prod['precio_unitario']; ?>"
         data-producto-imagen="<?php echo $prod['imagen_principal']; ?>"
         style="background:var(--blanco-puro);border-radius:20px;box-shadow:0 8px 30px rgba(136, 27, 53, 0.1);padding:30px;display:flex;flex-direction:column;align-items:center;transition:all 0.3s ease;border:2px solid transparent;">
      <img src="<?php echo $prod['imagen_principal']; ?>" alt="<?php echo htmlspecialchars($prod['nombre']); ?>" style="width:140px;height:240px;object-fit:contain;margin-bottom:20px;filter:drop-shadow(0 0 15px rgba(161, 122, 50, 0.3));">
      <h3 style="margin:0 0 10px 0;color:var(--vino-borgona);font-size:1.3em;text-align:center;"><?php echo htmlspecialchars($prod['nombre']); ?></h3>
      <div style="color:var(--oro-dorado);font-weight:bold;margin-bottom:10px;font-size:1.1em;">
        <?php echo $prod['categoria']; ?> | <?php echo $prod['tamanio']; ?> ml
      </div>
      <div style="margin-bottom:10px;font-size:1.2em;color:var(--vino-borgona);">
        <strong>S/ <?php echo number_format($prod['precio_unitario'],2); ?></strong>
        <span style="color:#888;font-size:0.9em;">(unidad)</span>
      </div>
      <div style="margin-bottom:20px;text-align:center;">
        <span style="color:var(--verde-hoja);font-size:0.95em;">Caja x12: </span>
        <span style="color:var(--vino-borgona);font-weight:bold;">
          <?php
            if (!is_null($prod['precio_caja'])) {
              echo 'S/ ' . number_format($prod['precio_caja'], 2);
            } else {
              echo 'Consultar';
            }
          ?>
        </span>
      </div>
      <a href="producto.php?id=<?php echo $prod['id']; ?>" class="btn" style="margin-top:auto;">Ver detalles</a>
    </div>
    <?php endforeach; ?>
    <?php if (empty($productos)): ?>
      <div style="grid-column:1/-1;text-align:center;color:var(--vino-borgona);font-size:1.3em;padding:60px 20px;">
        <h3>No se encontraron productos con esos filtros</h3>
        <p style="color:var(--verde-hoja);margin-top:15px;">Intenta ajustar los filtros o <a href="productos.php" style="color:var(--oro-dorado);">ver todos los productos</a></p>
      </div>
    <?php endif; ?>
  </div>
</main>
<?php include 'footer.php'; ?> 