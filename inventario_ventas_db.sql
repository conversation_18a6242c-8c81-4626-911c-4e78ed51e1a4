-- =====================================================
-- ACTUALIZACIÓN: SISTEMA DE VENTAS Y PRESUPUESTO UVAMAYU
-- =====================================================

-- Los precios ya están incluidos en la tabla liquidos del archivo inventario_db.sql
-- No es necesario crear tabla productos separada

-- Tabla de inversores
CREATE TABLE inversores (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(100) NOT NULL UNIQUE,
    tipo ENUM('persona', 'empresa') NOT NULL,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insertar inversores iniciales
INSERT INTO inversores (nombre, tipo) VALUES
('Sebastian', 'persona'),
('Papa', 'persona'),
('Empresa UVAMAYU', 'empresa');

-- Tabla de presupuesto/inversiones
CREATE TABLE inversiones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    inversor_id INT NOT NULL,
    monto DECIMAL(10,2) NOT NULL,
    concepto VARCHAR(200) NOT NULL,
    fecha_inversion DATE NOT NULL,
    tipo_inversion ENUM('capital_inicial', 'reposicion_stock', 'gastos_operativos', 'otros') NOT NULL,
    notas TEXT,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inversor_id) REFERENCES inversores(id)
);

-- Tabla de gastos por categorías
CREATE TABLE gastos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    categoria ENUM('etiquetas', 'botellas_tapones_capsulas', 'producto_liquido', 'otros') NOT NULL,
    subcategoria VARCHAR(100), -- Ej: "Etiquetas Pisco Quebranta", "Botellas 750ml"
    monto DECIMAL(10,2) NOT NULL,
    cantidad INT, -- Cantidad de items comprados
    precio_unitario DECIMAL(8,2), -- Precio por unidad
    proveedor VARCHAR(200),
    fecha_gasto DATE NOT NULL,
    descripcion TEXT,
    inversor_id INT, -- Quién pagó este gasto
    comprobante VARCHAR(200), -- Número de factura/boleta
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inversor_id) REFERENCES inversores(id)
);

-- Tabla de ventas
CREATE TABLE ventas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    codigo_venta VARCHAR(50) UNIQUE NOT NULL,
    cliente_nombre VARCHAR(200),
    cliente_telefono VARCHAR(20),
    cliente_email VARCHAR(200),
    fecha_venta DATE NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    descuento DECIMAL(10,2) DEFAULT 0,
    total DECIMAL(10,2) NOT NULL,
    es_regalo BOOLEAN DEFAULT FALSE,
    motivo_regalo VARCHAR(200), -- "Promoción", "Cliente frecuente", etc.
    metodo_pago ENUM('efectivo', 'transferencia', 'tarjeta', 'yape', 'plin', 'otro') DEFAULT 'efectivo',
    estado ENUM('pendiente', 'pagado', 'entregado', 'cancelado') DEFAULT 'pendiente',
    notas TEXT,
    vendedor VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de detalle de ventas
CREATE TABLE detalle_ventas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    venta_id INT NOT NULL,
    lote_embotellado_id INT, -- Referencia al lote de donde sale el producto
    producto_nombre VARCHAR(200) NOT NULL, -- Nombre del producto vendido
    capacidad_ml INT NOT NULL, -- 750, 4000, 50, 1000
    cantidad INT NOT NULL,
    precio_unitario DECIMAL(8,2) NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    es_regalo BOOLEAN DEFAULT FALSE, -- Puede ser regalo individual
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (venta_id) REFERENCES ventas(id),
    FOREIGN KEY (lote_embotellado_id) REFERENCES lotes_embotellado(id)
);

-- Tabla de stock de productos terminados (después del embotellado)
CREATE TABLE stock_productos_terminados (
    id INT PRIMARY KEY AUTO_INCREMENT,
    lote_embotellado_id INT NOT NULL,
    producto_nombre VARCHAR(200) NOT NULL,
    capacidad_ml INT NOT NULL,
    cantidad_inicial INT NOT NULL,
    cantidad_disponible INT NOT NULL,
    precio_750ml DECIMAL(8,2),
    precio_caja_12 DECIMAL(8,2),
    precio_4000ml DECIMAL(8,2),
    precio_50ml DECIMAL(8,2),
    fecha_embotellado DATE NOT NULL,
    fecha_vencimiento DATE, -- Si aplica
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (lote_embotellado_id) REFERENCES lotes_embotellado(id)
);

-- Tabla de resumen financiero (vista consolidada)
CREATE TABLE resumen_financiero (
    id INT PRIMARY KEY AUTO_INCREMENT,
    periodo_mes INT NOT NULL, -- 1-12
    periodo_año INT NOT NULL,
    total_inversiones DECIMAL(10,2) DEFAULT 0,
    total_gastos_etiquetas DECIMAL(10,2) DEFAULT 0,
    total_gastos_botellas_tapones DECIMAL(10,2) DEFAULT 0,
    total_gastos_producto DECIMAL(10,2) DEFAULT 0,
    total_gastos_otros DECIMAL(10,2) DEFAULT 0,
    total_ventas DECIMAL(10,2) DEFAULT 0,
    total_regalos DECIMAL(10,2) DEFAULT 0,
    utilidad_bruta DECIMAL(10,2) DEFAULT 0,
    saldo_disponible DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_periodo (periodo_mes, periodo_año)
);

-- Triggers para actualizar stock de productos terminados
DELIMITER //

CREATE TRIGGER after_embotellado_insert 
AFTER INSERT ON lotes_embotellado
FOR EACH ROW
BEGIN
    DECLARE producto_nombre_var VARCHAR(200);
    DECLARE precio_750_var, precio_caja_var, precio_4000_var, precio_50_var DECIMAL(8,2);
    
    -- Obtener nombre del producto y precios
    SELECT l.nombre, l.precio_750ml, l.precio_caja_12, l.precio_4000ml, l.precio_50ml
    INTO producto_nombre_var, precio_750_var, precio_caja_var, precio_4000_var, precio_50_var
    FROM liquidos l
    WHERE l.id = NEW.liquido_id;
    
    -- Insertar en stock de productos terminados
    INSERT INTO stock_productos_terminados (
        lote_embotellado_id, producto_nombre, capacidad_ml, 
        cantidad_inicial, cantidad_disponible,
        precio_750ml, precio_caja_12, precio_4000ml, precio_50ml,
        fecha_embotellado
    ) VALUES (
        NEW.id, producto_nombre_var, NEW.ml_por_botella,
        NEW.cantidad_botellas, NEW.cantidad_botellas,
        precio_750_var, precio_caja_var, precio_4000_var, precio_50_var,
        NEW.fecha_embotellado
    );
END//

CREATE TRIGGER after_venta_insert
AFTER INSERT ON detalle_ventas
FOR EACH ROW
BEGIN
    -- Descontar del stock de productos terminados
    UPDATE stock_productos_terminados 
    SET cantidad_disponible = cantidad_disponible - NEW.cantidad
    WHERE lote_embotellado_id = NEW.lote_embotellado_id;
END//

DELIMITER ;

-- Índices para optimización
CREATE INDEX idx_ventas_fecha ON ventas(fecha_venta);
CREATE INDEX idx_ventas_estado ON ventas(estado);
CREATE INDEX idx_gastos_categoria ON gastos(categoria, fecha_gasto);
CREATE INDEX idx_inversiones_fecha ON inversiones(fecha_inversion);
CREATE INDEX idx_stock_terminados_disponible ON stock_productos_terminados(cantidad_disponible);

-- Vista para dashboard financiero
CREATE VIEW vista_dashboard_financiero AS
SELECT 
    YEAR(CURDATE()) as año_actual,
    MONTH(CURDATE()) as mes_actual,
    
    -- Inversiones totales
    (SELECT COALESCE(SUM(monto), 0) FROM inversiones WHERE activo = TRUE) as total_inversiones,
    
    -- Gastos por categoría (mes actual)
    (SELECT COALESCE(SUM(monto), 0) FROM gastos WHERE categoria = 'etiquetas' AND YEAR(fecha_gasto) = YEAR(CURDATE()) AND MONTH(fecha_gasto) = MONTH(CURDATE())) as gastos_etiquetas_mes,
    (SELECT COALESCE(SUM(monto), 0) FROM gastos WHERE categoria = 'botellas_tapones_capsulas' AND YEAR(fecha_gasto) = YEAR(CURDATE()) AND MONTH(fecha_gasto) = MONTH(CURDATE())) as gastos_botellas_mes,
    (SELECT COALESCE(SUM(monto), 0) FROM gastos WHERE categoria = 'producto_liquido' AND YEAR(fecha_gasto) = YEAR(CURDATE()) AND MONTH(fecha_gasto) = MONTH(CURDATE())) as gastos_producto_mes,
    
    -- Ventas (mes actual)
    (SELECT COALESCE(SUM(total), 0) FROM ventas WHERE YEAR(fecha_venta) = YEAR(CURDATE()) AND MONTH(fecha_venta) = MONTH(CURDATE()) AND es_regalo = FALSE) as ventas_mes,
    (SELECT COALESCE(SUM(total), 0) FROM ventas WHERE YEAR(fecha_venta) = YEAR(CURDATE()) AND MONTH(fecha_venta) = MONTH(CURDATE()) AND es_regalo = TRUE) as regalos_mes,
    
    -- Stock disponible valorizado
    (SELECT COALESCE(SUM(
        CASE 
            WHEN capacidad_ml = 750 THEN cantidad_disponible * precio_750ml
            WHEN capacidad_ml = 4000 THEN cantidad_disponible * precio_4000ml
            WHEN capacidad_ml = 50 THEN cantidad_disponible * precio_50ml
            ELSE cantidad_disponible * precio_750ml
        END
    ), 0) FROM stock_productos_terminados WHERE activo = TRUE) as valor_stock_disponible;
