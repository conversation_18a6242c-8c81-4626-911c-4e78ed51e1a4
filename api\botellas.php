<?php
/**
 * API para gestión de botellas
 * Endpoints: GET, POST, PUT, DELETE /api/botellas.php
 */

require_once 'config/Database.php';
require_once 'config/ApiResponse.php';

// Headers CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $path_info = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
    $id = $path_info ? (int)ltrim($path_info, '/') : null;
    
    switch ($method) {
        case 'GET':
            if ($id) {
                getBotella($db, $id);
            } else {
                getBotellas($db);
            }
            break;
            
        case 'POST':
            createBotella($db);
            break;
            
        case 'PUT':
            if (!$id) {
                ApiResponse::error("ID requerido para actualizar", 400);
            }
            updateBotella($db, $id);
            break;
            
        case 'DELETE':
            if (!$id) {
                ApiResponse::error("ID requerido para eliminar", 400);
            }
            deleteBotella($db, $id);
            break;
            
        default:
            ApiResponse::error("Método no permitido", 405);
    }
    
} catch (Exception $e) {
    error_log("Error en API botellas: " . $e->getMessage());
    ApiResponse::serverError("Error interno del servidor");
}

function getBotellas($db) {
    try {
        $query = "SELECT * FROM botellas ORDER BY capacidad_ml, color, nombre";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $botellas = $stmt->fetchAll();
        
        foreach ($botellas as &$botella) {
            $botella['stock_status'] = $botella['stock_cantidad'] <= $botella['stock_minimo'] ? 'bajo' : 'normal';
            $botella['stock_cantidad'] = (int)$botella['stock_cantidad'];
            $botella['stock_minimo'] = (int)$botella['stock_minimo'];
            $botella['capacidad_ml'] = (int)$botella['capacidad_ml'];
            $botella['costo_unitario'] = (float)$botella['costo_unitario'];
        }
        
        ApiResponse::success($botellas, "Botellas obtenidas exitosamente");
        
    } catch (Exception $e) {
        error_log("Error obteniendo botellas: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

function getBotella($db, $id) {
    try {
        $query = "SELECT * FROM botellas WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        
        $botella = $stmt->fetch();
        
        if (!$botella) {
            ApiResponse::notFound("Botella no encontrada");
        }
        
        $botella['stock_cantidad'] = (int)$botella['stock_cantidad'];
        $botella['stock_minimo'] = (int)$botella['stock_minimo'];
        $botella['capacidad_ml'] = (int)$botella['capacidad_ml'];
        $botella['costo_unitario'] = (float)$botella['costo_unitario'];
        $botella['stock_status'] = $botella['stock_cantidad'] <= $botella['stock_minimo'] ? 'bajo' : 'normal';
        
        ApiResponse::success($botella, "Botella obtenida exitosamente");
        
    } catch (Exception $e) {
        error_log("Error obteniendo botella: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

function createBotella($db) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        ApiResponse::validateRequired($data, ['nombre', 'capacidad_ml', 'color', 'tipo_uso']);
        
        // Validaciones específicas
        if (!in_array($data['color'], ['transparente', 'verde'])) {
            ApiResponse::validationError(['color' => 'Color debe ser transparente o verde']);
        }
        
        if (!in_array($data['tipo_uso'], ['pisco', 'vino', 'ambos'])) {
            ApiResponse::validationError(['tipo_uso' => 'Tipo de uso debe ser pisco, vino o ambos']);
        }
        
        if (!is_numeric($data['capacidad_ml']) || $data['capacidad_ml'] <= 0) {
            ApiResponse::validationError(['capacidad_ml' => 'Capacidad debe ser un número positivo']);
        }
        
        $query = "INSERT INTO botellas (nombre, capacidad_ml, color, tipo_uso, stock_cantidad, stock_minimo, costo_unitario, activo) 
                  VALUES (:nombre, :capacidad_ml, :color, :tipo_uso, :stock_cantidad, :stock_minimo, :costo_unitario, :activo)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':nombre', $data['nombre']);
        $stmt->bindParam(':capacidad_ml', $data['capacidad_ml'], PDO::PARAM_INT);
        $stmt->bindParam(':color', $data['color']);
        $stmt->bindParam(':tipo_uso', $data['tipo_uso']);
        $stmt->bindParam(':stock_cantidad', $data['stock_cantidad'] ?? 0, PDO::PARAM_INT);
        $stmt->bindParam(':stock_minimo', $data['stock_minimo'] ?? 50, PDO::PARAM_INT);
        $stmt->bindParam(':costo_unitario', $data['costo_unitario'] ?? 0);
        $stmt->bindParam(':activo', $data['activo'] ?? true, PDO::PARAM_BOOL);
        
        if ($stmt->execute()) {
            $newId = $db->lastInsertId();
            
            // Registrar movimiento inicial
            if (($data['stock_cantidad'] ?? 0) > 0) {
                registrarMovimiento($db, 'botella', $newId, 'entrada', 0, $data['stock_cantidad'], $data['stock_cantidad'], 'Creación inicial');
            }
            
            ApiResponse::success(['id' => $newId], "Botella creada exitosamente", 201);
        } else {
            ApiResponse::serverError("Error al crear la botella");
        }
        
    } catch (Exception $e) {
        error_log("Error creando botella: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

function updateBotella($db, $id) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        // Verificar que existe y obtener stock actual
        $checkQuery = "SELECT stock_cantidad FROM botellas WHERE id = :id";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->bindParam(':id', $id, PDO::PARAM_INT);
        $checkStmt->execute();
        $current = $checkStmt->fetch();
        
        if (!$current) {
            ApiResponse::notFound("Botella no encontrada");
        }
        
        $fields = [];
        $params = [':id' => $id];
        
        $allowedFields = ['nombre', 'capacidad_ml', 'color', 'tipo_uso', 'stock_cantidad', 'stock_minimo', 'costo_unitario', 'activo'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = :$field";
                $params[":$field"] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            ApiResponse::error("No hay campos para actualizar", 400);
        }
        
        $query = "UPDATE botellas SET " . implode(', ', $fields) . " WHERE id = :id";
        $stmt = $db->prepare($query);
        
        if ($stmt->execute($params)) {
            // Registrar movimiento si cambió el stock
            if (isset($data['stock_cantidad']) && $data['stock_cantidad'] != $current['stock_cantidad']) {
                $diferencia = $data['stock_cantidad'] - $current['stock_cantidad'];
                $tipoMovimiento = $diferencia > 0 ? 'entrada' : 'salida';
                registrarMovimiento($db, 'botella', $id, $tipoMovimiento, $current['stock_cantidad'], abs($diferencia), $data['stock_cantidad'], 'Ajuste manual');
            }
            
            ApiResponse::success(null, "Botella actualizada exitosamente");
        } else {
            ApiResponse::serverError("Error al actualizar la botella");
        }
        
    } catch (Exception $e) {
        error_log("Error actualizando botella: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

function deleteBotella($db, $id) {
    try {
        $query = "UPDATE botellas SET activo = FALSE WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        
        if ($stmt->execute() && $stmt->rowCount() > 0) {
            ApiResponse::success(null, "Botella eliminada exitosamente");
        } else {
            ApiResponse::notFound("Botella no encontrada");
        }
        
    } catch (Exception $e) {
        error_log("Error eliminando botella: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

function registrarMovimiento($db, $tipoComponente, $componenteId, $tipoMovimiento, $cantidadAnterior, $cantidadMovimiento, $cantidadNueva, $motivo) {
    try {
        $query = "INSERT INTO movimientos_inventario (tipo_componente, componente_id, tipo_movimiento, cantidad_anterior, cantidad_movimiento, cantidad_nueva, motivo) 
                  VALUES (:tipo_componente, :componente_id, :tipo_movimiento, :cantidad_anterior, :cantidad_movimiento, :cantidad_nueva, :motivo)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':tipo_componente', $tipoComponente);
        $stmt->bindParam(':componente_id', $componenteId);
        $stmt->bindParam(':tipo_movimiento', $tipoMovimiento);
        $stmt->bindParam(':cantidad_anterior', $cantidadAnterior);
        $stmt->bindParam(':cantidad_movimiento', $cantidadMovimiento);
        $stmt->bindParam(':cantidad_nueva', $cantidadNueva);
        $stmt->bindParam(':motivo', $motivo);
        
        $stmt->execute();
    } catch (Exception $e) {
        error_log("Error registrando movimiento: " . $e->getMessage());
    }
}
?>
