<?php
/**
 * API para gestión de etiquetas
 */

require_once 'config/Database.php';
require_once 'config/ApiResponse.php';

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $path_info = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
    $id = $path_info ? (int)ltrim($path_info, '/') : null;
    
    switch ($method) {
        case 'GET':
            if ($id) {
                getEtiqueta($db, $id);
            } else {
                getEtiquetas($db);
            }
            break;
        case 'POST':
            createEtiqueta($db);
            break;
        case 'PUT':
            if (!$id) ApiResponse::error("ID requerido", 400);
            updateEtiqueta($db, $id);
            break;
        case 'DELETE':
            if (!$id) ApiResponse::error("ID requerido", 400);
            deleteEtiqueta($db, $id);
            break;
        default:
            ApiResponse::error("Método no permitido", 405);
    }
    
} catch (Exception $e) {
    error_log("Error en API etiquetas: " . $e->getMessage());
    ApiResponse::serverError();
}

function getEtiquetas($db) {
    try {
        $filtros = [];
        $params = [];
        
        // Filtros opcionales
        if (isset($_GET['categoria_producto'])) {
            $filtros[] = "categoria_producto = :categoria_producto";
            $params[':categoria_producto'] = $_GET['categoria_producto'];
        }
        
        if (isset($_GET['producto_liquido'])) {
            $filtros[] = "producto_liquido = :producto_liquido";
            $params[':producto_liquido'] = $_GET['producto_liquido'];
        }
        
        if (isset($_GET['tipo_etiqueta'])) {
            $filtros[] = "tipo_etiqueta = :tipo_etiqueta";
            $params[':tipo_etiqueta'] = $_GET['tipo_etiqueta'];
        }
        
        $whereClause = !empty($filtros) ? 'WHERE ' . implode(' AND ', $filtros) : '';
        
        $query = "SELECT * FROM etiquetas $whereClause ORDER BY categoria_producto, producto_liquido, tipo_etiqueta";
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        
        $etiquetas = $stmt->fetchAll();
        
        foreach ($etiquetas as &$etiqueta) {
            $etiqueta['stock_status'] = $etiqueta['stock_cantidad'] <= $etiqueta['stock_minimo'] ? 'bajo' : 'normal';
            $etiqueta['stock_cantidad'] = (int)$etiqueta['stock_cantidad'];
            $etiqueta['stock_minimo'] = (int)$etiqueta['stock_minimo'];
            $etiqueta['costo_unitario'] = (float)$etiqueta['costo_unitario'];
        }
        
        ApiResponse::success($etiquetas);
    } catch (Exception $e) {
        ApiResponse::serverError();
    }
}

function getEtiqueta($db, $id) {
    try {
        $query = "SELECT * FROM etiquetas WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        
        $etiqueta = $stmt->fetch();
        if (!$etiqueta) ApiResponse::notFound();
        
        $etiqueta['stock_cantidad'] = (int)$etiqueta['stock_cantidad'];
        $etiqueta['stock_minimo'] = (int)$etiqueta['stock_minimo'];
        $etiqueta['costo_unitario'] = (float)$etiqueta['costo_unitario'];
        $etiqueta['stock_status'] = $etiqueta['stock_cantidad'] <= $etiqueta['stock_minimo'] ? 'bajo' : 'normal';
        
        ApiResponse::success($etiqueta);
    } catch (Exception $e) {
        ApiResponse::serverError();
    }
}

function createEtiqueta($db) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        ApiResponse::validateRequired($data, ['nombre', 'tipo_etiqueta', 'producto_liquido', 'categoria_producto']);
        
        $tiposValidos = ['porron', 'botella_750ml_adelante', 'botella_750ml_atras', 'botella_regalo_50ml'];
        if (!in_array($data['tipo_etiqueta'], $tiposValidos)) {
            ApiResponse::validationError(['tipo_etiqueta' => 'Tipo de etiqueta inválido']);
        }
        
        if (!in_array($data['categoria_producto'], ['pisco', 'vino'])) {
            ApiResponse::validationError(['categoria_producto' => 'Categoría debe ser pisco o vino']);
        }
        
        $productosValidos = ['acholado', 'quebranta', 'italia', 'naranja', 'mistela', 'ciruela', 'perfecto_amor'];
        if (!in_array($data['producto_liquido'], $productosValidos)) {
            ApiResponse::validationError(['producto_liquido' => 'Producto líquido inválido']);
        }
        
        $query = "INSERT INTO etiquetas (nombre, tipo_etiqueta, producto_liquido, categoria_producto, stock_cantidad, stock_minimo, costo_unitario, activo) 
                  VALUES (:nombre, :tipo_etiqueta, :producto_liquido, :categoria_producto, :stock_cantidad, :stock_minimo, :costo_unitario, :activo)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':nombre', $data['nombre']);
        $stmt->bindParam(':tipo_etiqueta', $data['tipo_etiqueta']);
        $stmt->bindParam(':producto_liquido', $data['producto_liquido']);
        $stmt->bindParam(':categoria_producto', $data['categoria_producto']);
        $stmt->bindParam(':stock_cantidad', $data['stock_cantidad'] ?? 0, PDO::PARAM_INT);
        $stmt->bindParam(':stock_minimo', $data['stock_minimo'] ?? 200, PDO::PARAM_INT);
        $stmt->bindParam(':costo_unitario', $data['costo_unitario'] ?? 0);
        $stmt->bindParam(':activo', $data['activo'] ?? true, PDO::PARAM_BOOL);
        
        if ($stmt->execute()) {
            $newId = $db->lastInsertId();
            if (($data['stock_cantidad'] ?? 0) > 0) {
                registrarMovimiento($db, 'etiqueta', $newId, 'entrada', 0, $data['stock_cantidad'], $data['stock_cantidad'], 'Creación inicial');
            }
            ApiResponse::success(['id' => $newId], "Etiqueta creada exitosamente", 201);
        } else {
            ApiResponse::serverError();
        }
    } catch (Exception $e) {
        ApiResponse::serverError();
    }
}

function updateEtiqueta($db, $id) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        $checkQuery = "SELECT stock_cantidad FROM etiquetas WHERE id = :id";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->bindParam(':id', $id, PDO::PARAM_INT);
        $checkStmt->execute();
        $current = $checkStmt->fetch();
        
        if (!$current) ApiResponse::notFound();
        
        $fields = [];
        $params = [':id' => $id];
        
        $allowedFields = ['nombre', 'tipo_etiqueta', 'producto_liquido', 'categoria_producto', 'stock_cantidad', 'stock_minimo', 'costo_unitario', 'activo'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = :$field";
                $params[":$field"] = $data[$field];
            }
        }
        
        if (empty($fields)) ApiResponse::error("No hay campos para actualizar", 400);
        
        $query = "UPDATE etiquetas SET " . implode(', ', $fields) . " WHERE id = :id";
        $stmt = $db->prepare($query);
        
        if ($stmt->execute($params)) {
            if (isset($data['stock_cantidad']) && $data['stock_cantidad'] != $current['stock_cantidad']) {
                $diferencia = $data['stock_cantidad'] - $current['stock_cantidad'];
                $tipoMovimiento = $diferencia > 0 ? 'entrada' : 'salida';
                registrarMovimiento($db, 'etiqueta', $id, $tipoMovimiento, $current['stock_cantidad'], abs($diferencia), $data['stock_cantidad'], 'Ajuste manual');
            }
            ApiResponse::success(null, "Etiqueta actualizada exitosamente");
        } else {
            ApiResponse::serverError();
        }
    } catch (Exception $e) {
        ApiResponse::serverError();
    }
}

function deleteEtiqueta($db, $id) {
    try {
        $query = "UPDATE etiquetas SET activo = FALSE WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        
        if ($stmt->execute() && $stmt->rowCount() > 0) {
            ApiResponse::success(null, "Etiqueta eliminada exitosamente");
        } else {
            ApiResponse::notFound();
        }
    } catch (Exception $e) {
        ApiResponse::serverError();
    }
}

function registrarMovimiento($db, $tipoComponente, $componenteId, $tipoMovimiento, $cantidadAnterior, $cantidadMovimiento, $cantidadNueva, $motivo) {
    try {
        $query = "INSERT INTO movimientos_inventario (tipo_componente, componente_id, tipo_movimiento, cantidad_anterior, cantidad_movimiento, cantidad_nueva, motivo) 
                  VALUES (:tipo_componente, :componente_id, :tipo_movimiento, :cantidad_anterior, :cantidad_movimiento, :cantidad_nueva, :motivo)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':tipo_componente', $tipoComponente);
        $stmt->bindParam(':componente_id', $componenteId);
        $stmt->bindParam(':tipo_movimiento', $tipoMovimiento);
        $stmt->bindParam(':cantidad_anterior', $cantidadAnterior);
        $stmt->bindParam(':cantidad_movimiento', $cantidadMovimiento);
        $stmt->bindParam(':cantidad_nueva', $cantidadNueva);
        $stmt->bindParam(':motivo', $motivo);
        
        $stmt->execute();
    } catch (Exception $e) {
        error_log("Error registrando movimiento: " . $e->getMessage());
    }
}
?>
