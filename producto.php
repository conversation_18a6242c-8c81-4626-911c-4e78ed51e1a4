<?php
require 'conexion.php';
include 'header.php';
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$stmt = $pdo->prepare('SELECT * FROM productos WHERE id = ? AND activo = 1');
$stmt->execute([$id]);
$prod = $stmt->fetch(PDO::FETCH_ASSOC);
if (!$prod) {
  echo '<main style="padding:120px 20px 64px 20px;text-align:center;"><h2 style="color:var(--vino-borgona);">Producto no encontrado</h2><p><a href="productos.php" class="btn">Ver todos los productos</a></p></main>';
  include 'footer.php';
  exit;
}
// Imágenes adicionales
$stmt2 = $pdo->prepare('SELECT * FROM imagenes_producto WHERE producto_id = ?');
$stmt2->execute([$id]);
$imagenes = $stmt2->fetchAll(PDO::FETCH_ASSOC);
?>
<main style="max-width:1000px;margin:120px auto 40px auto;padding:0 20px;animation:fadeIn 1.2s;">
  <div style="display:flex;flex-wrap:wrap;gap:50px;align-items:flex-start;background:var(--blanco-puro);border-radius:20px;box-shadow:0 10px 40px rgba(136, 27, 53, 0.1);padding:40px;margin-bottom:40px;">
    <div style="flex:1;min-width:300px;text-align:center;">
      <div style="position:relative;display:inline-block;">
        <img src="<?php echo $prod['imagen_principal']; ?>"
             alt="<?php echo htmlspecialchars($prod['nombre']); ?>"
             style="width:100%;max-width:380px;object-fit:contain;filter:drop-shadow(0 0 20px rgba(161, 122, 50, 0.4));border-radius:15px;">
        <?php if ($prod['destacado']): ?>
          <div style="position:absolute;top:10px;right:10px;background:var(--oro-dorado);color:var(--vino-borgona);padding:8px 15px;border-radius:20px;font-weight:bold;font-size:0.9em;">⭐ Destacado</div>
        <?php endif; ?>
      </div>
      <?php if ($imagenes): ?>
        <div style="display:flex;gap:10px;margin-top:20px;justify-content:center;flex-wrap:wrap;">
          <?php foreach ($imagenes as $img): ?>
            <img src="<?php echo $img['ruta_imagen']; ?>"
                 alt="<?php echo htmlspecialchars($img['descripcion']); ?>"
                 style="width:70px;height:90px;object-fit:cover;border-radius:10px;border:3px solid var(--oro-dorado);cursor:pointer;transition:all 0.3s ease;"
                 onmouseover="this.style.transform='scale(1.1)'"
                 onmouseout="this.style.transform='scale(1)'">
          <?php endforeach; ?>
        </div>
      <?php endif; ?>
    </div>
    <div style="flex:2;min-width:300px;">
      <h1 style="color:var(--vino-borgona);font-size:2.5em;margin-bottom:15px;line-height:1.2;"><?php echo htmlspecialchars($prod['nombre']); ?></h1>
      <div style="color:var(--oro-dorado);font-weight:bold;margin-bottom:20px;font-size:1.3em;padding:10px 0;border-bottom:2px solid var(--gris-suave);">
        <?php echo $prod['categoria']; ?> | <?php echo $prod['tamanio']; ?> ml
      </div>

      <div style="display:flex;gap:30px;margin-bottom:25px;flex-wrap:wrap;">
        <div style="background:linear-gradient(135deg, var(--vino-borgona), #9a1e3d);color:var(--blanco-puro);padding:20px;border-radius:15px;text-align:center;min-width:150px;">
          <div style="font-size:0.9em;opacity:0.9;margin-bottom:5px;">Precio Unitario</div>
          <div style="font-size:1.8em;font-weight:bold;">S/ <?php echo number_format($prod['precio_unitario'],2); ?></div>
        </div>
        <div style="background:linear-gradient(135deg, var(--oro-dorado), #b8864a);color:var(--vino-borgona);padding:20px;border-radius:15px;text-align:center;min-width:150px;">
          <div style="font-size:0.9em;opacity:0.8;margin-bottom:5px;">Caja x12</div>
          <div style="font-size:1.5em;font-weight:bold;">
            <?php
              if (!is_null($prod['precio_caja'])) {
                echo 'S/ ' . number_format($prod['precio_caja'], 2);
              } else {
                echo 'Consultar';
              }
            ?>
          </div>
        </div>
      </div>

      <div style="background:var(--gris-suave);padding:25px;border-radius:15px;margin-bottom:25px;">
        <h3 style="color:var(--vino-borgona);margin-top:0;margin-bottom:15px;">Descripción</h3>
        <p style="line-height:1.6;color:var(--verde-hoja);font-size:1.1em;margin:0;"> <?php echo nl2br(htmlspecialchars($prod['descripcion'])); ?> </p>
      </div>

      <div style="background:var(--blanco-puro);border:2px solid var(--oro-dorado);padding:25px;border-radius:15px;margin-bottom:30px;">
        <h3 style="color:var(--vino-borgona);margin-top:0;margin-bottom:20px;">Características</h3>
        <ul style="color:var(--verde-hoja);font-size:1.1em;line-height:1.8;list-style:none;padding:0;">
          <li style="margin-bottom:10px;"><span style="color:var(--oro-dorado);font-weight:bold;">🍇 Varietal:</span> <?php echo htmlspecialchars($prod['varietal']); ?></li>
          <li style="margin-bottom:10px;"><span style="color:var(--oro-dorado);font-weight:bold;">🌸 Aroma:</span> <?php echo htmlspecialchars($prod['aroma']); ?></li>
          <li style="margin-bottom:10px;"><span style="color:var(--oro-dorado);font-weight:bold;">🍽️ Maridaje:</span> <?php echo htmlspecialchars($prod['maridaje']); ?></li>
          <li style="margin-bottom:10px;"><span style="color:var(--oro-dorado);font-weight:bold;">🌡️ Graduación:</span> <?php echo htmlspecialchars($prod['graduacion']); ?></li>
        </ul>
      </div>

      <!-- Formulario de compra -->
      <div style="background:linear-gradient(135deg, var(--gris-suave), #f0f0f0);padding:30px;border-radius:15px;margin-bottom:30px;">
        <h3 style="color:var(--vino-borgona);margin-top:0;margin-bottom:20px;">Agregar al carrito</h3>
        <form id="carritoForm" style="display:flex;align-items:center;gap:15px;flex-wrap:wrap;">
          <label for="cantidad" style="color:var(--verde-hoja);font-weight:bold;">Cantidad:</label>
          <input type="number" id="cantidad" name="cantidad" value="1" min="1" max="<?php echo $prod['stock']; ?>"
                 style="width:80px;padding:12px 15px;border:2px solid var(--oro-dorado);border-radius:8px;font-size:1.1em;text-align:center;">
          <button type="button" class="btn premium-glow"
                  data-producto-id="<?php echo $prod['id']; ?>"
                  data-producto-nombre="<?php echo htmlspecialchars($prod['nombre']); ?>"
                  data-producto-precio="<?php echo $prod['precio_unitario']; ?>"
                  data-producto-imagen="<?php echo $prod['imagen_principal']; ?>"
                  onclick="agregarAlCarrito(<?php echo $prod['id']; ?>)">
            🛒 Añadir al carrito
          </button>
        </form>
        <div id="carritoMsg" style="margin-top:15px;color:var(--verde-hoja);font-weight:bold;"></div>
      </div>

      <!-- Botones de compra directa -->
      <div style="display:flex;gap:20px;flex-wrap:wrap;">
        <a href="mailto:<?php echo $config['correo_contacto']; ?>?subject=Pedido%20de%20<?php echo urlencode($prod['nombre']); ?>&body=Quiero%20comprar%20<?php echo $prod['nombre']; ?>%20x%20"
           id="correoBtn" class="btn" style="flex:1;min-width:200px;text-align:center;">
          📧 Comprar por correo
        </a>
        <a href="https://wa.me/<?php echo preg_replace('/\D/','',$config['whatsapp']); ?>?text=Quiero%20comprar%20<?php echo urlencode($prod['nombre']); ?>%20x%20"
           id="wspBtn" class="btn bg-dorado" style="flex:1;min-width:200px;text-align:center;">
          📱 Comprar por WhatsApp
        </a>
      </div>
    </div>
  </div>
</main>
<script>
// Función mejorada para agregar al carrito
function agregarAlCarrito(id) {
  const cantidad = document.getElementById('cantidad').value;
  const nombre = <?php echo json_encode($prod['nombre']); ?>;
  const precio = <?php echo $prod['precio_unitario']; ?>;
  const imagen = <?php echo json_encode($prod['imagen_principal']); ?>;

  // Usar el sistema de carrito global si está disponible
  if (typeof carritoUVAMAYU !== 'undefined') {
    const productoData = {
      id: id,
      nombre: nombre,
      precio: precio,
      imagen: imagen
    };
    carritoUVAMAYU.agregarProducto(id, cantidad, productoData);
  } else {
    // Fallback al sistema anterior
    let carrito = JSON.parse(localStorage.getItem('carrito') || '{}');
    carrito[id] = (carrito[id] || 0) + parseInt(cantidad);
    localStorage.setItem('carrito', JSON.stringify(carrito));
    document.getElementById('carritoMsg').innerText = '✅ Producto añadido al carrito';
  }

  // Actualizar links de compra
  document.getElementById('correoBtn').href = `mailto:<?php echo $config['correo_contacto']; ?>?subject=Pedido%20de%20${encodeURIComponent(nombre)}&body=Quiero%20comprar%20${encodeURIComponent(nombre)}%20x%20${cantidad}`;
  document.getElementById('wspBtn').href = `https://wa.me/<?php echo preg_replace('/\D/','',$config['whatsapp']); ?>?text=Quiero%20comprar%20${encodeURIComponent(nombre)}%20x%20${cantidad}`;
}

// Animaciones de entrada
document.addEventListener('DOMContentLoaded', function() {
  // Efecto parallax suave en la imagen principal
  const mainImage = document.querySelector('img[alt*="<?php echo htmlspecialchars($prod['nombre']); ?>"]');
  if (mainImage) {
    mainImage.addEventListener('mousemove', function(e) {
      const rect = this.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const rotateX = (y - centerY) / 20;
      const rotateY = (centerX - x) / 20;

      this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(1.05)`;
    });

    mainImage.addEventListener('mouseleave', function() {
      this.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1)';
    });
  }

  // Animación de entrada para elementos
  const elements = document.querySelectorAll('h1, h3, p, ul, form, div[style*="background"]');
  elements.forEach((el, index) => {
    el.style.opacity = '0';
    el.style.transform = 'translateY(20px)';
    el.style.transition = 'all 0.6s ease-out';

    setTimeout(() => {
      el.style.opacity = '1';
      el.style.transform = 'translateY(0)';
    }, index * 100);
  });
});
</script>
<?php include 'footer.php'; ?> 