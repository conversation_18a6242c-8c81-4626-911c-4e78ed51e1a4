-- =====================================================
-- SISTEMA DE INVENTARIO Y CONTROL DE PRODUCCIÓN UVAMAYU
-- =====================================================

-- Tabla de tipos de líquidos (7 productos base)
CREATE TABLE liquidos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(50) NOT NULL UNIQUE,
    categoria ENUM('pisco', 'vino') NOT NULL,
    descripcion TEXT,
    stock_ml DECIMAL(10,2) DEFAULT 0,
    stock_minimo_ml DECIMAL(10,2) DEFAULT 1000,
    costo_por_ml DECIMAL(8,4) DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de tipos de botellas
CREATE TABLE botellas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(100) NOT NULL,
    capacidad_ml INT NOT NULL,
    color ENUM('transparente', 'verde') NOT NULL,
    tipo_uso ENUM('pisco', 'vino', 'ambos') NOT NULL,
    stock_cantidad INT DEFAULT 0,
    stock_minimo INT DEFAULT 50,
    costo_unitario DECIMAL(8,2) DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de tipos de tapones
CREATE TABLE tapones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(100) NOT NULL,
    tipo ENUM('corcho_sintetico', 'tapa_rosca_negra', 'tapa_porron') NOT NULL,
    compatible_con VARCHAR(200), -- Descripción de con qué botellas es compatible
    stock_cantidad INT DEFAULT 0,
    stock_minimo INT DEFAULT 100,
    costo_unitario DECIMAL(8,2) DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de cápsulas de termo sellado
CREATE TABLE capsulas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(100) NOT NULL,
    capacidad_botella INT NOT NULL, -- 750ml o 4000ml
    color ENUM('negro', 'dorado') NOT NULL,
    tipo_producto ENUM('pisco', 'vino') NOT NULL,
    stock_cantidad INT DEFAULT 0,
    stock_minimo INT DEFAULT 100,
    costo_unitario DECIMAL(8,2) DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de etiquetas (28 tipos: 7 productos x 4 tipos de etiqueta)
CREATE TABLE etiquetas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(100) NOT NULL,
    tipo_etiqueta ENUM('porron', 'botella_750ml_adelante', 'botella_750ml_atras', 'botella_regalo_50ml') NOT NULL,
    producto_liquido VARCHAR(50) NOT NULL, -- acholado, quebranta, italia, naranja, mistela, ciruela, perfecto_amor
    categoria_producto ENUM('pisco', 'vino') NOT NULL,
    stock_cantidad INT DEFAULT 0,
    stock_minimo INT DEFAULT 200,
    costo_unitario DECIMAL(8,2) DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de configuración de embotellado (qué componentes necesita cada tipo de producto)
CREATE TABLE configuracion_embotellado (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre_configuracion VARCHAR(100) NOT NULL,
    capacidad_ml INT NOT NULL,
    categoria_producto ENUM('pisco', 'vino') NOT NULL,
    botella_id INT NOT NULL,
    tapon_id INT NOT NULL,
    capsula_id INT NOT NULL,
    requiere_etiqueta_adelante BOOLEAN DEFAULT TRUE,
    requiere_etiqueta_atras BOOLEAN DEFAULT FALSE,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (botella_id) REFERENCES botellas(id),
    FOREIGN KEY (tapon_id) REFERENCES tapones(id),
    FOREIGN KEY (capsula_id) REFERENCES capsulas(id)
);

-- Tabla de movimientos de inventario (para auditoría)
CREATE TABLE movimientos_inventario (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tipo_componente ENUM('liquido', 'botella', 'tapon', 'capsula', 'etiqueta') NOT NULL,
    componente_id INT NOT NULL,
    tipo_movimiento ENUM('entrada', 'salida', 'ajuste', 'embotellado') NOT NULL,
    cantidad_anterior DECIMAL(10,2),
    cantidad_movimiento DECIMAL(10,2) NOT NULL,
    cantidad_nueva DECIMAL(10,2),
    motivo VARCHAR(200),
    usuario VARCHAR(100),
    referencia_embotellado INT NULL, -- Si es por embotellado, referencia al lote
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de lotes de embotellado
CREATE TABLE lotes_embotellado (
    id INT PRIMARY KEY AUTO_INCREMENT,
    codigo_lote VARCHAR(50) UNIQUE NOT NULL,
    liquido_id INT NOT NULL,
    configuracion_id INT NOT NULL,
    cantidad_botellas INT NOT NULL,
    ml_por_botella INT NOT NULL,
    ml_total_usado DECIMAL(10,2) NOT NULL,
    fecha_embotellado DATE NOT NULL,
    usuario_embotellado VARCHAR(100),
    notas TEXT,
    estado ENUM('planificado', 'en_proceso', 'completado', 'cancelado') DEFAULT 'planificado',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (liquido_id) REFERENCES liquidos(id),
    FOREIGN KEY (configuracion_id) REFERENCES configuracion_embotellado(id)
);

-- Tabla de alertas de stock bajo
CREATE TABLE alertas_stock (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tipo_componente ENUM('liquido', 'botella', 'tapon', 'capsula', 'etiqueta') NOT NULL,
    componente_id INT NOT NULL,
    nombre_componente VARCHAR(200) NOT NULL,
    stock_actual DECIMAL(10,2) NOT NULL,
    stock_minimo DECIMAL(10,2) NOT NULL,
    estado ENUM('activa', 'resuelta', 'ignorada') DEFAULT 'activa',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL
);

-- =====================================================
-- DATOS INICIALES
-- =====================================================

-- Insertar líquidos base
INSERT INTO liquidos (nombre, categoria, descripcion, stock_ml, stock_minimo_ml) VALUES
('Pisco Acholado', 'pisco', 'Pisco Acholado artesanal UVAMAYU', 0, 5000),
('Pisco Quebranta', 'pisco', 'Pisco Quebranta artesanal UVAMAYU', 0, 5000),
('Pisco Italia', 'pisco', 'Pisco Italia artesanal UVAMAYU', 0, 5000),
('Vino Naranja', 'vino', 'Vino de Naranja artesanal UVAMAYU', 0, 3000),
('Vino Mistela', 'vino', 'Vino Mistela artesanal UVAMAYU', 0, 3000),
('Vino Ciruela', 'vino', 'Vino de Ciruela artesanal UVAMAYU', 0, 3000),
('Vino Perfecto Amor', 'vino', 'Vino Perfecto Amor artesanal UVAMAYU', 0, 3000);

-- Insertar tipos de botellas
INSERT INTO botellas (nombre, capacidad_ml, color, tipo_uso, stock_cantidad, stock_minimo) VALUES
('Botella Transparente 750ml', 750, 'transparente', 'pisco', 0, 100),
('Botella Verde 750ml', 750, 'verde', 'vino', 0, 100),
('Botella Transparente 4000ml', 4000, 'transparente', 'ambos', 0, 50),
('Botella Transparente 50ml', 50, 'transparente', 'ambos', 0, 200),
('Porrón', 1000, 'transparente', 'ambos', 0, 30);

-- Insertar tipos de tapones
INSERT INTO tapones (nombre, tipo, compatible_con, stock_cantidad, stock_minimo) VALUES
('Corcho Sintético', 'corcho_sintetico', 'Botellas 750ml y 4000ml', 0, 200),
('Tapa Rosca Negra', 'tapa_rosca_negra', 'Botellas 50ml', 0, 300),
('Tapa Porrón', 'tapa_porron', 'Porrón', 0, 50);

-- Insertar cápsulas
INSERT INTO capsulas (nombre, capacidad_botella, color, tipo_producto, stock_cantidad, stock_minimo) VALUES
('Cápsula 750ml Negro', 750, 'negro', 'pisco', 0, 150),
('Cápsula 750ml Dorado', 750, 'dorado', 'vino', 0, 150),
('Cápsula 4000ml Negro', 4000, 'negro', 'pisco', 0, 75),
('Cápsula 4000ml Dorado', 4000, 'dorado', 'vino', 0, 75);

-- Insertar etiquetas (28 tipos: 7 productos x 4 tipos)
-- Etiquetas para Porrón
INSERT INTO etiquetas (nombre, tipo_etiqueta, producto_liquido, categoria_producto, stock_cantidad, stock_minimo) VALUES
('Etiqueta Porrón Pisco Acholado', 'porron', 'acholado', 'pisco', 0, 50),
('Etiqueta Porrón Pisco Quebranta', 'porron', 'quebranta', 'pisco', 0, 50),
('Etiqueta Porrón Pisco Italia', 'porron', 'italia', 'pisco', 0, 50),
('Etiqueta Porrón Vino Naranja', 'porron', 'naranja', 'vino', 0, 50),
('Etiqueta Porrón Vino Mistela', 'porron', 'mistela', 'vino', 0, 50),
('Etiqueta Porrón Vino Ciruela', 'porron', 'ciruela', 'vino', 0, 50),
('Etiqueta Porrón Vino Perfecto Amor', 'porron', 'perfecto_amor', 'vino', 0, 50);

-- Etiquetas para Botella 750ml Adelante
INSERT INTO etiquetas (nombre, tipo_etiqueta, producto_liquido, categoria_producto, stock_cantidad, stock_minimo) VALUES
('Etiqueta 750ml Adelante Pisco Acholado', 'botella_750ml_adelante', 'acholado', 'pisco', 0, 200),
('Etiqueta 750ml Adelante Pisco Quebranta', 'botella_750ml_adelante', 'quebranta', 'pisco', 0, 200),
('Etiqueta 750ml Adelante Pisco Italia', 'botella_750ml_adelante', 'italia', 'pisco', 0, 200),
('Etiqueta 750ml Adelante Vino Naranja', 'botella_750ml_adelante', 'naranja', 'vino', 0, 200),
('Etiqueta 750ml Adelante Vino Mistela', 'botella_750ml_adelante', 'mistela', 'vino', 0, 200),
('Etiqueta 750ml Adelante Vino Ciruela', 'botella_750ml_adelante', 'ciruela', 'vino', 0, 200),
('Etiqueta 750ml Adelante Vino Perfecto Amor', 'botella_750ml_adelante', 'perfecto_amor', 'vino', 0, 200);

-- Etiquetas para Botella 750ml Atrás
INSERT INTO etiquetas (nombre, tipo_etiqueta, producto_liquido, categoria_producto, stock_cantidad, stock_minimo) VALUES
('Etiqueta 750ml Atrás Pisco Acholado', 'botella_750ml_atras', 'acholado', 'pisco', 0, 200),
('Etiqueta 750ml Atrás Pisco Quebranta', 'botella_750ml_atras', 'quebranta', 'pisco', 0, 200),
('Etiqueta 750ml Atrás Pisco Italia', 'botella_750ml_atras', 'italia', 'pisco', 0, 200),
('Etiqueta 750ml Atrás Vino Naranja', 'botella_750ml_atras', 'naranja', 'vino', 0, 200),
('Etiqueta 750ml Atrás Vino Mistela', 'botella_750ml_atras', 'mistela', 'vino', 0, 200),
('Etiqueta 750ml Atrás Vino Ciruela', 'botella_750ml_atras', 'ciruela', 'vino', 0, 200),
('Etiqueta 750ml Atrás Vino Perfecto Amor', 'botella_750ml_atras', 'perfecto_amor', 'vino', 0, 200);

-- Etiquetas para Botella Regalo 50ml
INSERT INTO etiquetas (nombre, tipo_etiqueta, producto_liquido, categoria_producto, stock_cantidad, stock_minimo) VALUES
('Etiqueta 50ml Regalo Pisco Acholado', 'botella_regalo_50ml', 'acholado', 'pisco', 0, 100),
('Etiqueta 50ml Regalo Pisco Quebranta', 'botella_regalo_50ml', 'quebranta', 'pisco', 0, 100),
('Etiqueta 50ml Regalo Pisco Italia', 'botella_regalo_50ml', 'italia', 'pisco', 0, 100),
('Etiqueta 50ml Regalo Vino Naranja', 'botella_regalo_50ml', 'naranja', 'vino', 0, 100),
('Etiqueta 50ml Regalo Vino Mistela', 'botella_regalo_50ml', 'mistela', 'vino', 0, 100),
('Etiqueta 50ml Regalo Vino Ciruela', 'botella_regalo_50ml', 'ciruela', 'vino', 0, 100),
('Etiqueta 50ml Regalo Vino Perfecto Amor', 'botella_regalo_50ml', 'perfecto_amor', 'vino', 0, 100);

-- Configuraciones de embotellado
INSERT INTO configuracion_embotellado (nombre_configuracion, capacidad_ml, categoria_producto, botella_id, tapon_id, capsula_id, requiere_etiqueta_adelante, requiere_etiqueta_atras) VALUES
-- Configuraciones para Pisco 750ml
('Pisco 750ml', 750, 'pisco', 1, 1, 1, TRUE, TRUE),
-- Configuraciones para Vino 750ml
('Vino 750ml', 750, 'vino', 2, 1, 2, TRUE, TRUE),
-- Configuraciones para Pisco 4000ml
('Pisco 4000ml', 4000, 'pisco', 3, 1, 3, TRUE, FALSE),
-- Configuraciones para Vino 4000ml
('Vino 4000ml', 4000, 'vino', 3, 1, 4, TRUE, FALSE),
-- Configuraciones para Regalo 50ml (ambos usan tapa rosca)
('Regalo Pisco 50ml', 50, 'pisco', 4, 2, NULL, TRUE, FALSE),
('Regalo Vino 50ml', 50, 'vino', 4, 2, NULL, TRUE, FALSE),
-- Configuraciones para Porrón
('Porrón Pisco', 1000, 'pisco', 5, 3, NULL, TRUE, FALSE),
('Porrón Vino', 1000, 'vino', 5, 3, NULL, TRUE, FALSE);

-- Índices para optimizar consultas
CREATE INDEX idx_movimientos_componente ON movimientos_inventario(tipo_componente, componente_id);
CREATE INDEX idx_movimientos_fecha ON movimientos_inventario(created_at);
CREATE INDEX idx_lotes_fecha ON lotes_embotellado(fecha_embotellado);
CREATE INDEX idx_lotes_estado ON lotes_embotellado(estado);
CREATE INDEX idx_alertas_estado ON alertas_stock(estado);
CREATE INDEX idx_etiquetas_producto ON etiquetas(producto_liquido, tipo_etiqueta);
