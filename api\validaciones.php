<?php
/**
 * API para validaciones del sistema de inventario
 */

require_once 'config/Database.php';
require_once 'config/ApiResponse.php';

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $path_info = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
    
    // Extraer acción de la URL
    $action = '';
    if ($path_info) {
        $parts = explode('/', trim($path_info, '/'));
        $action = $parts[0] ?? '';
    }
    
    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'disponibilidad-embotellado':
                    validarDisponibilidadEmbotellado($db);
                    break;
                case 'stock-minimo':
                    validarStockMinimo($db);
                    break;
                case 'consistencia':
                    validarConsistencia($db);
                    break;
                default:
                    ApiResponse::error("Validación no válida. Disponibles: disponibilidad-embotellado, stock-minimo, consistencia", 400);
            }
            break;
            
        case 'POST':
            switch ($action) {
                case 'simular-embotellado':
                    simularEmbotellado($db);
                    break;
                case 'verificar-componentes':
                    verificarComponentes($db);
                    break;
                default:
                    ApiResponse::error("Acción no válida", 400);
            }
            break;
            
        default:
            ApiResponse::error("Método no permitido", 405);
    }
    
} catch (Exception $e) {
    error_log("Error en API validaciones: " . $e->getMessage());
    ApiResponse::serverError();
}

/**
 * Validar disponibilidad para embotellado
 */
function validarDisponibilidadEmbotellado($db) {
    try {
        $liquidoId = $_GET['liquido_id'] ?? null;
        $configuracionId = $_GET['configuracion_id'] ?? null;
        $cantidadBotellas = (int)($_GET['cantidad_botellas'] ?? 0);
        
        if (!$liquidoId || !$configuracionId || $cantidadBotellas <= 0) {
            ApiResponse::error("Parámetros requeridos: liquido_id, configuracion_id, cantidad_botellas", 400);
        }
        
        $validacion = validarComponentesEmbotellado($db, $liquidoId, $configuracionId, $cantidadBotellas);
        
        ApiResponse::success($validacion, "Validación de disponibilidad completada");
        
    } catch (Exception $e) {
        error_log("Error validando disponibilidad: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Validar stock mínimo de todos los componentes
 */
function validarStockMinimo($db) {
    try {
        $alertas = [];
        
        // Validar líquidos
        $queryLiquidos = "SELECT 'liquido' as tipo, id, nombre, stock_ml as stock_actual, stock_minimo_ml as stock_minimo,
                                 ROUND((stock_ml / stock_minimo_ml) * 100, 2) as porcentaje_stock,
                                 CASE 
                                     WHEN stock_ml = 0 THEN 'critico'
                                     WHEN stock_ml <= (stock_minimo_ml * 0.5) THEN 'muy_bajo'
                                     WHEN stock_ml <= stock_minimo_ml THEN 'bajo'
                                     ELSE 'normal'
                                 END as nivel_alerta
                          FROM liquidos 
                          WHERE activo = TRUE
                          ORDER BY porcentaje_stock ASC";
        
        $stmt = $db->prepare($queryLiquidos);
        $stmt->execute();
        $alertas['liquidos'] = $stmt->fetchAll();
        
        // Validar botellas
        $queryBotellas = "SELECT 'botella' as tipo, id, nombre, stock_cantidad as stock_actual, stock_minimo,
                                 ROUND((stock_cantidad / stock_minimo) * 100, 2) as porcentaje_stock,
                                 CASE 
                                     WHEN stock_cantidad = 0 THEN 'critico'
                                     WHEN stock_cantidad <= (stock_minimo * 0.5) THEN 'muy_bajo'
                                     WHEN stock_cantidad <= stock_minimo THEN 'bajo'
                                     ELSE 'normal'
                                 END as nivel_alerta
                          FROM botellas 
                          WHERE activo = TRUE
                          ORDER BY porcentaje_stock ASC";
        
        $stmt = $db->prepare($queryBotellas);
        $stmt->execute();
        $alertas['botellas'] = $stmt->fetchAll();
        
        // Validar tapones
        $queryTapones = "SELECT 'tapon' as tipo, id, nombre, stock_cantidad as stock_actual, stock_minimo,
                                ROUND((stock_cantidad / stock_minimo) * 100, 2) as porcentaje_stock,
                                CASE 
                                    WHEN stock_cantidad = 0 THEN 'critico'
                                    WHEN stock_cantidad <= (stock_minimo * 0.5) THEN 'muy_bajo'
                                    WHEN stock_cantidad <= stock_minimo THEN 'bajo'
                                    ELSE 'normal'
                                END as nivel_alerta
                         FROM tapones 
                         WHERE activo = TRUE
                         ORDER BY porcentaje_stock ASC";
        
        $stmt = $db->prepare($queryTapones);
        $stmt->execute();
        $alertas['tapones'] = $stmt->fetchAll();
        
        // Validar cápsulas
        $queryCapsulas = "SELECT 'capsula' as tipo, id, nombre, stock_cantidad as stock_actual, stock_minimo,
                                 ROUND((stock_cantidad / stock_minimo) * 100, 2) as porcentaje_stock,
                                 CASE 
                                     WHEN stock_cantidad = 0 THEN 'critico'
                                     WHEN stock_cantidad <= (stock_minimo * 0.5) THEN 'muy_bajo'
                                     WHEN stock_cantidad <= stock_minimo THEN 'bajo'
                                     ELSE 'normal'
                                 END as nivel_alerta
                          FROM capsulas 
                          WHERE activo = TRUE
                          ORDER BY porcentaje_stock ASC";
        
        $stmt = $db->prepare($queryCapsulas);
        $stmt->execute();
        $alertas['capsulas'] = $stmt->fetchAll();
        
        // Validar etiquetas
        $queryEtiquetas = "SELECT 'etiqueta' as tipo, id, nombre, stock_cantidad as stock_actual, stock_minimo,
                                  ROUND((stock_cantidad / stock_minimo) * 100, 2) as porcentaje_stock,
                                  CASE 
                                      WHEN stock_cantidad = 0 THEN 'critico'
                                      WHEN stock_cantidad <= (stock_minimo * 0.5) THEN 'muy_bajo'
                                      WHEN stock_cantidad <= stock_minimo THEN 'bajo'
                                      ELSE 'normal'
                                  END as nivel_alerta
                           FROM etiquetas 
                           WHERE activo = TRUE
                           ORDER BY porcentaje_stock ASC";
        
        $stmt = $db->prepare($queryEtiquetas);
        $stmt->execute();
        $alertas['etiquetas'] = $stmt->fetchAll();
        
        // Contar alertas por nivel
        $resumen = [
            'critico' => 0,
            'muy_bajo' => 0,
            'bajo' => 0,
            'normal' => 0
        ];
        
        foreach ($alertas as $categoria => $items) {
            foreach ($items as $item) {
                $resumen[$item['nivel_alerta']]++;
            }
        }
        
        $resultado = [
            'resumen' => $resumen,
            'alertas_por_categoria' => $alertas,
            'total_componentes' => array_sum($resumen)
        ];
        
        ApiResponse::success($resultado, "Validación de stock mínimo completada");
        
    } catch (Exception $e) {
        error_log("Error validando stock mínimo: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Validar consistencia de datos
 */
function validarConsistencia($db) {
    try {
        $inconsistencias = [];
        
        // Verificar configuraciones de embotellado
        $queryConfig = "SELECT ce.id, ce.nombre_configuracion, ce.botella_id, ce.tapon_id, ce.capsula_id
                        FROM configuracion_embotellado ce
                        LEFT JOIN botellas b ON ce.botella_id = b.id
                        LEFT JOIN tapones t ON ce.tapon_id = t.id
                        LEFT JOIN capsulas c ON ce.capsula_id = c.id
                        WHERE ce.activo = TRUE AND (b.id IS NULL OR t.id IS NULL OR (ce.capsula_id IS NOT NULL AND c.id IS NULL))";
        
        $stmt = $db->prepare($queryConfig);
        $stmt->execute();
        $configInconsistentes = $stmt->fetchAll();
        
        if (!empty($configInconsistentes)) {
            $inconsistencias['configuraciones_embotellado'] = $configInconsistentes;
        }
        
        // Verificar etiquetas huérfanas (sin producto líquido válido)
        $productosValidos = ['acholado', 'quebranta', 'italia', 'naranja', 'mistela', 'ciruela', 'perfecto_amor'];
        $placeholders = str_repeat('?,', count($productosValidos) - 1) . '?';
        
        $queryEtiquetas = "SELECT id, nombre, producto_liquido 
                           FROM etiquetas 
                           WHERE activo = TRUE AND producto_liquido NOT IN ($placeholders)";
        
        $stmt = $db->prepare($queryEtiquetas);
        $stmt->execute($productosValidos);
        $etiquetasInconsistentes = $stmt->fetchAll();
        
        if (!empty($etiquetasInconsistentes)) {
            $inconsistencias['etiquetas_producto_invalido'] = $etiquetasInconsistentes;
        }
        
        // Verificar movimientos sin referencia válida
        $queryMovimientos = "SELECT mi.id, mi.tipo_componente, mi.componente_id, mi.motivo
                             FROM movimientos_inventario mi
                             WHERE (
                                 (mi.tipo_componente = 'liquido' AND mi.componente_id NOT IN (SELECT id FROM liquidos)) OR
                                 (mi.tipo_componente = 'botella' AND mi.componente_id NOT IN (SELECT id FROM botellas)) OR
                                 (mi.tipo_componente = 'tapon' AND mi.componente_id NOT IN (SELECT id FROM tapones)) OR
                                 (mi.tipo_componente = 'capsula' AND mi.componente_id NOT IN (SELECT id FROM capsulas)) OR
                                 (mi.tipo_componente = 'etiqueta' AND mi.componente_id NOT IN (SELECT id FROM etiquetas))
                             )
                             LIMIT 100";
        
        $stmt = $db->prepare($queryMovimientos);
        $stmt->execute();
        $movimientosInconsistentes = $stmt->fetchAll();
        
        if (!empty($movimientosInconsistentes)) {
            $inconsistencias['movimientos_referencia_invalida'] = $movimientosInconsistentes;
        }
        
        // Verificar stocks negativos
        $stocksNegativos = [];
        
        $tablas = [
            'liquidos' => 'stock_ml',
            'botellas' => 'stock_cantidad',
            'tapones' => 'stock_cantidad',
            'capsulas' => 'stock_cantidad',
            'etiquetas' => 'stock_cantidad'
        ];
        
        foreach ($tablas as $tabla => $campo) {
            $query = "SELECT id, nombre, $campo as stock FROM $tabla WHERE $campo < 0";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $negativos = $stmt->fetchAll();
            
            if (!empty($negativos)) {
                $stocksNegativos[$tabla] = $negativos;
            }
        }
        
        if (!empty($stocksNegativos)) {
            $inconsistencias['stocks_negativos'] = $stocksNegativos;
        }
        
        $resultado = [
            'tiene_inconsistencias' => !empty($inconsistencias),
            'total_inconsistencias' => count($inconsistencias),
            'inconsistencias' => $inconsistencias
        ];
        
        ApiResponse::success($resultado, "Validación de consistencia completada");
        
    } catch (Exception $e) {
        error_log("Error validando consistencia: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Simular embotellado
 */
function simularEmbotellado($db) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        ApiResponse::validateRequired($data, ['liquido_id', 'configuracion_id', 'cantidad_botellas']);
        
        $validacion = validarComponentesEmbotellado($db, $data['liquido_id'], $data['configuracion_id'], $data['cantidad_botellas']);
        
        // Agregar cálculo de costos si está disponible
        if ($validacion['puede_embotellar']) {
            $validacion['costos_estimados'] = calcularCostosEmbotellado($db, $data['liquido_id'], $data['configuracion_id'], $data['cantidad_botellas']);
        }
        
        ApiResponse::success($validacion, "Simulación de embotellado completada");
        
    } catch (Exception $e) {
        error_log("Error simulando embotellado: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Verificar componentes específicos
 */
function verificarComponentes($db) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        ApiResponse::validateRequired($data, ['componentes']);
        
        $resultados = [];
        
        foreach ($data['componentes'] as $componente) {
            $tipo = $componente['tipo'];
            $id = $componente['id'];
            $cantidadRequerida = $componente['cantidad_requerida'] ?? 1;
            
            $resultado = verificarComponenteIndividual($db, $tipo, $id, $cantidadRequerida);
            $resultados[] = $resultado;
        }
        
        $puedeProducir = array_reduce($resultados, function($carry, $item) {
            return $carry && $item['disponible'];
        }, true);
        
        $respuesta = [
            'puede_producir' => $puedeProducir,
            'componentes' => $resultados
        ];
        
        ApiResponse::success($respuesta, "Verificación de componentes completada");
        
    } catch (Exception $e) {
        error_log("Error verificando componentes: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Validar componentes para embotellado (función auxiliar)
 */
function validarComponentesEmbotellado($db, $liquidoId, $configuracionId, $cantidadBotellas) {
    // Esta función ya existe en embotellado.php, la reutilizamos
    require_once 'embotellado.php';
    return validarComponentes($db, $liquidoId, $configuracionId, $cantidadBotellas);
}

/**
 * Calcular costos de embotellado
 */
function calcularCostosEmbotellado($db, $liquidoId, $configuracionId, $cantidadBotellas) {
    try {
        $costos = ['total' => 0, 'desglose' => []];
        
        // Obtener configuración
        $queryConfig = "SELECT ce.*, b.costo_unitario as costo_botella, t.costo_unitario as costo_tapon, c.costo_unitario as costo_capsula
                        FROM configuracion_embotellado ce
                        JOIN botellas b ON ce.botella_id = b.id
                        JOIN tapones t ON ce.tapon_id = t.id
                        LEFT JOIN capsulas c ON ce.capsula_id = c.id
                        WHERE ce.id = :configuracion_id";
        
        $stmt = $db->prepare($queryConfig);
        $stmt->bindParam(':configuracion_id', $configuracionId);
        $stmt->execute();
        $config = $stmt->fetch();
        
        if (!$config) return $costos;
        
        // Costo del líquido
        $queryLiquido = "SELECT costo_por_ml FROM liquidos WHERE id = :liquido_id";
        $stmt = $db->prepare($queryLiquido);
        $stmt->bindParam(':liquido_id', $liquidoId);
        $stmt->execute();
        $liquido = $stmt->fetch();
        
        if ($liquido) {
            $costoLiquido = $liquido['costo_por_ml'] * $config['capacidad_ml'] * $cantidadBotellas;
            $costos['desglose']['liquido'] = $costoLiquido;
            $costos['total'] += $costoLiquido;
        }
        
        // Costo de botellas
        $costoBotellas = $config['costo_botella'] * $cantidadBotellas;
        $costos['desglose']['botellas'] = $costoBotellas;
        $costos['total'] += $costoBotellas;
        
        // Costo de tapones
        $costoTapones = $config['costo_tapon'] * $cantidadBotellas;
        $costos['desglose']['tapones'] = $costoTapones;
        $costos['total'] += $costoTapones;
        
        // Costo de cápsulas (si aplica)
        if ($config['capsula_id'] && $config['costo_capsula']) {
            $costoCapsulas = $config['costo_capsula'] * $cantidadBotellas;
            $costos['desglose']['capsulas'] = $costoCapsulas;
            $costos['total'] += $costoCapsulas;
        }
        
        // Costo promedio de etiquetas (simplificado)
        $queryCostoEtiquetas = "SELECT AVG(costo_unitario) as costo_promedio FROM etiquetas WHERE activo = TRUE";
        $stmt = $db->prepare($queryCostoEtiquetas);
        $stmt->execute();
        $etiquetaCosto = $stmt->fetch();
        
        if ($etiquetaCosto && $etiquetaCosto['costo_promedio']) {
            $etiquetasNecesarias = $config['requiere_etiqueta_atras'] ? 2 : 1;
            $costoEtiquetas = $etiquetaCosto['costo_promedio'] * $etiquetasNecesarias * $cantidadBotellas;
            $costos['desglose']['etiquetas'] = $costoEtiquetas;
            $costos['total'] += $costoEtiquetas;
        }
        
        $costos['costo_por_botella'] = $costos['total'] / $cantidadBotellas;
        
        return $costos;
        
    } catch (Exception $e) {
        error_log("Error calculando costos: " . $e->getMessage());
        return ['total' => 0, 'desglose' => [], 'error' => 'No se pudieron calcular los costos'];
    }
}

/**
 * Verificar componente individual
 */
function verificarComponenteIndividual($db, $tipo, $id, $cantidadRequerida) {
    $tablas = [
        'liquido' => ['tabla' => 'liquidos', 'campo_stock' => 'stock_ml', 'campo_minimo' => 'stock_minimo_ml'],
        'botella' => ['tabla' => 'botellas', 'campo_stock' => 'stock_cantidad', 'campo_minimo' => 'stock_minimo'],
        'tapon' => ['tabla' => 'tapones', 'campo_stock' => 'stock_cantidad', 'campo_minimo' => 'stock_minimo'],
        'capsula' => ['tabla' => 'capsulas', 'campo_stock' => 'stock_cantidad', 'campo_minimo' => 'stock_minimo'],
        'etiqueta' => ['tabla' => 'etiquetas', 'campo_stock' => 'stock_cantidad', 'campo_minimo' => 'stock_minimo']
    ];
    
    if (!isset($tablas[$tipo])) {
        return ['disponible' => false, 'error' => 'Tipo de componente inválido'];
    }
    
    $config = $tablas[$tipo];
    $query = "SELECT nombre, {$config['campo_stock']} as stock, {$config['campo_minimo']} as minimo 
              FROM {$config['tabla']} 
              WHERE id = :id AND activo = TRUE";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $componente = $stmt->fetch();
    
    if (!$componente) {
        return ['disponible' => false, 'error' => 'Componente no encontrado'];
    }
    
    $disponible = $componente['stock'] >= $cantidadRequerida;
    $stockRestante = $componente['stock'] - $cantidadRequerida;
    $alertaStockBajo = $stockRestante <= $componente['minimo'];
    
    return [
        'tipo' => $tipo,
        'id' => $id,
        'nombre' => $componente['nombre'],
        'stock_actual' => $componente['stock'],
        'cantidad_requerida' => $cantidadRequerida,
        'disponible' => $disponible,
        'stock_restante' => $stockRestante,
        'stock_minimo' => $componente['minimo'],
        'alerta_stock_bajo' => $alertaStockBajo,
        'faltante' => $disponible ? 0 : $cantidadRequerida - $componente['stock']
    ];
}
?>
