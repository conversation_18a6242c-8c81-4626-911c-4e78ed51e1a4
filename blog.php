<?php
$page_title = "Blog";
include 'includes/header.php';

// Artículos del blog
$articulos = [
    [
        'id' => 'fiestas-patrias',
        'titulo' => 'Celebra Fiestas Patrias con UVAMAYU 🇵🇪',
        'fecha' => '2024-07-20',
        'categoria' => 'Tradición',
        'imagen' => '/assets/secciones/reunion_familiar.png',
        'resumen' => 'Descubre cómo nuestros piscos y vinos pueden hacer de tus celebraciones patrias una experiencia inolvidable.',
        'contenido' => 'Las Fiestas Patrias son una época especial para celebrar nuestra identidad peruana, y qué mejor manera de hacerlo que con productos 100% peruanos como nuestros piscos y vinos artesanales...'
    ],
    [
        'id' => 'recetas-pisco',
        'titulo' => 'Recetas Clásicas con Pisco UVAMAYU 🍸',
        'fecha' => '2024-07-15',
        'categoria' => 'Recetas',
        'imagen' => '/assets/ambiente/mesa_piscos.png',
        'resumen' => 'Aprende a preparar los cócteles más emblemáticos del Perú con nuestros piscos premium.',
        'contenido' => 'El pisco es la base de muchos cócteles tradicionales peruanos. Te enseñamos las recetas clásicas para que puedas disfrutar en casa...'
    ],
    [
        'id' => 'maridajes-vino',
        'titulo' => 'Maridajes Perfectos con Vinos UVAMAYU 🍷',
        'fecha' => '2024-07-10',
        'categoria' => 'Gastronomía',
        'imagen' => '/assets/secciones/maridajes.png',
        'resumen' => 'Descubre las combinaciones perfectas entre nuestros vinos artesanales y la gastronomía peruana.',
        'contenido' => 'El arte del maridaje consiste en encontrar la armonía perfecta entre el vino y la comida. Nuestros vinos artesanales...'
    ],
    [
        'id' => 'proceso-artesanal',
        'titulo' => 'El Arte de la Destilación Artesanal ⚗️',
        'fecha' => '2024-07-05',
        'categoria' => 'Proceso',
        'imagen' => '/assets/secciones/proceso_artesanal.png',
        'resumen' => 'Conoce el proceso tradicional que seguimos para crear nuestros piscos de calidad premium.',
        'contenido' => 'La destilación artesanal es un arte que requiere paciencia, conocimiento y pasión. En UVAMAYU seguimos métodos tradicionales...'
    ],
    [
        'id' => 'cata-degustacion',
        'titulo' => 'Guía de Cata y Degustación 👃',
        'fecha' => '2024-06-30',
        'categoria' => 'Educación',
        'imagen' => '/assets/secciones/cata_degustacion.png',
        'resumen' => 'Aprende a degustar correctamente nuestros piscos y vinos para apreciar todos sus matices.',
        'contenido' => 'La cata es una experiencia sensorial completa que nos permite apreciar todas las cualidades de un buen pisco o vino...'
    ]
];
?>

<section class="blog-hero">
    <div class="container">
        <div class="hero-content">
            <h1 class="page-title">Blog UVAMAYU 📝</h1>
            <p class="page-subtitle">Descubre historias, recetas y secretos del mundo del pisco y vino peruano</p>
        </div>
    </div>
</section>

<section class="blog-section">
    <div class="container">
        <div class="blog-grid">
            <?php foreach ($articulos as $index => $articulo): ?>
            <article class="blog-card" id="<?php echo $articulo['id']; ?>">
                <div class="blog-image image-container">
                    <div class="image-placeholder">
                        <div class="placeholder-icon">📝</div>
                    </div>
                    <img class="lazy-image blog-img" data-src="<?php echo $articulo['imagen']; ?>" alt="<?php echo $articulo['titulo']; ?>" data-width="400" data-height="300">
                    <div class="blog-category"><?php echo $articulo['categoria']; ?></div>
                </div>
                
                <div class="blog-content">
                    <div class="blog-meta">
                        <span class="blog-date">📅 <?php echo date('d/m/Y', strtotime($articulo['fecha'])); ?></span>
                    </div>
                    
                    <h2 class="blog-title"><?php echo $articulo['titulo']; ?></h2>
                    <p class="blog-excerpt"><?php echo $articulo['resumen']; ?></p>
                    
                    <button class="btn-read-more" onclick="toggleArticle('<?php echo $articulo['id']; ?>')">
                        Leer Más
                    </button>
                    
                    <div class="blog-full-content" id="content-<?php echo $articulo['id']; ?>" style="display: none;">
                        <div class="blog-text">
                            <?php if ($articulo['id'] === 'fiestas-patrias'): ?>
                                <p>Las Fiestas Patrias son una época especial para celebrar nuestra identidad peruana, y qué mejor manera de hacerlo que con productos 100% peruanos como nuestros piscos y vinos artesanales. 🇵🇪</p>
                                
                                <h3>Tradición en cada brindis 🥂</h3>
                                <p>Durante estas fechas especiales, las familias peruanas se reúnen para compartir momentos únicos. Nuestro Pisco Acholado es perfecto para preparar el tradicional Pisco Sour, mientras que nuestros vinos artesanales complementan perfectamente los platos típicos de la temporada.</p>
                                
                                <h3>Recomendaciones especiales 🌟</h3>
                                <ul>
                                            <li><strong>Pisco Quebranta:</strong> Ideal para cócteles tradicionales</li>
                                    <li><strong>Vino Borgoña:</strong> Perfecto con anticuchos y parrillas</li>
                                    <li><strong>Pisco Italia:</strong> Excelente para degustar solo</li>
                                </ul>
                                
                                <p>Celebra con orgullo peruano, celebra con UVAMAYU. Cada sorbo es un homenaje a nuestra tierra y tradición. 🍇</p>
                                
                            <?php elseif ($articulo['id'] === 'recetas-pisco'): ?>
                                <p>El pisco es la base de muchos cócteles tradicionales peruanos. Te enseñamos las recetas clásicas para que puedas disfrutar en casa de los sabores auténticos del Perú. 🍸</p>
                                
                                <h3>Pisco Sour Clásico 🥃</h3>
                                <p><strong>Ingredientes:</strong></p>
                                <ul>
                                    <li>3 oz de Pisco UVAMAYU Quebranta</li>
                                    <li>1 oz de jugo de limón fresco</li>
                                    <li>3/4 oz de jarabe de goma</li>
                                    <li>1 clara de huevo</li>
                                    <li>Gotas de amargo de angostura</li>
                                </ul>
                                
                                <h3>Chilcano de Pisco 🍋</h3>
                                <p>Refrescante y perfecto para el verano peruano:</p>
                                <ul>
                                    <li>2 oz de Pisco UVAMAYU Italia</li>
                                    <li>Jugo de 1 limón</li>
                                    <li>Ginger ale</li>
                                    <li>Hielo y rodaja de limón</li>
                                </ul>
                                
                                <p>Cada receta tiene su secreto, y el nuestro es usar solo piscos de la más alta calidad. 🌟</p>
                                
                            <?php elseif ($articulo['id'] === 'maridajes-vino'): ?>
                                <p>El arte del maridaje consiste en encontrar la armonía perfecta entre el vino y la comida. Nuestros vinos artesanales han sido creados pensando en la rica gastronomía peruana. 🍷</p>
                                
                                <h3>Vino Perfecto Amor 💕</h3>
                                <p>Su dulzura natural lo hace ideal para:</p>
                                <ul>
                                    <li>Postres tradicionales como mazamorra morada</li>
                                    <li>Quesos frescos y frutas</li>
                                    <li>Chocolate y dulces peruanos</li>
                                </ul>
                                
                                <h3>Vino Borgoña 🍇</h3>
                                <p>Su cuerpo medio y sabor equilibrado combina perfectamente con:</p>
                                <ul>
                                    <li>Carnes rojas a la parrilla</li>
                                    <li>Anticuchos y parrilladas</li>
                                    <li>Guisos tradicionales peruanos</li>
                                </ul>
                                
                                <h3>Vino Naranja 🍊</h3>
                                <p>Su frescura cítrica es perfecta para:</p>
                                <ul>
                                    <li>Ceviches y tiraditos</li>
                                    <li>Mariscos y pescados</li>
                                    <li>Ensaladas frescas</li>
                                </ul>
                                
                                <p>Cada maridaje es una experiencia única que resalta lo mejor de ambos mundos. 🌟</p>
                                
                            <?php elseif ($articulo['id'] === 'proceso-artesanal'): ?>
                                <p>La destilación artesanal es un arte que requiere paciencia, conocimiento y pasión. En UVAMAYU seguimos métodos tradicionales transmitidos de generación en generación. ⚗️</p>
                                
                                <h3>Selección de la uva 🍇</h3>
                                <p>Todo comienza con la cuidadosa selección de uvas en su punto óptimo de maduración. Nuestros viñedos en el valle de Ica producen uvas de calidad excepcional gracias al clima privilegiado de la región.</p>
                                
                                <h3>Fermentación controlada 🌿</h3>
                                <p>El proceso de fermentación se realiza en tanques de acero inoxidable bajo estricto control de temperatura. Utilizamos levaduras naturales que preservan los aromas característicos de cada varietal.</p>
                                
                                <h3>Destilación en alambique 🔥</h3>
                                <p>Nuestros alambiques de cobre, fabricados siguiendo diseños tradicionales, permiten una destilación lenta y cuidadosa. Este proceso artesanal garantiza la pureza y concentración de aromas.</p>
                                
                                <h3>Reposo y maduración ⏰</h3>
                                <p>Después de la destilación, nuestros piscos reposan en tanques de acero inoxidable, permitiendo que los sabores se integren y maduren naturalmente.</p>
                                
                                <p>Cada botella de UVAMAYU es el resultado de este proceso artesanal que honra la tradición pisquera peruana. 🏆</p>
                                
                            <?php else: ?>
                                <p>La cata es una experiencia sensorial completa que nos permite apreciar todas las cualidades de un buen pisco o vino. Te enseñamos los pasos básicos para una degustación profesional. 👃</p>
                                
                                <h3>Preparación para la cata 🍷</h3>
                                <p>Antes de comenzar, asegúrate de tener:</p>
                                <ul>
                                    <li>Copas adecuadas (tulipán para pisco, copa de vino para vinos)</li>
                                    <li>Ambiente sin olores fuertes</li>
                                    <li>Temperatura adecuada (16-18°C para piscos, 12-14°C para vinos)</li>
                                    <li>Agua y galletas neutras para limpiar el paladar</li>
                                </ul>
                                
                                <h3>Los cinco sentidos en la cata 👁️</h3>
                                <p><strong>Vista:</strong> Observa el color, transparencia y viscosidad</p>
                                <p><strong>Olfato:</strong> Identifica los aromas primarios, secundarios y terciarios</p>
                                <p><strong>Gusto:</strong> Evalúa dulzor, acidez, amargor y astringencia</p>
                                <p><strong>Tacto:</strong> Siente la textura y cuerpo en boca</p>
                                <p><strong>Retrogusto:</strong> Aprecia la persistencia de sabores</p>
                                
                                <h3>Notas de cata UVAMAYU 📝</h3>
                                <p>Nuestros productos se caracterizan por:</p>
                                <ul>
                                    <li><strong>Piscos:</strong> Aromas frutales intensos, sabor limpio y equilibrado</li>
                                    <li><strong>Vinos:</strong> Expresión frutal, acidez balanceada y final persistente</li>
                                </ul>
                                
                                <p>La práctica hace al maestro. ¡Disfruta descubriendo los matices de cada producto UVAMAYU! 🌟</p>
                            <?php endif; ?>
                        </div>
                        
                        <div class="blog-actions">
                            <button class="btn-close" onclick="toggleArticle('<?php echo $articulo['id']; ?>')">
                                Cerrar Artículo
                            </button>
                            <a href="/contacto.php" class="btn-premium">Contáctanos 💬</a>
                        </div>
                    </div>
                </div>
            </article>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<style>
    .blog-hero {
        background: linear-gradient(135deg, var(--gris-oscuro) 0%, var(--negro) 100%);
        padding: 8rem 0 4rem;
        text-align: center;
    }
    
    .page-title {
        font-family: 'Playfair Display', serif;
        font-size: 3rem;
        color: var(--dorado);
        margin-bottom: 1rem;
        animation: fadeInUp 0.6s ease-out;
    }
    
    .page-subtitle {
        font-size: 1.3rem;
        color: #ccc;
        max-width: 600px;
        margin: 0 auto;
        animation: fadeInUp 0.6s ease-out 0.2s both;
    }
    
    .blog-section {
        padding: 4rem 0;
        background: var(--negro);
    }
    
    .blog-grid {
        display: grid;
        gap: 3rem;
    }
    
    .blog-card {
        background: var(--gris-medio);
        border-radius: 15px;
        overflow: hidden;
        transition: all 0.3s ease;
        animation: fadeInUp 0.6s ease-out;
    }
    
    .blog-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(205, 170, 88, 0.2);
    }
    
    .blog-image {
        position: relative;
        height: 300px;
        overflow: hidden;
    }
    
    .blog-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .blog-card:hover .blog-img {
        transform: scale(1.05);
    }
    
    .blog-category {
        position: absolute;
        top: 1rem;
        left: 1rem;
        background: var(--dorado);
        color: var(--negro);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: bold;
    }
    
    .blog-content {
        padding: 2rem;
    }
    
    .blog-meta {
        margin-bottom: 1rem;
    }
    
    .blog-date {
        color: var(--verde);
        font-size: 0.9rem;
    }
    
    .blog-title {
        font-family: 'Playfair Display', serif;
        font-size: 1.8rem;
        color: var(--dorado);
        margin-bottom: 1rem;
        line-height: 1.3;
    }
    
    .blog-excerpt {
        color: #ccc;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }
    
    .btn-read-more {
        background: linear-gradient(45deg, var(--dorado), var(--guinda));
        color: var(--negro);
        border: none;
        padding: 0.8rem 1.5rem;
        border-radius: 8px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
    }
    
    .btn-read-more:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(205, 170, 88, 0.4);
    }
    
    .blog-full-content {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 2px solid var(--gris-oscuro);
        animation: fadeInUp 0.4s ease-out;
    }
    
    .blog-text {
        color: #ccc;
        line-height: 1.8;
    }
    
    .blog-text h3 {
        color: var(--dorado);
        margin: 2rem 0 1rem;
        font-size: 1.3rem;
    }
    
    .blog-text ul {
        margin: 1rem 0;
        padding-left: 2rem;
    }
    
    .blog-text li {
        margin-bottom: 0.5rem;
    }
    
    .blog-text strong {
        color: var(--dorado);
    }
    
    .blog-actions {
        display: flex;
        gap: 1rem;
        margin-top: 2rem;
        justify-content: center;
    }
    
    .btn-close {
        background: var(--gris-oscuro);
        color: var(--dorado);
        border: 2px solid var(--dorado);
        padding: 0.8rem 1.5rem;
        border-radius: 8px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-close:hover {
        background: var(--dorado);
        color: var(--negro);
    }
    
    @media (max-width: 768px) {
        .page-title {
            font-size: 2.5rem;
        }
        
        .blog-actions {
            flex-direction: column;
        }
        
        .blog-image {
            height: 250px;
        }
    }
</style>

<script>
function toggleArticle(articleId) {
    const content = document.getElementById('content-' + articleId);
    const button = content.previousElementSibling;
    
    if (content.style.display === 'none' || content.style.display === '') {
        content.style.display = 'block';
        button.textContent = 'Cerrar Artículo';
        button.style.background = 'var(--gris-oscuro)';
        button.style.color = 'var(--dorado)';
        
        // Scroll to article
        document.getElementById(articleId).scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    } else {
        content.style.display = 'none';
        button.textContent = 'Leer Más';
        button.style.background = 'linear-gradient(45deg, var(--dorado), var(--guinda))';
        button.style.color = 'var(--negro)';
    }
}
</script>

<?php include 'includes/footer.php'; ?>
