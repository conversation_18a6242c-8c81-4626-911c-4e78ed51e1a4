<?php
// Activar TODOS los errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Log de errores
ini_set('log_errors', 1);
ini_set('error_log', 'error.log');

echo "🔍 DEBUG BÁSICO UVAMAYU<br><br>";

// Test 1: PHP básico
echo "✅ PHP funciona - Versión: " . phpversion() . "<br>";

// Test 2: Información del servidor
echo "Servidor: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "HTTP Host: " . $_SERVER['HTTP_HOST'] . "<br>";

// Test 3: Verificar si estamos en el directorio correcto
echo "Directorio actual: " . getcwd() . "<br>";

// Test 4: Listar archivos en el directorio actual
echo "<br>📁 Archivos en directorio actual:<br>";
$files = scandir('.');
foreach($files as $file) {
    if($file != '.' && $file != '..') {
        echo "- $file<br>";
    }
}

// Test 5: Verificar si existe la carpeta api
if(is_dir('api')) {
    echo "<br>✅ Carpeta 'api' existe<br>";
    echo "📁 Archivos en carpeta api:<br>";
    $apiFiles = scandir('api');
    foreach($apiFiles as $file) {
        if($file != '.' && $file != '..') {
            echo "- api/$file<br>";
        }
    }
} else {
    echo "<br>❌ Carpeta 'api' NO existe<br>";
}

// Test 6: Verificar extensiones PHP críticas
echo "<br>🔧 Extensiones PHP:<br>";
$extensions = ['pdo', 'pdo_mysql', 'json', 'mysqli'];
foreach($extensions as $ext) {
    $status = extension_loaded($ext) ? "✅" : "❌";
    echo "$status $ext<br>";
}

// Test 7: Test de conexión MySQL básico
echo "<br>🗄️ Test de conexión MySQL:<br>";
try {
    $host = 'localhost';
    $dbname = 'uvamayuc_db';
    $username = 'uvamayuc_sebas';
    $password = 'Sebastian090101@R';
    
    echo "Intentando conectar a: $host<br>";
    echo "Base de datos: $dbname<br>";
    echo "Usuario: $username<br>";
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    echo "✅ Conexión MySQL exitosa<br>";
    
    // Verificar tablas
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Tablas encontradas: " . count($tables) . "<br>";
    
} catch(Exception $e) {
    echo "❌ Error MySQL: " . $e->getMessage() . "<br>";
}

// Test 8: Verificar permisos de archivos
echo "<br>🔐 Permisos de archivos:<br>";
$checkFiles = ['index.php', 'config.php', '.htaccess'];
foreach($checkFiles as $file) {
    if(file_exists($file)) {
        $perms = substr(sprintf('%o', fileperms($file)), -4);
        echo "$file: $perms<br>";
    } else {
        echo "$file: ❌ NO existe<br>";
    }
}

// Test 9: Variables de entorno importantes
echo "<br>🌍 Variables de entorno:<br>";
echo "PHP_SELF: " . $_SERVER['PHP_SELF'] . "<br>";
echo "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "SCRIPT_NAME: " . $_SERVER['SCRIPT_NAME'] . "<br>";

// Test 10: Crear archivo de log de errores
echo "<br>📝 Creando archivo de log...<br>";
file_put_contents('debug.log', date('Y-m-d H:i:s') . " - Debug ejecutado correctamente\n", FILE_APPEND);
echo "✅ Log creado<br>";

// Test 11: Verificar .htaccess
echo "<br>⚙️ Verificando .htaccess:<br>";
if(file_exists('.htaccess')) {
    echo "✅ .htaccess existe<br>";
    $htaccess_size = filesize('.htaccess');
    echo "Tamaño: $htaccess_size bytes<br>";
} else {
    echo "❌ .htaccess NO existe<br>";
}

// Test 12: Test de include/require
echo "<br>📥 Test de includes:<br>";
if(file_exists('config.php')) {
    try {
        $config = include 'config.php';
        echo "✅ config.php se puede incluir<br>";
        if(is_array($config)) {
            echo "✅ config.php retorna array<br>";
        } else {
            echo "❌ config.php NO retorna array<br>";
        }
    } catch(Exception $e) {
        echo "❌ Error incluyendo config.php: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ config.php NO existe<br>";
}

echo "<br>🎯 DEBUG COMPLETADO<br>";
echo "Si ves este mensaje, PHP está funcionando.<br>";
echo "Hora: " . date('Y-m-d H:i:s') . "<br>";
?>
