<?php
/**
 * API para gestión de ventas
 * Incluye opción de regalo y control de stock
 */

require_once 'config/Database.php';
require_once 'config/ApiResponse.php';

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $path_info = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
    
    // Extraer acción de la URL
    $action = '';
    $id = null;
    if ($path_info) {
        $parts = explode('/', trim($path_info, '/'));
        $action = $parts[0] ?? '';
        $id = isset($parts[1]) ? (int)$parts[1] : null;
    }
    
    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'stock-disponible':
                    getStockDisponible($db);
                    break;
                case 'precios':
                    getPrecios($db);
                    break;
                case 'historial':
                    getHistorialVentas($db);
                    break;
                case '':
                    if ($id) {
                        getVenta($db, $id);
                    } else {
                        getVentas($db);
                    }
                    break;
                default:
                    ApiResponse::error("Acción no válida", 400);
            }
            break;
            
        case 'POST':
            switch ($action) {
                case 'procesar':
                    procesarVenta($db);
                    break;
                case 'validar':
                    validarVenta($db);
                    break;
                default:
                    crearVenta($db);
            }
            break;
            
        case 'PUT':
            if (!$id) {
                ApiResponse::error("ID requerido para actualizar", 400);
            }
            actualizarVenta($db, $id);
            break;
            
        case 'DELETE':
            if (!$id) {
                ApiResponse::error("ID requerido para cancelar", 400);
            }
            cancelarVenta($db, $id);
            break;
            
        default:
            ApiResponse::error("Método no permitido", 405);
    }
    
} catch (Exception $e) {
    error_log("Error en API ventas: " . $e->getMessage());
    ApiResponse::serverError();
}

/**
 * Obtener stock disponible de productos terminados
 */
function getStockDisponible($db) {
    try {
        $query = "SELECT 
                    spt.*,
                    le.codigo_lote,
                    CASE 
                        WHEN spt.capacidad_ml = 750 THEN spt.precio_750ml
                        WHEN spt.capacidad_ml = 4000 THEN spt.precio_4000ml
                        WHEN spt.capacidad_ml = 50 THEN spt.precio_50ml
                        ELSE spt.precio_750ml
                    END as precio_actual
                  FROM stock_productos_terminados spt
                  JOIN lotes_embotellado le ON spt.lote_embotellado_id = le.id
                  WHERE spt.cantidad_disponible > 0 AND spt.activo = TRUE
                  ORDER BY spt.producto_nombre, spt.capacidad_ml, spt.fecha_embotellado";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $stock = $stmt->fetchAll();
        
        // Agrupar por producto y capacidad
        $stockAgrupado = [];
        foreach ($stock as $item) {
            $key = $item['producto_nombre'] . '_' . $item['capacidad_ml'] . 'ml';
            if (!isset($stockAgrupado[$key])) {
                $stockAgrupado[$key] = [
                    'producto_nombre' => $item['producto_nombre'],
                    'capacidad_ml' => $item['capacidad_ml'],
                    'precio_actual' => $item['precio_actual'],
                    'cantidad_total' => 0,
                    'lotes' => []
                ];
            }
            
            $stockAgrupado[$key]['cantidad_total'] += $item['cantidad_disponible'];
            $stockAgrupado[$key]['lotes'][] = [
                'lote_id' => $item['lote_embotellado_id'],
                'codigo_lote' => $item['codigo_lote'],
                'cantidad_disponible' => $item['cantidad_disponible'],
                'fecha_embotellado' => $item['fecha_embotellado']
            ];
        }
        
        ApiResponse::success(array_values($stockAgrupado), "Stock disponible obtenido");
        
    } catch (Exception $e) {
        error_log("Error obteniendo stock: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Obtener precios de productos
 */
function getPrecios($db) {
    try {
        $query = "SELECT nombre, precio_750ml, precio_caja_12, precio_4000ml, precio_50ml 
                  FROM productos 
                  WHERE activo = TRUE 
                  ORDER BY categoria, nombre";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $precios = $stmt->fetchAll();
        
        ApiResponse::success($precios, "Precios obtenidos exitosamente");
        
    } catch (Exception $e) {
        error_log("Error obteniendo precios: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Procesar venta completa
 */
function procesarVenta($db) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        ApiResponse::validateRequired($data, ['productos', 'fecha_venta']);
        
        // Validar stock disponible
        $validacion = validarStockDisponible($db, $data['productos']);
        if (!$validacion['puede_vender']) {
            ApiResponse::error("Stock insuficiente", 400, $validacion['faltantes']);
        }
        
        $db->beginTransaction();
        
        try {
            // Generar código de venta
            $codigoVenta = generarCodigoVenta($db, $data['fecha_venta']);
            
            // Calcular totales
            $subtotal = 0;
            foreach ($data['productos'] as $producto) {
                $subtotal += $producto['precio_unitario'] * $producto['cantidad'];
            }
            
            $descuento = $data['descuento'] ?? 0;
            $total = $subtotal - $descuento;
            
            // Si es regalo, el total es 0
            if ($data['es_regalo'] ?? false) {
                $total = 0;
            }
            
            // Crear venta
            $queryVenta = "INSERT INTO ventas (
                codigo_venta, cliente_nombre, cliente_telefono, cliente_email,
                fecha_venta, subtotal, descuento, total, es_regalo, motivo_regalo,
                metodo_pago, estado, notas, vendedor
            ) VALUES (
                :codigo_venta, :cliente_nombre, :cliente_telefono, :cliente_email,
                :fecha_venta, :subtotal, :descuento, :total, :es_regalo, :motivo_regalo,
                :metodo_pago, :estado, :notas, :vendedor
            )";
            
            $stmtVenta = $db->prepare($queryVenta);
            $stmtVenta->bindParam(':codigo_venta', $codigoVenta);
            $stmtVenta->bindParam(':cliente_nombre', $data['cliente_nombre'] ?? '');
            $stmtVenta->bindParam(':cliente_telefono', $data['cliente_telefono'] ?? '');
            $stmtVenta->bindParam(':cliente_email', $data['cliente_email'] ?? '');
            $stmtVenta->bindParam(':fecha_venta', $data['fecha_venta']);
            $stmtVenta->bindParam(':subtotal', $subtotal);
            $stmtVenta->bindParam(':descuento', $descuento);
            $stmtVenta->bindParam(':total', $total);
            $stmtVenta->bindParam(':es_regalo', $data['es_regalo'] ?? false, PDO::PARAM_BOOL);
            $stmtVenta->bindParam(':motivo_regalo', $data['motivo_regalo'] ?? '');
            $stmtVenta->bindParam(':metodo_pago', $data['metodo_pago'] ?? 'efectivo');
            $stmtVenta->bindParam(':estado', $data['estado'] ?? 'pagado');
            $stmtVenta->bindParam(':notas', $data['notas'] ?? '');
            $stmtVenta->bindParam(':vendedor', $data['vendedor'] ?? 'Sistema');
            
            $stmtVenta->execute();
            $ventaId = $db->lastInsertId();
            
            // Crear detalles de venta y descontar stock
            foreach ($data['productos'] as $producto) {
                // Insertar detalle
                $queryDetalle = "INSERT INTO detalle_ventas (
                    venta_id, lote_embotellado_id, producto_nombre, capacidad_ml,
                    cantidad, precio_unitario, subtotal, es_regalo
                ) VALUES (
                    :venta_id, :lote_embotellado_id, :producto_nombre, :capacidad_ml,
                    :cantidad, :precio_unitario, :subtotal, :es_regalo
                )";
                
                $stmtDetalle = $db->prepare($queryDetalle);
                $stmtDetalle->bindParam(':venta_id', $ventaId);
                $stmtDetalle->bindParam(':lote_embotellado_id', $producto['lote_id']);
                $stmtDetalle->bindParam(':producto_nombre', $producto['producto_nombre']);
                $stmtDetalle->bindParam(':capacidad_ml', $producto['capacidad_ml']);
                $stmtDetalle->bindParam(':cantidad', $producto['cantidad']);
                $stmtDetalle->bindParam(':precio_unitario', $producto['precio_unitario']);
                
                $subtotalProducto = $producto['precio_unitario'] * $producto['cantidad'];
                $stmtDetalle->bindParam(':subtotal', $subtotalProducto);
                $stmtDetalle->bindParam(':es_regalo', $producto['es_regalo'] ?? false, PDO::PARAM_BOOL);
                
                $stmtDetalle->execute();
                
                // Descontar stock (el trigger se encarga automáticamente)
            }
            
            $db->commit();
            
            ApiResponse::success([
                'venta_id' => $ventaId,
                'codigo_venta' => $codigoVenta,
                'total' => $total,
                'es_regalo' => $data['es_regalo'] ?? false
            ], "Venta procesada exitosamente", 201);
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
        
    } catch (Exception $e) {
        error_log("Error procesando venta: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Validar stock disponible para venta
 */
function validarStockDisponible($db, $productos) {
    $faltantes = [];
    $puedeVender = true;
    
    foreach ($productos as $producto) {
        $query = "SELECT cantidad_disponible 
                  FROM stock_productos_terminados 
                  WHERE lote_embotellado_id = :lote_id AND activo = TRUE";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':lote_id', $producto['lote_id']);
        $stmt->execute();
        
        $stock = $stmt->fetch();
        
        if (!$stock || $stock['cantidad_disponible'] < $producto['cantidad']) {
            $faltantes[] = [
                'producto' => $producto['producto_nombre'],
                'capacidad' => $producto['capacidad_ml'] . 'ml',
                'solicitado' => $producto['cantidad'],
                'disponible' => $stock ? $stock['cantidad_disponible'] : 0,
                'faltante' => $producto['cantidad'] - ($stock ? $stock['cantidad_disponible'] : 0)
            ];
            $puedeVender = false;
        }
    }
    
    return [
        'puede_vender' => $puedeVender,
        'faltantes' => $faltantes
    ];
}

/**
 * Generar código único de venta
 */
function generarCodigoVenta($db, $fecha) {
    $fechaFormato = date('Ymd', strtotime($fecha));
    $base = "VENTA-$fechaFormato";
    
    $query = "SELECT COUNT(*) as total FROM ventas WHERE codigo_venta LIKE :base";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':base', $base . '%');
    $stmt->execute();
    $result = $stmt->fetch();
    
    $numero = $result['total'] + 1;
    return $base . '-' . str_pad($numero, 3, '0', STR_PAD_LEFT);
}

/**
 * Obtener historial de ventas
 */
function getHistorialVentas($db) {
    try {
        $limite = (int)($_GET['limite'] ?? 50);
        $fechaDesde = $_GET['fecha_desde'] ?? null;
        $fechaHasta = $_GET['fecha_hasta'] ?? null;
        $soloRegalos = $_GET['solo_regalos'] ?? null;
        
        $filtros = [];
        $params = [];
        
        if ($fechaDesde) {
            $filtros[] = "v.fecha_venta >= :fecha_desde";
            $params[':fecha_desde'] = $fechaDesde;
        }
        
        if ($fechaHasta) {
            $filtros[] = "v.fecha_venta <= :fecha_hasta";
            $params[':fecha_hasta'] = $fechaHasta;
        }
        
        if ($soloRegalos !== null) {
            $filtros[] = "v.es_regalo = :solo_regalos";
            $params[':solo_regalos'] = $soloRegalos === 'true' ? 1 : 0;
        }
        
        $whereClause = !empty($filtros) ? 'WHERE ' . implode(' AND ', $filtros) : '';
        
        $query = "SELECT v.*, 
                         COUNT(dv.id) as total_productos,
                         SUM(dv.cantidad) as total_unidades
                  FROM ventas v
                  LEFT JOIN detalle_ventas dv ON v.id = dv.venta_id
                  $whereClause
                  GROUP BY v.id
                  ORDER BY v.fecha_venta DESC, v.created_at DESC
                  LIMIT :limite";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':limite', $limite, PDO::PARAM_INT);
        
        foreach ($params as $key => $value) {
            $stmt->bindParam($key, $value);
        }
        
        $stmt->execute();
        $ventas = $stmt->fetchAll();
        
        ApiResponse::success($ventas, "Historial de ventas obtenido");
        
    } catch (Exception $e) {
        error_log("Error obteniendo historial: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Obtener venta específica con detalles
 */
function getVenta($db, $id) {
    try {
        // Obtener venta
        $queryVenta = "SELECT * FROM ventas WHERE id = :id";
        $stmtVenta = $db->prepare($queryVenta);
        $stmtVenta->bindParam(':id', $id);
        $stmtVenta->execute();
        
        $venta = $stmtVenta->fetch();
        if (!$venta) {
            ApiResponse::notFound("Venta no encontrada");
        }
        
        // Obtener detalles
        $queryDetalles = "SELECT dv.*, le.codigo_lote
                          FROM detalle_ventas dv
                          LEFT JOIN lotes_embotellado le ON dv.lote_embotellado_id = le.id
                          WHERE dv.venta_id = :venta_id";
        
        $stmtDetalles = $db->prepare($queryDetalles);
        $stmtDetalles->bindParam(':venta_id', $id);
        $stmtDetalles->execute();
        
        $venta['detalles'] = $stmtDetalles->fetchAll();
        
        ApiResponse::success($venta, "Venta obtenida exitosamente");
        
    } catch (Exception $e) {
        error_log("Error obteniendo venta: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Obtener todas las ventas (resumen)
 */
function getVentas($db) {
    try {
        $query = "SELECT v.*, 
                         COUNT(dv.id) as total_productos,
                         SUM(dv.cantidad) as total_unidades
                  FROM ventas v
                  LEFT JOIN detalle_ventas dv ON v.id = dv.venta_id
                  GROUP BY v.id
                  ORDER BY v.fecha_venta DESC, v.created_at DESC
                  LIMIT 100";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $ventas = $stmt->fetchAll();
        
        ApiResponse::success($ventas, "Ventas obtenidas exitosamente");
        
    } catch (Exception $e) {
        error_log("Error obteniendo ventas: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Validar venta antes de procesar
 */
function validarVenta($db) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        ApiResponse::validateRequired($data, ['productos']);
        
        $validacion = validarStockDisponible($db, $data['productos']);
        
        ApiResponse::success($validacion, "Validación completada");
        
    } catch (Exception $e) {
        error_log("Error validando venta: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Crear venta (sin procesar - borrador)
 */
function crearVenta($db) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        // Crear venta en estado pendiente
        $data['estado'] = 'pendiente';
        procesarVenta($db);
        
    } catch (Exception $e) {
        error_log("Error creando venta: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Actualizar venta
 */
function actualizarVenta($db, $id) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        $fields = [];
        $params = [':id' => $id];
        
        $allowedFields = ['cliente_nombre', 'cliente_telefono', 'cliente_email', 'estado', 'metodo_pago', 'notas'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = :$field";
                $params[":$field"] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            ApiResponse::error("No hay campos para actualizar", 400);
        }
        
        $query = "UPDATE ventas SET " . implode(', ', $fields) . " WHERE id = :id";
        $stmt = $db->prepare($query);
        
        if ($stmt->execute($params) && $stmt->rowCount() > 0) {
            ApiResponse::success(null, "Venta actualizada exitosamente");
        } else {
            ApiResponse::notFound("Venta no encontrada");
        }
        
    } catch (Exception $e) {
        error_log("Error actualizando venta: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Cancelar venta
 */
function cancelarVenta($db, $id) {
    try {
        $db->beginTransaction();
        
        try {
            // Obtener detalles de la venta para restaurar stock
            $queryDetalles = "SELECT * FROM detalle_ventas WHERE venta_id = :venta_id";
            $stmtDetalles = $db->prepare($queryDetalles);
            $stmtDetalles->bindParam(':venta_id', $id);
            $stmtDetalles->execute();
            $detalles = $stmtDetalles->fetchAll();
            
            // Restaurar stock
            foreach ($detalles as $detalle) {
                $queryRestore = "UPDATE stock_productos_terminados 
                                SET cantidad_disponible = cantidad_disponible + :cantidad
                                WHERE lote_embotellado_id = :lote_id";
                $stmtRestore = $db->prepare($queryRestore);
                $stmtRestore->bindParam(':cantidad', $detalle['cantidad']);
                $stmtRestore->bindParam(':lote_id', $detalle['lote_embotellado_id']);
                $stmtRestore->execute();
            }
            
            // Marcar venta como cancelada
            $queryCancel = "UPDATE ventas SET estado = 'cancelado' WHERE id = :id";
            $stmtCancel = $db->prepare($queryCancel);
            $stmtCancel->bindParam(':id', $id);
            $stmtCancel->execute();
            
            if ($stmtCancel->rowCount() === 0) {
                throw new Exception("Venta no encontrada");
            }
            
            $db->commit();
            
            ApiResponse::success(null, "Venta cancelada y stock restaurado");
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
        
    } catch (Exception $e) {
        error_log("Error cancelando venta: " . $e->getMessage());
        ApiResponse::serverError();
    }
}
?>
