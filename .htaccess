# UVAMAYU - Configuración Apache
RewriteEngine On

# Redireccionar a HTTPS (opcional, descomenta si tienes SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# ========================================
# RUTAS LIMPIAS SIN .PHP
# ========================================

# 1. Redireccionar URLs con .php a URLs limpias (301 redirect)
RewriteCond %{THE_REQUEST} \s/+(.+)\.php[\s?] [NC]
RewriteCond %1 !^(sitemap|procesar_contacto|comentarios_internos|404|router)$
RewriteRule ^ /%1? [R=301,L]

# 2. Manejar index.php específicamente
RewriteCond %{THE_REQUEST} \s/+index\.php[\s?] [NC]
RewriteRule ^index\.php$ /? [R=301,L]

# 3. Sitemap dinámico
RewriteRule ^sitemap\.xml$ sitemap.php [L]

# 3.1. APIs del sistema de inventario
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/([^/]+)/(.*)$ api/$1.php/$2 [L,QSA]

# 4. URLs de productos individuales (SEO friendly)
RewriteRule ^productos/([0-9]+)/?$ productos.php?id=$1 [L,QSA]

# 5. Rutas principales sin extensión .php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^productos/?$ productos.php [L,QSA]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^nosotros/?$ nosotros.php [L,QSA]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^blog/?$ blog.php [L,QSA]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^contacto/?$ contacto.php [L,QSA]

# 6. Remover múltiples slashes
RewriteCond %{THE_REQUEST} //+
RewriteRule ^(.*)$ /$1 [R=301,L]

# Configuración de tipos MIME
AddType application/json .json

# Compresión GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache para recursos estáticos
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/json "access plus 1 week"
</IfModule>

# Seguridad básica
<Files "config.json">
    Order Allow,Deny
    Allow from all
</Files>

<Files "productos.json">
    Order Allow,Deny
    Allow from all
</Files>

# Proteger archivo de mensajes (solo acceso interno)
<Files "mensajes.json">
    Order Allow,Deny
    Deny from all
</Files>

# Proteger router de acceso directo
<Files "router.php">
    Order Allow,Deny
    Deny from all
</Files>

# Proteger archivos SEO
<Files "seo_config.json">
    Order Allow,Deny
    Allow from all
</Files>

# Prevenir acceso a archivos sensibles
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Página de error personalizada
ErrorDocument 404 /404.php

# Configuración de PHP (si es necesario)
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value memory_limit 128M
    php_value max_execution_time 30
</IfModule>

# Headers de seguridad
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # CORS para APIs
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
</IfModule>

# Proteger archivos de configuración del inventario
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.sql">
    Order Allow,Deny
    Deny from all
</Files>
