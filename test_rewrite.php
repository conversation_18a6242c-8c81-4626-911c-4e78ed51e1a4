<?php
echo "<h1>Test de mod_rewrite</h1>";

if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "✅ mod_rewrite está ACTIVO<br>";
    } else {
        echo "❌ mod_rewrite NO está activo<br>";
    }
} else {
    echo "⚠️ No se puede verificar mod_rewrite (función no disponible)<br>";
}

echo "<br><strong>PATH_INFO:</strong> " . ($_SERVER['PATH_INFO'] ?? 'No definido') . "<br>";
echo "<strong>REQUEST_URI:</strong> " . $_SERVER['REQUEST_URI'] . "<br>";
echo "<strong>SCRIPT_NAME:</strong> " . $_SERVER['SCRIPT_NAME'] . "<br>";
?>
