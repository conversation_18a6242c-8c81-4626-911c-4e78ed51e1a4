<?php
// SEO Manager para UVAMAYU
class SEOManager {
    private $seo_config;
    private $config;
    
    public function __construct() {
        $this->seo_config = json_decode(file_get_contents(__DIR__ . '/../seo_config.json'), true);
        $this->config = json_decode(file_get_contents(__DIR__ . '/../config.json'), true);
    }
    
    public function getPageSEO($page, $product = null) {
        $seo = $this->seo_config['pages'][$page] ?? $this->seo_config['pages']['home'];
        $base_url = $this->seo_config['global']['base_url'];
        
        // Si es una página de producto específico
        if ($product && isset($this->seo_config['products'][$product['categoria']])) {
            $product_config = $this->seo_config['products'][strtolower($product['categoria'])];
            
            $seo['title'] = str_replace([
                '{product_name}',
                '{varietal}',
                '{graduacion}'
            ], [
                $product['nombre'],
                $product['varietal'],
                $product['graduacion']
            ], $product_config['title_template']);
            
            $seo['description'] = str_replace([
                '{product_name}',
                '{varietal}',
                '{graduacion}'
            ], [
                $product['nombre'],
                $product['varietal'],
                $product['graduacion']
            ], $product_config['description_template']);
            
            $seo['keywords'] = str_replace([
                '{product_name}',
                '{varietal}'
            ], [
                $product['nombre'],
                strtolower($product['varietal'])
            ], $product_config['keywords_template']);
            
            $seo['canonical'] = "/productos/" . $product['id'];
            $seo['og_type'] = 'product';
        }
        
        return $seo;
    }
    
    public function renderMetaTags($page, $product = null, $additional_meta = []) {
        $seo = $this->getPageSEO($page, $product);
        $base_url = $this->seo_config['global']['base_url'];
        $site_name = $this->seo_config['global']['site_name'];
        
        // Merge additional meta
        $seo = array_merge($seo, $additional_meta);
        
        $current_url = $base_url . $seo['canonical'];
        $image_url = $base_url . ($product['imagen_principal'] ?? $this->seo_config['global']['default_image']);
        
        echo "<!-- SEO Meta Tags -->\n";
        echo "<title>" . htmlspecialchars($seo['title']) . "</title>\n";
        echo "<meta name=\"description\" content=\"" . htmlspecialchars($seo['description']) . "\">\n";
        echo "<meta name=\"keywords\" content=\"" . htmlspecialchars($seo['keywords']) . "\">\n";
        echo "<meta name=\"author\" content=\"" . htmlspecialchars($this->config['seo']['author']) . "\">\n";
        echo "<link rel=\"canonical\" href=\"" . $current_url . "\">\n";
        
        // Open Graph
        echo "\n<!-- Open Graph / Facebook -->\n";
        echo "<meta property=\"og:type\" content=\"" . $seo['og_type'] . "\">\n";
        echo "<meta property=\"og:url\" content=\"" . $current_url . "\">\n";
        echo "<meta property=\"og:title\" content=\"" . htmlspecialchars($seo['title']) . "\">\n";
        echo "<meta property=\"og:description\" content=\"" . htmlspecialchars($seo['description']) . "\">\n";
        echo "<meta property=\"og:image\" content=\"" . $image_url . "\">\n";
        echo "<meta property=\"og:site_name\" content=\"" . htmlspecialchars($site_name) . "\">\n";
        echo "<meta property=\"og:locale\" content=\"es_PE\">\n";
        
        // Twitter Card
        echo "\n<!-- Twitter -->\n";
        echo "<meta property=\"twitter:card\" content=\"summary_large_image\">\n";
        echo "<meta property=\"twitter:url\" content=\"" . $current_url . "\">\n";
        echo "<meta property=\"twitter:title\" content=\"" . htmlspecialchars($seo['title']) . "\">\n";
        echo "<meta property=\"twitter:description\" content=\"" . htmlspecialchars($seo['description']) . "\">\n";
        echo "<meta property=\"twitter:image\" content=\"" . $image_url . "\">\n";
        
        if (!empty($this->seo_config['global']['twitter_handle'])) {
            echo "<meta property=\"twitter:site\" content=\"" . $this->seo_config['global']['twitter_handle'] . "\">\n";
        }
        
        // Additional SEO
        echo "\n<!-- Additional SEO -->\n";
        echo "<meta name=\"robots\" content=\"index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1\">\n";
        echo "<meta name=\"googlebot\" content=\"index, follow\">\n";
        echo "<meta name=\"language\" content=\"Spanish\">\n";
        echo "<meta name=\"geo.region\" content=\"PE-LIM\">\n";
        echo "<meta name=\"geo.placename\" content=\"Lima, Perú\">\n";
        echo "<meta name=\"geo.position\" content=\"-12.167156;-76.990201\">\n";
        echo "<meta name=\"ICBM\" content=\"-12.167156, -76.990201\">\n";
        
        // Favicon and icons
        echo "\n<!-- Favicon -->\n";
        echo "<link rel=\"icon\" type=\"image/x-icon\" href=\"/favicon.ico\">\n";
        echo "<link rel=\"apple-touch-icon\" sizes=\"180x180\" href=\"/apple-touch-icon.png\">\n";
        echo "<link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/favicon-32x32.png\">\n";
        echo "<link rel=\"icon\" type=\"image/png\" sizes=\"16x16\" href=\"/favicon-16x16.png\">\n";
        echo "<link rel=\"manifest\" href=\"/site.webmanifest\">\n";
    }
    
    public function renderSchemaOrg($page, $product = null, $products = null) {
        echo "\n<!-- Schema.org JSON-LD -->\n";
        echo "<script type=\"application/ld+json\">\n";
        
        if ($page === 'home') {
            // Organization + Website schema
            $schema = [
                "@context" => "https://schema.org",
                "@graph" => [
                    $this->seo_config['schema_org']['organization'],
                    $this->seo_config['schema_org']['local_business'],
                    [
                        "@type" => "WebSite",
                        "@id" => $this->seo_config['global']['base_url'] . "/#website",
                        "url" => $this->seo_config['global']['base_url'],
                        "name" => $this->seo_config['global']['site_name'],
                        "description" => $this->seo_config['pages']['home']['description'],
                        "potentialAction" => [
                            "@type" => "SearchAction",
                            "target" => [
                                "@type" => "EntryPoint",
                                "urlTemplate" => $this->seo_config['global']['base_url'] . "/productos?search={search_term_string}"
                            ],
                            "query-input" => "required name=search_term_string"
                        ]
                    ]
                ]
            ];
        } elseif ($page === 'productos' && $product) {
            // Product schema
            $schema = [
                "@context" => "https://schema.org",
                "@type" => "Product",
                "name" => $product['nombre'],
                "description" => $product['descripcion'],
                "image" => $this->seo_config['global']['base_url'] . $product['imagen_principal'],
                "brand" => [
                    "@type" => "Brand",
                    "name" => "UVAMAYU"
                ],
                "manufacturer" => [
                    "@type" => "Organization",
                    "name" => "UVAMAYU"
                ],
                "offers" => [
                    "@type" => "Offer",
                    "url" => $this->seo_config['global']['base_url'] . "/productos",
                    "priceCurrency" => "PEN",
                    "price" => $product['precio_unitario'],
                    "availability" => "https://schema.org/InStock",
                    "seller" => [
                        "@type" => "Organization",
                        "name" => "UVAMAYU"
                    ]
                ],
                "category" => $product['categoria'],
                "alcoholContent" => $product['graduacion']
            ];
            
            if ($product['precio_caja']) {
                $schema['offers'][] = [
                    "@type" => "Offer",
                    "url" => $this->seo_config['global']['base_url'] . "/productos",
                    "priceCurrency" => "PEN",
                    "price" => $product['precio_caja'],
                    "availability" => "https://schema.org/InStock",
                    "priceSpecification" => [
                        "@type" => "UnitPriceSpecification",
                        "price" => $product['precio_caja'],
                        "priceCurrency" => "PEN",
                        "referenceQuantity" => [
                            "@type" => "QuantitativeValue",
                            "value" => 12,
                            "unitText" => "unidades"
                        ]
                    ],
                    "seller" => [
                        "@type" => "Organization",
                        "name" => "UVAMAYU"
                    ]
                ];
            }
        } elseif ($page === 'productos' && $products) {
            // Product catalog
            $schema = [
                "@context" => "https://schema.org",
                "@type" => "ItemList",
                "name" => "Catálogo de Piscos y Vinos UVAMAYU",
                "description" => "Productos artesanales de pisco y vino peruano",
                "numberOfItems" => count($products),
                "itemListElement" => []
            ];
            
            foreach ($products as $index => $prod) {
                $schema['itemListElement'][] = [
                    "@type" => "ListItem",
                    "position" => $index + 1,
                    "item" => [
                        "@type" => "Product",
                        "name" => $prod['nombre'],
                        "description" => $prod['descripcion'],
                        "image" => $this->seo_config['global']['base_url'] . $prod['imagen_principal'],
                        "offers" => [
                            "@type" => "Offer",
                            "price" => $prod['precio_unitario'],
                            "priceCurrency" => "PEN"
                        ]
                    ]
                ];
            }
        } else {
            // Basic webpage schema
            $schema = [
                "@context" => "https://schema.org",
                "@type" => "WebPage",
                "name" => $this->seo_config['pages'][$page]['title'],
                "description" => $this->seo_config['pages'][$page]['description'],
                "url" => $this->seo_config['global']['base_url'] . $this->seo_config['pages'][$page]['canonical'],
                "isPartOf" => [
                    "@type" => "WebSite",
                    "name" => $this->seo_config['global']['site_name'],
                    "url" => $this->seo_config['global']['base_url']
                ]
            ];
        }
        
        echo json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        echo "\n</script>\n";
    }
    
    public function renderBreadcrumbs($breadcrumbs) {
        if (empty($breadcrumbs)) return;
        
        echo "\n<!-- Breadcrumbs Schema -->\n";
        echo "<script type=\"application/ld+json\">\n";
        
        $schema = [
            "@context" => "https://schema.org",
            "@type" => "BreadcrumbList",
            "itemListElement" => []
        ];
        
        foreach ($breadcrumbs as $index => $crumb) {
            $schema['itemListElement'][] = [
                "@type" => "ListItem",
                "position" => $index + 1,
                "name" => $crumb['name'],
                "item" => $this->seo_config['global']['base_url'] . $crumb['url']
            ];
        }
        
        echo json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        echo "\n</script>\n";
    }
}

// Función helper para usar en las páginas
function renderSEO($page, $product = null, $additional_meta = [], $breadcrumbs = [], $products = null) {
    $seo = new SEOManager();
    $seo->renderMetaTags($page, $product, $additional_meta);
    $seo->renderSchemaOrg($page, $product, $products);
    if (!empty($breadcrumbs)) {
        $seo->renderBreadcrumbs($breadcrumbs);
    }
}
?>
