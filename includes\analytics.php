<?php
// Analytics y herramientas de seguimiento para UVAMAYU

function renderAnalytics() {
    $seo_config = json_decode(file_get_contents(__DIR__ . '/../seo_config.json'), true);
    
    // Google Analytics 4 (GA4)
    if (!empty($seo_config['global']['google_analytics'])) {
        $ga_id = $seo_config['global']['google_analytics'];
        echo "\n<!-- Google Analytics -->\n";
        echo "<script async src=\"https://www.googletagmanager.com/gtag/js?id={$ga_id}\"></script>\n";
        echo "<script>\n";
        echo "window.dataLayer = window.dataLayer || [];\n";
        echo "function gtag(){dataLayer.push(arguments);}\n";
        echo "gtag('js', new Date());\n";
        echo "gtag('config', '{$ga_id}', {\n";
        echo "  page_title: document.title,\n";
        echo "  page_location: window.location.href,\n";
        echo "  custom_map: {\n";
        echo "    'dimension1': 'page_type',\n";
        echo "    'dimension2': 'product_category'\n";
        echo "  }\n";
        echo "});\n";
        echo "</script>\n";
    }
    
    // Google Site Verification
    if (!empty($seo_config['global']['google_site_verification'])) {
        echo "<meta name=\"google-site-verification\" content=\"{$seo_config['global']['google_site_verification']}\">\n";
    }
    
    // Facebook Pixel (opcional)
    if (!empty($seo_config['global']['facebook_app_id'])) {
        echo "<meta property=\"fb:app_id\" content=\"{$seo_config['global']['facebook_app_id']}\">\n";
    }
}

// Función para tracking de eventos específicos
function renderEventTracking() {
    echo "\n<!-- Event Tracking -->\n";
    echo "<script>\n";
    echo "// Tracking de eventos personalizados para UVAMAYU\n";
    echo "function trackEvent(action, category, label, value) {\n";
    echo "  if (typeof gtag !== 'undefined') {\n";
    echo "    gtag('event', action, {\n";
    echo "      'event_category': category,\n";
    echo "      'event_label': label,\n";
    echo "      'value': value\n";
    echo "    });\n";
    echo "  }\n";
    echo "}\n\n";
    
    echo "// Tracking automático de interacciones\n";
    echo "document.addEventListener('DOMContentLoaded', function() {\n";
    echo "  // Track clicks en productos\n";
    echo "  document.querySelectorAll('.product-card').forEach(function(card) {\n";
    echo "    card.addEventListener('click', function() {\n";
    echo "      const productName = this.querySelector('.product-name')?.textContent || 'Unknown';\n";
    echo "      trackEvent('click', 'product', productName);\n";
    echo "    });\n";
    echo "  });\n\n";
    
    echo "  // Track envío de formulario de contacto\n";
    echo "  const contactForm = document.getElementById('contactForm');\n";
    echo "  if (contactForm) {\n";
    echo "    contactForm.addEventListener('submit', function() {\n";
    echo "      const producto = document.getElementById('producto')?.value || 'General';\n";
    echo "      const asunto = document.getElementById('asunto')?.value || 'General';\n";
    echo "      trackEvent('form_submit', 'contact', asunto + ' - ' + producto);\n";
    echo "    });\n";
    echo "  }\n\n";
    
    echo "  // Track clicks en redes sociales\n";
    echo "  document.querySelectorAll('.social-link, .social-btn').forEach(function(link) {\n";
    echo "    link.addEventListener('click', function() {\n";
    echo "      const platform = this.textContent.toLowerCase().trim();\n";
    echo "      trackEvent('click', 'social', platform);\n";
    echo "    });\n";
    echo "  });\n\n";
    
    echo "  // Track tiempo en página\n";
    echo "  let startTime = Date.now();\n";
    echo "  window.addEventListener('beforeunload', function() {\n";
    echo "    const timeSpent = Math.round((Date.now() - startTime) / 1000);\n";
    echo "    if (timeSpent > 10) { // Solo si estuvo más de 10 segundos\n";
    echo "      trackEvent('time_on_page', 'engagement', document.title, timeSpent);\n";
    echo "    }\n";
    echo "  });\n";
    echo "});\n";
    echo "</script>\n";
}

// Función para renderizar todo el tracking
function renderAllTracking() {
    renderAnalytics();
    renderEventTracking();
}
?>
