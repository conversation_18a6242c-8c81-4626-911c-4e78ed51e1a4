<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

// Obtener datos del formulario
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    $input = $_POST;
}

// Validar campos requeridos
$required_fields = ['nombre', 'email', 'asunto', 'mensaje'];
foreach ($required_fields as $field) {
    if (empty($input[$field])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => "El campo {$field} es requerido"]);
        exit;
    }
}

// Validar email
if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Email inválido']);
    exit;
}

// Crear el mensaje
$mensaje = [
    'id' => uniqid(),
    'fecha' => date('Y-m-d H:i:s'),
    'nombre' => htmlspecialchars(trim($input['nombre'])),
    'email' => htmlspecialchars(trim($input['email'])),
    'telefono' => htmlspecialchars(trim($input['telefono'] ?? '')),
    'producto' => htmlspecialchars(trim($input['producto'] ?? '')),
    'asunto' => htmlspecialchars(trim($input['asunto'])),
    'mensaje' => htmlspecialchars(trim($input['mensaje'])),
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
    'leido' => false
];

// Leer mensajes existentes
$mensajes_file = 'mensajes.json';
if (file_exists($mensajes_file)) {
    $data = json_decode(file_get_contents($mensajes_file), true);
} else {
    $data = ['mensajes' => []];
}

// Agregar nuevo mensaje
$data['mensajes'][] = $mensaje;

// Guardar en archivo
if (file_put_contents($mensajes_file, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
    echo json_encode([
        'success' => true, 
        'message' => '¡Mensaje enviado correctamente! Te contactaremos pronto 🎉',
        'id' => $mensaje['id']
    ]);
} else {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error al guardar el mensaje']);
}
?>
