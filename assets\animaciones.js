// Animaciones Premium para UVAMAYU
class AnimacionesPremium {
    constructor() {
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.setupParallaxEffects();
        this.setupHoverEffects();
        this.setupScrollAnimations();
        this.setupLoadingAnimations();
    }

    // Observer para animaciones al hacer scroll
    setupIntersectionObserver() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateElement(entry.target);
                }
            });
        }, observerOptions);

        // Observar elementos con clases de animación
        document.querySelectorAll('.animate-on-scroll, .animate-fade-in, .animate-slide-up, .animate-fade-in-left, .animate-fade-in-right').forEach(el => {
            observer.observe(el);
        });
    }

    // Animar elemento cuando entra en vista
    animateElement(element) {
        if (element.classList.contains('animate-fade-in')) {
            element.style.opacity = '0';
            element.style.transform = 'translateY(30px)';
            element.style.transition = 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            
            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, 100);
        }

        if (element.classList.contains('animate-slide-up')) {
            element.style.opacity = '0';
            element.style.transform = 'translateY(50px)';
            element.style.transition = 'all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            
            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, 150);
        }

        if (element.classList.contains('animate-fade-in-left')) {
            element.style.opacity = '0';
            element.style.transform = 'translateX(-40px)';
            element.style.transition = 'all 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            
            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateX(0)';
            }, 200);
        }

        if (element.classList.contains('animate-fade-in-right')) {
            element.style.opacity = '0';
            element.style.transform = 'translateX(40px)';
            element.style.transition = 'all 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            
            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateX(0)';
            }, 200);
        }
    }

    // Efectos parallax suaves
    setupParallaxEffects() {
        let ticking = false;

        const updateParallax = () => {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.parallax-element');
            
            parallaxElements.forEach(element => {
                const speed = element.dataset.speed || 0.5;
                const yPos = -(scrolled * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });
            
            ticking = false;
        };

        const requestTick = () => {
            if (!ticking) {
                requestAnimationFrame(updateParallax);
                ticking = true;
            }
        };

        window.addEventListener('scroll', requestTick);
    }

    // Efectos hover premium
    setupHoverEffects() {
        // Efecto hover para productos
        document.querySelectorAll('.producto-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-10px) scale(1.02)';
                card.style.boxShadow = '0 20px 50px rgba(136, 27, 53, 0.2)';
                card.style.borderColor = 'var(--oro-dorado)';
                
                // Animar imagen del producto
                const img = card.querySelector('img');
                if (img) {
                    img.style.transform = 'scale(1.05) rotate(2deg)';
                    img.style.filter = 'drop-shadow(0 0 20px rgba(161, 122, 50, 0.5))';
                }
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
                card.style.boxShadow = '0 8px 30px rgba(136, 27, 53, 0.1)';
                card.style.borderColor = 'transparent';
                
                const img = card.querySelector('img');
                if (img) {
                    img.style.transform = 'scale(1) rotate(0deg)';
                    img.style.filter = 'drop-shadow(0 0 15px rgba(161, 122, 50, 0.3))';
                }
            });
        });

        // Efecto hover para botones
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.transform = 'translateY(-3px) scale(1.05)';
            });
            
            btn.addEventListener('mouseleave', () => {
                btn.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Efecto hover para enlaces del navbar
        document.querySelectorAll('header nav a').forEach(link => {
            link.addEventListener('mouseenter', () => {
                link.style.transform = 'translateY(-2px)';
                link.style.textShadow = '0 0 10px rgba(161, 122, 50, 0.8)';
            });
            
            link.addEventListener('mouseleave', () => {
                link.style.transform = 'translateY(0)';
                link.style.textShadow = 'none';
            });
        });
    }

    // Animaciones basadas en scroll
    setupScrollAnimations() {
        let lastScrollTop = 0;
        
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            // Efecto parallax en el hero
            const heroContent = document.querySelector('.hero-content');
            if (heroContent) {
                const scrolled = window.pageYOffset;
                const rate = scrolled * -0.3;
                heroContent.style.transform = `translateY(${rate}px)`;
            }
            
            // Animación del header
            const header = document.getElementById('mainHeader');
            if (header) {
                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    // Scrolling down
                    header.style.transform = 'translateY(-100%)';
                } else {
                    // Scrolling up
                    header.style.transform = 'translateY(0)';
                }
            }
            
            lastScrollTop = scrollTop;
        });
    }

    // Animaciones de carga
    setupLoadingAnimations() {
        // Animación de entrada para elementos principales
        const mainElements = document.querySelectorAll('main > section, main > div');
        mainElements.forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(30px)';
            element.style.transition = 'all 0.8s ease-out';
            
            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, index * 200 + 300);
        });

        // Animación de typing para títulos principales
        this.setupTypingAnimation();
    }

    // Efecto de escritura para títulos
    setupTypingAnimation() {
        const typingElements = document.querySelectorAll('.typing-effect');
        
        typingElements.forEach(element => {
            const text = element.textContent;
            element.textContent = '';
            element.style.borderRight = '2px solid var(--oro-dorado)';
            element.style.animation = 'blink 1s infinite';
            
            let i = 0;
            const typeWriter = () => {
                if (i < text.length) {
                    element.textContent += text.charAt(i);
                    i++;
                    setTimeout(typeWriter, 100);
                } else {
                    element.style.borderRight = 'none';
                    element.style.animation = 'none';
                }
            };
            
            setTimeout(typeWriter, 1000);
        });
    }

    // Animación de números contadores
    animateCounters() {
        const counters = document.querySelectorAll('.counter');
        
        counters.forEach(counter => {
            const target = parseInt(counter.dataset.target);
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;
            
            const updateCounter = () => {
                current += step;
                if (current < target) {
                    counter.textContent = Math.floor(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };
            
            updateCounter();
        });
    }

    // Efecto de ondas al hacer click
    createRippleEffect(event) {
        const button = event.currentTarget;
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');
        
        button.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }
}

// Inicializar animaciones cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    new AnimacionesPremium();
    
    // Agregar efecto ripple a botones
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const animaciones = new AnimacionesPremium();
            animaciones.createRippleEffect(e);
        });
    });
});

// CSS para el efecto ripple (se inyecta dinámicamente)
const rippleCSS = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
    }
`;

// Inyectar CSS
const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);
