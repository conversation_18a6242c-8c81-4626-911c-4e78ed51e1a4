/* UVAMAYU - Sistema de Carga y Placeholders */

/* ========================================
   LOADER PRINCIPAL DE PÁGINA
======================================== */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--negro) 0%, var(--gris-oscuro) 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.page-loader.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader-content {
    text-align: center;
    animation: fadeInUp 0.8s ease-out;
}

.loader-logo {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    color: var(--dorado);
    margin-bottom: 1rem;
    text-shadow: 0 0 20px rgba(205, 170, 88, 0.5);
    animation: pulse 2s infinite;
}

.loader-subtitle {
    color: var(--guinda);
    font-size: 1.2rem;
    margin-bottom: 2rem;
    font-style: italic;
}

.loader-spinner {
    width: 60px;
    height: 60px;
    border: 3px solid rgba(205, 170, 88, 0.3);
    border-top: 3px solid var(--dorado);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.loader-progress {
    width: 200px;
    height: 4px;
    background: rgba(205, 170, 88, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin: 1rem auto;
}

.loader-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--dorado), var(--guinda));
    border-radius: 2px;
    width: 0%;
    animation: loadProgress 3s ease-out forwards;
}

/* ========================================
   PLACEHOLDERS PARA IMÁGENES
======================================== */
.image-container {
    position: relative;
    overflow: hidden;
    background: var(--gris-medio);
    border-radius: 8px;
}

.image-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--gris-medio) 0%, var(--gris-oscuro) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.5s ease;
    z-index: 2;
}

.image-placeholder.hidden {
    opacity: 0;
    pointer-events: none;
}

.placeholder-icon {
    font-size: 3rem;
    color: var(--dorado);
    opacity: 0.5;
    animation: pulse 2s infinite;
}

.lazy-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 0.5s ease;
    position: relative;
    z-index: 1;
}

.lazy-image.loaded {
    opacity: 1;
}

/* Efecto blur para carga progresiva */
.lazy-image.blur {
    filter: blur(10px);
    transform: scale(1.1);
    transition: filter 0.5s ease, transform 0.5s ease, opacity 0.5s ease;
}

.lazy-image.blur.loaded {
    filter: blur(0);
    transform: scale(1);
}

/* ========================================
   SKELETON LOADING PARA CONTENIDO
======================================== */
.skeleton {
    background: linear-gradient(90deg, var(--gris-medio) 25%, var(--gris-oscuro) 50%, var(--gris-medio) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
}

.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
}

.skeleton-text.large {
    height: 1.5rem;
}

.skeleton-text.small {
    height: 0.8rem;
    width: 60%;
}

.skeleton-image {
    width: 100%;
    height: 200px;
    margin-bottom: 1rem;
}

.skeleton-card {
    padding: 1rem;
    border-radius: 12px;
    background: var(--gris-medio);
}

/* ========================================
   ANIMACIONES
======================================== */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { 
        opacity: 1;
        transform: scale(1);
    }
    50% { 
        opacity: 0.7;
        transform: scale(1.05);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes loadProgress {
    0% { width: 0%; }
    20% { width: 30%; }
    50% { width: 60%; }
    80% { width: 85%; }
    100% { width: 100%; }
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* ========================================
   CARRUSEL CON LOADING (REMOVIDO - NO INTERFERIR)
======================================== */
/* El carrusel maneja sus propias imágenes de fondo */

/* ========================================
   RESPONSIVE
======================================== */
@media (max-width: 768px) {
    .loader-logo {
        font-size: 2rem;
    }
    
    .loader-subtitle {
        font-size: 1rem;
    }
    
    .loader-spinner {
        width: 40px;
        height: 40px;
    }
    
    .loader-progress {
        width: 150px;
    }
    
    .placeholder-icon {
        font-size: 2rem;
    }
}

/* ========================================
   ESTADOS DE CARGA ESPECÍFICOS
======================================== */
.loading-state {
    pointer-events: none;
    user-select: none;
}

.content-loading {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.content-loaded {
    opacity: 1;
}

/* Efecto shimmer para elementos de carga */
.shimmer {
    background: linear-gradient(90deg, 
        rgba(205, 170, 88, 0.1) 0%, 
        rgba(205, 170, 88, 0.3) 50%, 
        rgba(205, 170, 88, 0.1) 100%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}
