# UVAMAYU - Configuración mínima
RewriteEngine On

# Página principal
DirectoryIndex index.php

# Activar errores para debug
php_flag display_errors On
php_flag display_startup_errors On

# APIs básicas
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/([^/]+)/(.*)$ api/$1.php/$2 [L,QSA]

# CORS básico
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
</IfModule>
