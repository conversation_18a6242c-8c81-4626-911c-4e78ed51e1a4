<?php
// Página interna para revisar mensajes de contacto
$page_title = "Mensajes de Contacto - Panel Interno";

// Leer mensajes
$mensajes_file = 'mensajes.json';
$mensajes = [];
if (file_exists($mensajes_file)) {
    $data = json_decode(file_get_contents($mensajes_file), true);
    $mensajes = $data['mensajes'] ?? [];
}

// Ordenar por fecha (más recientes primero)
usort($mensajes, function($a, $b) {
    return strtotime($b['fecha']) - strtotime($a['fecha']);
});

// Marcar como leído si se especifica
if (isset($_GET['marcar_leido']) && isset($_GET['id'])) {
    $id = $_GET['id'];
    foreach ($mensajes as &$mensaje) {
        if ($mensaje['id'] === $id) {
            $mensaje['leido'] = true;
            break;
        }
    }
    $data['mensajes'] = $mensajes;
    file_put_contents($mensajes_file, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    header('Location: comentarios_internos.php');
    exit;
}

// Estadísticas
$total_mensajes = count($mensajes);
$mensajes_no_leidos = count(array_filter($mensajes, function($m) { return !$m['leido']; }));
$mensajes_hoy = count(array_filter($mensajes, function($m) { 
    return date('Y-m-d', strtotime($m['fecha'])) === date('Y-m-d'); 
}));
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="/styles.css">
    <style>
        body {
            background: var(--gris-oscuro);
            padding: 2rem;
        }
        
        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .admin-header {
            background: var(--negro);
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .admin-title {
            font-family: 'Playfair Display', serif;
            font-size: 2.5rem;
            color: var(--dorado);
            margin-bottom: 1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: var(--gris-medio);
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--dorado);
        }
        
        .stat-label {
            color: #ccc;
            margin-top: 0.5rem;
        }
        
        .messages-container {
            display: grid;
            gap: 1.5rem;
        }
        
        .message-card {
            background: var(--negro);
            border-radius: 12px;
            padding: 2rem;
            border-left: 4px solid var(--dorado);
            transition: all 0.3s ease;
        }
        
        .message-card.unread {
            border-left-color: var(--guinda);
            box-shadow: 0 0 20px rgba(111, 43, 48, 0.3);
        }
        
        .message-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        
        .message-header {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1rem;
            align-items: start;
            margin-bottom: 1rem;
        }
        
        .message-info h3 {
            color: var(--dorado);
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
        }
        
        .message-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            color: #999;
            font-size: 0.9rem;
        }
        
        .message-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-small {
            padding: 0.5rem 1rem;
            border-radius: 5px;
            text-decoration: none;
            font-size: 0.8rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-read {
            background: var(--verde);
            color: white;
        }
        
        .btn-read:hover {
            background: var(--dorado);
            color: var(--negro);
        }
        
        .btn-contact {
            background: var(--dorado);
            color: var(--negro);
        }
        
        .btn-contact:hover {
            background: var(--guinda);
            color: white;
        }
        
        .message-content {
            margin-bottom: 1rem;
        }
        
        .message-subject {
            background: var(--gris-medio);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .message-subject strong {
            color: var(--dorado);
        }
        
        .message-text {
            background: var(--gris-oscuro);
            padding: 1.5rem;
            border-radius: 8px;
            line-height: 1.6;
            color: #ccc;
        }
        
        .message-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--gris-medio);
        }
        
        .detail-item {
            color: #999;
            font-size: 0.9rem;
        }
        
        .detail-item strong {
            color: var(--dorado);
        }
        
        .no-messages {
            text-align: center;
            padding: 4rem;
            color: #999;
        }
        
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-unread {
            background: var(--guinda);
            color: white;
        }
        
        .status-read {
            background: var(--verde);
            color: white;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .message-header {
                grid-template-columns: 1fr;
            }
            
            .message-actions {
                justify-content: center;
            }
            
            .message-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1 class="admin-title">📧 Panel de Mensajes UVAMAYU</h1>
            <p>Gestión interna de contactos y consultas</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_mensajes; ?></div>
                <div class="stat-label">Total Mensajes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $mensajes_no_leidos; ?></div>
                <div class="stat-label">Sin Leer</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $mensajes_hoy; ?></div>
                <div class="stat-label">Hoy</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo date('d/m/Y'); ?></div>
                <div class="stat-label">Fecha Actual</div>
            </div>
        </div>
        
        <div class="messages-container">
            <?php if (empty($mensajes)): ?>
                <div class="no-messages">
                    <h3>📭 No hay mensajes aún</h3>
                    <p>Los mensajes de contacto aparecerán aquí cuando los usuarios envíen el formulario.</p>
                </div>
            <?php else: ?>
                <?php foreach ($mensajes as $mensaje): ?>
                    <div class="message-card <?php echo !$mensaje['leido'] ? 'unread' : ''; ?>">
                        <div class="message-header">
                            <div class="message-info">
                                <h3><?php echo $mensaje['nombre']; ?></h3>
                                <div class="message-meta">
                                    <span>📅 <?php echo date('d/m/Y H:i', strtotime($mensaje['fecha'])); ?></span>
                                    <span>📧 <?php echo $mensaje['email']; ?></span>
                                    <?php if (!empty($mensaje['telefono'])): ?>
                                        <span>📱 <?php echo $mensaje['telefono']; ?></span>
                                    <?php endif; ?>
                                    <span class="status-badge <?php echo !$mensaje['leido'] ? 'status-unread' : 'status-read'; ?>">
                                        <?php echo !$mensaje['leido'] ? '🔴 Nuevo' : '✅ Leído'; ?>
                                    </span>
                                </div>
                            </div>
                            <div class="message-actions">
                                <?php if (!$mensaje['leido']): ?>
                                    <a href="?marcar_leido=1&id=<?php echo $mensaje['id']; ?>" class="btn-small btn-read">
                                        ✓ Marcar Leído
                                    </a>
                                <?php endif; ?>
                                <a href="mailto:<?php echo $mensaje['email']; ?>" class="btn-small btn-contact">
                                    📧 Responder
                                </a>
                            </div>
                        </div>
                        
                        <div class="message-content">
                            <div class="message-subject">
                                <strong>Asunto:</strong> <?php echo $mensaje['asunto']; ?>
                                <?php if (!empty($mensaje['producto'])): ?>
                                    <br><strong>Producto:</strong> <?php echo $mensaje['producto']; ?>
                                <?php endif; ?>
                            </div>
                            
                            <div class="message-text">
                                <?php echo nl2br($mensaje['mensaje']); ?>
                            </div>
                        </div>
                        
                        <div class="message-details">
                            <div class="detail-item">
                                <strong>ID:</strong> <?php echo $mensaje['id']; ?>
                            </div>
                            <div class="detail-item">
                                <strong>IP:</strong> <?php echo $mensaje['ip']; ?>
                            </div>
                            <div class="detail-item">
                                <strong>Navegador:</strong> <?php echo substr($mensaje['user_agent'], 0, 50) . '...'; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
