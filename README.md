# 🍷 UVAMAYU - Renovación Premium Completa

## 🎨 Descripción del Proyecto

UVAMAYU es un sitio web premium para una bodega familiar peruana especializada en pisco y vino artesanal. Esta renovación completa transforma el sitio en una experiencia premium que refleja la calidad y tradición de la marca desde 1961.

## ✨ Características Implementadas

### 🎨 Diseño Premium
- **Paleta de colores premium**: <PERSON>o <PERSON> (#881B35), oro dorado (#A17A32), verde hoja (#5C7330)
- **Tipografía elegante**: Google Fonts Cinzel para un look sofisticado
- **Efectos visuales**: Gradientes, sombras premium, y efectos de glow
- **Animaciones sutiles**: Fade-in, slide-up, parallax suave

### 🛒 Sistema de Carrito Mejorado
- **Modal interactivo**: Carrito con resumen completo de productos
- **Gestión de productos**: Agregar, eliminar, modificar cantidades
- **Integración directa**: Redirección automática a WhatsApp y correo
- **Persistencia**: LocalStorage para mantener el carrito entre sesiones
- **Contador visual**: Indicador en tiempo real en el header

### 📱 Responsive Optimizado
- **Mobile-first**: Diseño optimizado para móviles 16:9 vertical
- **Breakpoints inteligentes**: Adaptación perfecta en todos los dispositivos
- **Navegación móvil**: Header responsive con menú optimizado
- **Grids adaptativos**: Productos y contenido se ajustan automáticamente

### 🌟 Header Transparente Premium
- **Efecto glassmorphism**: Header transparente con blur sobre el carrusel
- **Transición suave**: Cambio gradual al hacer scroll
- **Auto-hide**: Se oculta al hacer scroll hacia abajo rápido
- **Efectos de partículas**: Animaciones sutiles de fondo

### 🎠 Carrusel Pantalla Completa
- **100vh/100vw**: Ocupa toda la pantalla inicial
- **4 banners**: Incluyendo nuevo banner de tradición familiar
- **Controles premium**: Navegación con dots y flechas elegantes
- **Auto-play inteligente**: Pausa en hover, transiciones suaves

### 🎭 Animaciones Premium
- **Intersection Observer**: Animaciones al entrar en vista
- **Efectos hover**: Transformaciones elegantes en productos y botones
- **Parallax sutil**: Efectos de profundidad sin mareo
- **Transiciones CSS**: Cubic-bezier para movimientos naturales

## 📁 Estructura de Archivos

```
UVAMAYU/
├── assets/
│   ├── estilos.css          # CSS principal con paleta premium
│   ├── carrito.js           # Sistema de carrito mejorado
│   ├── animaciones.js       # Animaciones premium
│   ├── banner1.png          # Banner principal
│   ├── banner2.png          # Banner fiestas patrias
│   ├── banner3.png          # Banner productos
│   └── banner4.png          # Banner tradición (NUEVO)
├── index.php                # Página principal renovada
├── productos.php            # Catálogo de productos mejorado
├── producto.php             # Página individual de producto
├── contacto.php             # Formulario de contacto premium
├── historia.php             # Historia de la empresa
├── header.php               # Header con navbar transparente
├── footer.php               # Footer premium renovado
├── config.example.php       # Configuración de ejemplo
├── conexion.php             # Conexión a base de datos
├── db.sql                   # Estructura de base de datos
├── test.html                # Página de pruebas
└── ESPECIFICACIONES_IMAGENES.md # Guía para nuevas imágenes
```

## 🚀 Instalación

### 1. Requisitos
- PHP 7.4 o superior
- MySQL 5.7 o superior
- Servidor web (Apache/Nginx)

### 2. Configuración de Base de Datos
```sql
-- Importar el archivo db.sql
mysql -u usuario -p nombre_bd < db.sql
```

### 3. Configuración del Sitio
```bash
# Copiar archivo de configuración
cp config.example.php config.php

# Editar configuración
nano config.php
```

### 4. Configurar config.php
```php
return [
    'db_host' => 'localhost',
    'db_name' => 'uvamayu',
    'db_user' => 'tu_usuario',
    'db_pass' => 'tu_contraseña',
    'correo_contacto' => '<EMAIL>',
    'whatsapp' => '+51 999 999 999',
    // ... más configuraciones
];
```

## 🎨 Paleta de Colores Premium

| Color | Código HEX | Uso Principal |
|-------|------------|---------------|
| Vino Borgoña | `#881B35` | Color principal de identidad |
| Oro Dorado | `#A17A32` | Elegancia y tradición |
| Verde Hoja | `#5C7330` | Natural y artesanal |
| Negro Profundo | `#000000` | Fondo premium |
| Blanco Puro | `#FFFFFF` | Contraste y limpieza |

## 📱 Responsive Breakpoints

- **Mobile**: 320px - 767px
- **Tablet**: 768px - 1023px
- **Desktop**: 1024px+

Optimizado especialmente para móviles con relación de aspecto 16:9 vertical.

## 🛒 Funcionalidades del Carrito

### Agregar Productos
```javascript
// Agregar producto al carrito
agregarAlCarrito(productoId, cantidad);

// El sistema automáticamente:
// - Actualiza el contador del header
// - Guarda en localStorage
// - Muestra notificación
```

### Modal del Carrito
- Resumen completo de productos
- Modificar cantidades
- Eliminar productos
- Calcular total automático
- Botones de compra directa

### Integración WhatsApp/Email
- Genera mensaje automático con resumen
- Incluye productos, cantidades y total
- Redirección directa a aplicaciones

## ✨ Animaciones Implementadas

### Clases CSS Disponibles
```css
.animate-fade-in        /* Aparición suave */
.animate-slide-up       /* Deslizamiento hacia arriba */
.animate-fade-in-left   /* Aparición desde izquierda */
.animate-fade-in-right  /* Aparición desde derecha */
.premium-glow           /* Efecto de brillo premium */
```

### JavaScript
- Intersection Observer para animaciones en scroll
- Efectos hover premium
- Parallax sutil
- Partículas de fondo

## 🖼️ Especificaciones de Imágenes

Ver archivo `ESPECIFICACIONES_IMAGENES.md` para:
- Relaciones de aspecto requeridas
- Prompts sugeridos para IA
- Paleta de colores a usar
- Formatos y optimización

## 🧪 Testing

Abrir `test.html` en el navegador para verificar:
- ✅ Paleta de colores
- ✅ Sistema de carrito
- ✅ Responsive design
- ✅ Animaciones
- ✅ Formularios

## 📞 Soporte

Para soporte técnico o consultas sobre la implementación:
- Revisar el archivo `test.html` para diagnósticos
- Verificar la consola del navegador para errores
- Comprobar la configuración en `config.php`

## 🎯 Próximos Pasos Recomendados

1. **Imágenes**: Generar las imágenes según especificaciones
2. **Contenido**: Agregar productos reales a la base de datos
3. **SEO**: Implementar meta tags y optimización
4. **Analytics**: Configurar Google Analytics
5. **SSL**: Configurar certificado SSL
6. **CDN**: Implementar CDN para mejor rendimiento

---

**UVAMAYU** - Espíritu del Valle, Orgullo del Perú 🇵🇪  
*Desde 1961, tradición familiar en cada copa*
