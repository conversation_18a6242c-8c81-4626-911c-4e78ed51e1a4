<?php
/**
 * API para gestión de presupuesto, inversiones y gastos
 * Control financiero completo de UVAMAYU
 */

require_once 'config/Database.php';
require_once 'config/ApiResponse.php';

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $path_info = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
    
    // Extraer acción de la URL
    $action = '';
    $id = null;
    if ($path_info) {
        $parts = explode('/', trim($path_info, '/'));
        $action = $parts[0] ?? '';
        $id = isset($parts[1]) ? (int)$parts[1] : null;
    }
    
    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'dashboard':
                    getDashboardFinanciero($db);
                    break;
                case 'inversores':
                    getInversores($db);
                    break;
                case 'inversiones':
                    getInversiones($db);
                    break;
                case 'gastos':
                    getGastos($db);
                    break;
                case 'resumen-mensual':
                    getResumenMensual($db);
                    break;
                case 'saldo-disponible':
                    getSaldoDisponible($db);
                    break;
                default:
                    ApiResponse::error("Acción no válida", 400);
            }
            break;
            
        case 'POST':
            switch ($action) {
                case 'inversion':
                    registrarInversion($db);
                    break;
                case 'gasto':
                    registrarGasto($db);
                    break;
                case 'inversor':
                    crearInversor($db);
                    break;
                default:
                    ApiResponse::error("Acción no válida", 400);
            }
            break;
            
        case 'PUT':
            if (!$id) {
                ApiResponse::error("ID requerido para actualizar", 400);
            }
            switch ($action) {
                case 'inversion':
                    actualizarInversion($db, $id);
                    break;
                case 'gasto':
                    actualizarGasto($db, $id);
                    break;
                default:
                    ApiResponse::error("Acción no válida", 400);
            }
            break;
            
        case 'DELETE':
            if (!$id) {
                ApiResponse::error("ID requerido para eliminar", 400);
            }
            switch ($action) {
                case 'inversion':
                    eliminarInversion($db, $id);
                    break;
                case 'gasto':
                    eliminarGasto($db, $id);
                    break;
                default:
                    ApiResponse::error("Acción no válida", 400);
            }
            break;
            
        default:
            ApiResponse::error("Método no permitido", 405);
    }
    
} catch (Exception $e) {
    error_log("Error en API presupuesto: " . $e->getMessage());
    ApiResponse::serverError();
}

/**
 * Obtener dashboard financiero completo
 */
function getDashboardFinanciero($db) {
    try {
        $query = "SELECT * FROM vista_dashboard_financiero";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $dashboard = $stmt->fetch();
        
        // Obtener desglose de gastos por inversor (mes actual)
        $queryInversores = "SELECT 
                               i.nombre,
                               COALESCE(SUM(g.monto), 0) as total_gastado_mes,
                               COALESCE(SUM(inv.monto), 0) as total_invertido
                            FROM inversores i
                            LEFT JOIN gastos g ON i.id = g.inversor_id 
                                AND YEAR(g.fecha_gasto) = YEAR(CURDATE()) 
                                AND MONTH(g.fecha_gasto) = MONTH(CURDATE())
                            LEFT JOIN inversiones inv ON i.id = inv.inversor_id
                            WHERE i.activo = TRUE
                            GROUP BY i.id, i.nombre";
        
        $stmtInversores = $db->prepare($queryInversores);
        $stmtInversores->execute();
        $dashboard['desglose_inversores'] = $stmtInversores->fetchAll();
        
        // Calcular saldo disponible
        $totalInversiones = $dashboard['total_inversiones'];
        $totalGastos = $dashboard['gastos_etiquetas_mes'] + 
                      $dashboard['gastos_botellas_mes'] + 
                      $dashboard['gastos_producto_mes'];
        
        $dashboard['saldo_disponible'] = $totalInversiones - $totalGastos;
        $dashboard['utilidad_mes'] = $dashboard['ventas_mes'] - $totalGastos;
        
        ApiResponse::success($dashboard, "Dashboard financiero obtenido");
        
    } catch (Exception $e) {
        error_log("Error obteniendo dashboard: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Obtener inversores
 */
function getInversores($db) {
    try {
        $query = "SELECT i.*,
                         COALESCE(SUM(inv.monto), 0) as total_invertido,
                         COALESCE(SUM(g.monto), 0) as total_gastado
                  FROM inversores i
                  LEFT JOIN inversiones inv ON i.id = inv.inversor_id AND inv.activo = TRUE
                  LEFT JOIN gastos g ON i.id = g.inversor_id AND g.activo = TRUE
                  WHERE i.activo = TRUE
                  GROUP BY i.id
                  ORDER BY i.nombre";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $inversores = $stmt->fetchAll();
        
        ApiResponse::success($inversores, "Inversores obtenidos exitosamente");
        
    } catch (Exception $e) {
        error_log("Error obteniendo inversores: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Registrar nueva inversión
 */
function registrarInversion($db) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        ApiResponse::validateRequired($data, ['inversor_id', 'monto', 'concepto', 'fecha_inversion', 'tipo_inversion']);
        
        if ($data['monto'] <= 0) {
            ApiResponse::validationError(['monto' => 'El monto debe ser mayor a 0']);
        }
        
        $query = "INSERT INTO inversiones (inversor_id, monto, concepto, fecha_inversion, tipo_inversion, notas) 
                  VALUES (:inversor_id, :monto, :concepto, :fecha_inversion, :tipo_inversion, :notas)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':inversor_id', $data['inversor_id']);
        $stmt->bindParam(':monto', $data['monto']);
        $stmt->bindParam(':concepto', $data['concepto']);
        $stmt->bindParam(':fecha_inversion', $data['fecha_inversion']);
        $stmt->bindParam(':tipo_inversion', $data['tipo_inversion']);
        $stmt->bindParam(':notas', $data['notas'] ?? '');
        
        if ($stmt->execute()) {
            $newId = $db->lastInsertId();
            ApiResponse::success(['id' => $newId], "Inversión registrada exitosamente", 201);
        } else {
            ApiResponse::serverError("Error al registrar la inversión");
        }
        
    } catch (Exception $e) {
        error_log("Error registrando inversión: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Registrar nuevo gasto
 */
function registrarGasto($db) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        ApiResponse::validateRequired($data, ['categoria', 'monto', 'fecha_gasto', 'descripcion']);
        
        if ($data['monto'] <= 0) {
            ApiResponse::validationError(['monto' => 'El monto debe ser mayor a 0']);
        }
        
        $categoriasValidas = ['etiquetas', 'botellas_tapones_capsulas', 'producto_liquido', 'otros'];
        if (!in_array($data['categoria'], $categoriasValidas)) {
            ApiResponse::validationError(['categoria' => 'Categoría inválida']);
        }
        
        $query = "INSERT INTO gastos (categoria, subcategoria, monto, cantidad, precio_unitario, proveedor, fecha_gasto, descripcion, inversor_id, comprobante) 
                  VALUES (:categoria, :subcategoria, :monto, :cantidad, :precio_unitario, :proveedor, :fecha_gasto, :descripcion, :inversor_id, :comprobante)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':categoria', $data['categoria']);
        $stmt->bindParam(':subcategoria', $data['subcategoria'] ?? '');
        $stmt->bindParam(':monto', $data['monto']);
        $stmt->bindParam(':cantidad', $data['cantidad'] ?? null);
        $stmt->bindParam(':precio_unitario', $data['precio_unitario'] ?? null);
        $stmt->bindParam(':proveedor', $data['proveedor'] ?? '');
        $stmt->bindParam(':fecha_gasto', $data['fecha_gasto']);
        $stmt->bindParam(':descripcion', $data['descripcion']);
        $stmt->bindParam(':inversor_id', $data['inversor_id'] ?? null);
        $stmt->bindParam(':comprobante', $data['comprobante'] ?? '');
        
        if ($stmt->execute()) {
            $newId = $db->lastInsertId();
            
            // Si se especificó cantidad y es una compra de inventario, actualizar stock
            if (isset($data['actualizar_stock']) && $data['actualizar_stock'] && isset($data['cantidad'])) {
                actualizarStockPorGasto($db, $data);
            }
            
            ApiResponse::success(['id' => $newId], "Gasto registrado exitosamente", 201);
        } else {
            ApiResponse::serverError("Error al registrar el gasto");
        }
        
    } catch (Exception $e) {
        error_log("Error registrando gasto: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Actualizar stock basado en gasto (opcional)
 */
function actualizarStockPorGasto($db, $data) {
    try {
        switch ($data['categoria']) {
            case 'etiquetas':
                if (isset($data['etiqueta_id'])) {
                    $query = "UPDATE etiquetas SET stock_cantidad = stock_cantidad + :cantidad WHERE id = :id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':cantidad', $data['cantidad']);
                    $stmt->bindParam(':id', $data['etiqueta_id']);
                    $stmt->execute();
                }
                break;
                
            case 'botellas_tapones_capsulas':
                if (isset($data['componente_tipo']) && isset($data['componente_id'])) {
                    $tablas = [
                        'botella' => 'botellas',
                        'tapon' => 'tapones',
                        'capsula' => 'capsulas'
                    ];
                    
                    if (isset($tablas[$data['componente_tipo']])) {
                        $tabla = $tablas[$data['componente_tipo']];
                        $query = "UPDATE $tabla SET stock_cantidad = stock_cantidad + :cantidad WHERE id = :id";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':cantidad', $data['cantidad']);
                        $stmt->bindParam(':id', $data['componente_id']);
                        $stmt->execute();
                    }
                }
                break;
                
            case 'producto_liquido':
                if (isset($data['liquido_id'])) {
                    $query = "UPDATE liquidos SET stock_ml = stock_ml + :cantidad WHERE id = :id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':cantidad', $data['cantidad']); // En ml
                    $stmt->bindParam(':id', $data['liquido_id']);
                    $stmt->execute();
                }
                break;
        }
    } catch (Exception $e) {
        error_log("Error actualizando stock por gasto: " . $e->getMessage());
    }
}

/**
 * Obtener inversiones con filtros
 */
function getInversiones($db) {
    try {
        $fechaDesde = $_GET['fecha_desde'] ?? null;
        $fechaHasta = $_GET['fecha_hasta'] ?? null;
        $inversorId = $_GET['inversor_id'] ?? null;
        $limite = (int)($_GET['limite'] ?? 100);
        
        $filtros = [];
        $params = [];
        
        if ($fechaDesde) {
            $filtros[] = "inv.fecha_inversion >= :fecha_desde";
            $params[':fecha_desde'] = $fechaDesde;
        }
        
        if ($fechaHasta) {
            $filtros[] = "inv.fecha_inversion <= :fecha_hasta";
            $params[':fecha_hasta'] = $fechaHasta;
        }
        
        if ($inversorId) {
            $filtros[] = "inv.inversor_id = :inversor_id";
            $params[':inversor_id'] = $inversorId;
        }
        
        $whereClause = !empty($filtros) ? 'WHERE ' . implode(' AND ', $filtros) . ' AND inv.activo = TRUE' : 'WHERE inv.activo = TRUE';
        
        $query = "SELECT inv.*, i.nombre as inversor_nombre
                  FROM inversiones inv
                  JOIN inversores i ON inv.inversor_id = i.id
                  $whereClause
                  ORDER BY inv.fecha_inversion DESC, inv.created_at DESC
                  LIMIT :limite";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':limite', $limite, PDO::PARAM_INT);
        
        foreach ($params as $key => $value) {
            $stmt->bindParam($key, $value);
        }
        
        $stmt->execute();
        $inversiones = $stmt->fetchAll();
        
        ApiResponse::success($inversiones, "Inversiones obtenidas exitosamente");
        
    } catch (Exception $e) {
        error_log("Error obteniendo inversiones: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Obtener gastos con filtros
 */
function getGastos($db) {
    try {
        $fechaDesde = $_GET['fecha_desde'] ?? null;
        $fechaHasta = $_GET['fecha_hasta'] ?? null;
        $categoria = $_GET['categoria'] ?? null;
        $inversorId = $_GET['inversor_id'] ?? null;
        $limite = (int)($_GET['limite'] ?? 100);
        
        $filtros = [];
        $params = [];
        
        if ($fechaDesde) {
            $filtros[] = "g.fecha_gasto >= :fecha_desde";
            $params[':fecha_desde'] = $fechaDesde;
        }
        
        if ($fechaHasta) {
            $filtros[] = "g.fecha_gasto <= :fecha_hasta";
            $params[':fecha_hasta'] = $fechaHasta;
        }
        
        if ($categoria) {
            $filtros[] = "g.categoria = :categoria";
            $params[':categoria'] = $categoria;
        }
        
        if ($inversorId) {
            $filtros[] = "g.inversor_id = :inversor_id";
            $params[':inversor_id'] = $inversorId;
        }
        
        $whereClause = !empty($filtros) ? 'WHERE ' . implode(' AND ', $filtros) . ' AND g.activo = TRUE' : 'WHERE g.activo = TRUE';
        
        $query = "SELECT g.*, i.nombre as inversor_nombre
                  FROM gastos g
                  LEFT JOIN inversores i ON g.inversor_id = i.id
                  $whereClause
                  ORDER BY g.fecha_gasto DESC, g.created_at DESC
                  LIMIT :limite";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':limite', $limite, PDO::PARAM_INT);
        
        foreach ($params as $key => $value) {
            $stmt->bindParam($key, $value);
        }
        
        $stmt->execute();
        $gastos = $stmt->fetchAll();
        
        ApiResponse::success($gastos, "Gastos obtenidos exitosamente");
        
    } catch (Exception $e) {
        error_log("Error obteniendo gastos: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Obtener resumen mensual
 */
function getResumenMensual($db) {
    try {
        $año = (int)($_GET['año'] ?? date('Y'));
        $mes = (int)($_GET['mes'] ?? date('n'));
        
        $query = "SELECT 
                    -- Inversiones del mes
                    COALESCE(SUM(CASE WHEN YEAR(inv.fecha_inversion) = :año AND MONTH(inv.fecha_inversion) = :mes THEN inv.monto ELSE 0 END), 0) as inversiones_mes,
                    
                    -- Gastos por categoría del mes
                    COALESCE(SUM(CASE WHEN YEAR(g.fecha_gasto) = :año AND MONTH(g.fecha_gasto) = :mes AND g.categoria = 'etiquetas' THEN g.monto ELSE 0 END), 0) as gastos_etiquetas,
                    COALESCE(SUM(CASE WHEN YEAR(g.fecha_gasto) = :año AND MONTH(g.fecha_gasto) = :mes AND g.categoria = 'botellas_tapones_capsulas' THEN g.monto ELSE 0 END), 0) as gastos_botellas,
                    COALESCE(SUM(CASE WHEN YEAR(g.fecha_gasto) = :año AND MONTH(g.fecha_gasto) = :mes AND g.categoria = 'producto_liquido' THEN g.monto ELSE 0 END), 0) as gastos_producto,
                    COALESCE(SUM(CASE WHEN YEAR(g.fecha_gasto) = :año AND MONTH(g.fecha_gasto) = :mes AND g.categoria = 'otros' THEN g.monto ELSE 0 END), 0) as gastos_otros,
                    
                    -- Ventas del mes
                    COALESCE(SUM(CASE WHEN YEAR(v.fecha_venta) = :año AND MONTH(v.fecha_venta) = :mes AND v.es_regalo = FALSE THEN v.total ELSE 0 END), 0) as ventas_mes,
                    COALESCE(SUM(CASE WHEN YEAR(v.fecha_venta) = :año AND MONTH(v.fecha_venta) = :mes AND v.es_regalo = TRUE THEN v.total ELSE 0 END), 0) as regalos_mes
                  FROM inversiones inv
                  CROSS JOIN gastos g
                  CROSS JOIN ventas v
                  WHERE inv.activo = TRUE AND g.activo = TRUE";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':año', $año);
        $stmt->bindParam(':mes', $mes);
        $stmt->execute();
        
        $resumen = $stmt->fetch();
        
        // Calcular totales
        $resumen['total_gastos'] = $resumen['gastos_etiquetas'] + $resumen['gastos_botellas'] + $resumen['gastos_producto'] + $resumen['gastos_otros'];
        $resumen['utilidad_mes'] = $resumen['ventas_mes'] - $resumen['total_gastos'];
        $resumen['flujo_neto'] = $resumen['inversiones_mes'] + $resumen['ventas_mes'] - $resumen['total_gastos'];
        
        $resumen['periodo'] = [
            'mes' => $mes,
            'año' => $año,
            'nombre_mes' => date('F', mktime(0, 0, 0, $mes, 1))
        ];
        
        ApiResponse::success($resumen, "Resumen mensual obtenido");
        
    } catch (Exception $e) {
        error_log("Error obteniendo resumen mensual: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Obtener saldo disponible actual
 */
function getSaldoDisponible($db) {
    try {
        $query = "SELECT 
                    COALESCE(SUM(inv.monto), 0) as total_inversiones,
                    COALESCE(SUM(g.monto), 0) as total_gastos,
                    COALESCE(SUM(v.total), 0) as total_ventas,
                    (COALESCE(SUM(inv.monto), 0) + COALESCE(SUM(v.total), 0) - COALESCE(SUM(g.monto), 0)) as saldo_disponible
                  FROM inversiones inv
                  CROSS JOIN gastos g
                  CROSS JOIN ventas v
                  WHERE inv.activo = TRUE AND g.activo = TRUE AND v.es_regalo = FALSE";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $saldo = $stmt->fetch();
        
        ApiResponse::success($saldo, "Saldo disponible obtenido");
        
    } catch (Exception $e) {
        error_log("Error obteniendo saldo: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Crear nuevo inversor
 */
function crearInversor($db) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        ApiResponse::validateRequired($data, ['nombre', 'tipo']);
        
        if (!in_array($data['tipo'], ['persona', 'empresa'])) {
            ApiResponse::validationError(['tipo' => 'Tipo debe ser persona o empresa']);
        }
        
        $query = "INSERT INTO inversores (nombre, tipo) VALUES (:nombre, :tipo)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':nombre', $data['nombre']);
        $stmt->bindParam(':tipo', $data['tipo']);
        
        if ($stmt->execute()) {
            $newId = $db->lastInsertId();
            ApiResponse::success(['id' => $newId], "Inversor creado exitosamente", 201);
        } else {
            ApiResponse::serverError("Error al crear el inversor");
        }
        
    } catch (Exception $e) {
        error_log("Error creando inversor: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Actualizar inversión
 */
function actualizarInversion($db, $id) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        $fields = [];
        $params = [':id' => $id];
        
        $allowedFields = ['monto', 'concepto', 'fecha_inversion', 'tipo_inversion', 'notas'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = :$field";
                $params[":$field"] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            ApiResponse::error("No hay campos para actualizar", 400);
        }
        
        $query = "UPDATE inversiones SET " . implode(', ', $fields) . " WHERE id = :id AND activo = TRUE";
        $stmt = $db->prepare($query);
        
        if ($stmt->execute($params) && $stmt->rowCount() > 0) {
            ApiResponse::success(null, "Inversión actualizada exitosamente");
        } else {
            ApiResponse::notFound("Inversión no encontrada");
        }
        
    } catch (Exception $e) {
        error_log("Error actualizando inversión: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Actualizar gasto
 */
function actualizarGasto($db, $id) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        $fields = [];
        $params = [':id' => $id];
        
        $allowedFields = ['categoria', 'subcategoria', 'monto', 'cantidad', 'precio_unitario', 'proveedor', 'fecha_gasto', 'descripcion', 'inversor_id', 'comprobante'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = :$field";
                $params[":$field"] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            ApiResponse::error("No hay campos para actualizar", 400);
        }
        
        $query = "UPDATE gastos SET " . implode(', ', $fields) . " WHERE id = :id AND activo = TRUE";
        $stmt = $db->prepare($query);
        
        if ($stmt->execute($params) && $stmt->rowCount() > 0) {
            ApiResponse::success(null, "Gasto actualizado exitosamente");
        } else {
            ApiResponse::notFound("Gasto no encontrado");
        }
        
    } catch (Exception $e) {
        error_log("Error actualizando gasto: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Eliminar inversión (soft delete)
 */
function eliminarInversion($db, $id) {
    try {
        $query = "UPDATE inversiones SET activo = FALSE WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id);
        
        if ($stmt->execute() && $stmt->rowCount() > 0) {
            ApiResponse::success(null, "Inversión eliminada exitosamente");
        } else {
            ApiResponse::notFound("Inversión no encontrada");
        }
        
    } catch (Exception $e) {
        error_log("Error eliminando inversión: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Eliminar gasto (soft delete)
 */
function eliminarGasto($db, $id) {
    try {
        $query = "UPDATE gastos SET activo = FALSE WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id);
        
        if ($stmt->execute() && $stmt->rowCount() > 0) {
            ApiResponse::success(null, "Gasto eliminado exitosamente");
        } else {
            ApiResponse::notFound("Gasto no encontrado");
        }
        
    } catch (Exception $e) {
        error_log("Error eliminando gasto: " . $e->getMessage());
        ApiResponse::serverError();
    }
}
?>
