/* UVAMAYU - Estilos Premium */
:root {
    --dorado: #CDAA58;
    --guinda: #6F2B30;
    --verde: #5B6B33;
    --negro: #000000;
    --gris-oscuro: #1a1a1a;
    --gris-medio: #333333;
    --blanco: #ffffff;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--negro);
    color: var(--dorado);
    line-height: 1.6;
    overflow-x: hidden;
    scroll-behavior: smooth;
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gris-oscuro);
}

::-webkit-scrollbar-thumb {
    background: var(--dorado);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--guinda);
}

/* HEADER */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 50%, rgba(0, 0, 0, 0) 100%);
    backdrop-filter: blur(5px);
    z-index: 1000;
    padding: 1.5rem 0 1.5rem 0;
    transition: all 0.4s ease;
}

.header.scrolled {
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    padding: 0.8rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
    position: relative;
    z-index: 2;
}

.logo {
    font-size: 2rem;
    font-weight: bold;
    color: var(--dorado);
    text-decoration: none;
    transition: all 0.3s ease;
    text-shadow: 0 0 10px rgba(205, 170, 88, 0.5);
}

.logo:hover {
    transform: scale(1.05);
    text-shadow: 0 0 20px var(--dorado);
}

.header:not(.scrolled) .logo {
    text-shadow: 0 0 15px rgba(205, 170, 88, 0.8), 0 0 25px rgba(205, 170, 88, 0.4);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--dorado);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.nav-link:hover {
    color: var(--guinda);
    transform: translateY(-2px);
    text-shadow: 0 2px 8px rgba(111, 43, 48, 0.6);
}

.header:not(.scrolled) .nav-link {
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.8), 0 0 10px rgba(205, 170, 88, 0.3);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--guinda);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* MOBILE MENU */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--dorado);
    font-size: 1.5rem;
    cursor: pointer;
}

/* CAROUSEL */
.carousel-container {
    position: relative;
    height: 100vh;
    overflow: hidden;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    background-color: var(--gris-oscuro);
}

.carousel-slide.active {
    opacity: 1;
}

.carousel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
}

.carousel-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    width: 100%;
}

.carousel-content.left {
    text-align: left;
}

.carousel-content.right {
    text-align: right;
}

.carousel-content.center {
    text-align: center;
}

.carousel-title {
    font-size: 3.5rem;
    font-weight: bold;
    color: var(--dorado);
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    animation: slideInUp 1s ease-out;
}

.carousel-subtitle {
    font-size: 1.5rem;
    color: var(--blanco);
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    animation: slideInUp 1s ease-out 0.3s both;
}

.btn-premium {
    display: inline-block;
    padding: 1rem 2rem;
    background: linear-gradient(45deg, var(--dorado), var(--guinda));
    color: var(--negro);
    text-decoration: none;
    font-weight: bold;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    animation: slideInUp 1s ease-out 0.6s both;
    border: none;
    cursor: pointer;
}

.btn-premium:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(205, 170, 88, 0.3);
    background: linear-gradient(45deg, var(--guinda), var(--dorado));
}

/* CAROUSEL INDICATORS */
.carousel-indicators {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
    z-index: 3;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(205, 170, 88, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: var(--dorado);
    transform: scale(1.2);
}

/* ANIMATIONS */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes glow {
    0% { box-shadow: 0 0 5px var(--dorado); }
    50% { box-shadow: 0 0 20px var(--dorado), 0 0 30px var(--dorado); }
    100% { box-shadow: 0 0 5px var(--dorado); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Elementos con animación al scroll */
[data-animate] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

[data-animate].animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Efectos hover mejorados */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(205, 170, 88, 0.3);
}

.hover-glow:hover {
    animation: glow 2s infinite;
}

/* Gradientes premium */
.gradient-gold {
    background: linear-gradient(135deg, var(--dorado) 0%, #f4d03f 100%);
}

.gradient-wine {
    background: linear-gradient(135deg, var(--guinda) 0%, #8b1538 100%);
}

.gradient-green {
    background: linear-gradient(135deg, var(--verde) 0%, #7d8f47 100%);
}

.gradient-dark {
    background: linear-gradient(135deg, var(--gris-oscuro) 0%, var(--negro) 100%);
}

/* RESPONSIVE */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }
    
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        width: 100%;
        background: var(--negro);
        flex-direction: column;
        padding: 2rem;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .carousel-title {
        font-size: 2.5rem;
    }
    
    .carousel-subtitle {
        font-size: 1.2rem;
    }
    
    .nav-container {
        padding: 0 1rem;
    }
}
