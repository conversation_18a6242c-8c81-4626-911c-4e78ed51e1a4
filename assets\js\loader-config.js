// Configuración personalizada para el loader de UVAMAYU
document.addEventListener('DOMContentLoaded', function() {
    // Configurar placeholders específicos por tipo de contenido
    const placeholderConfig = {
        productos: '🍇',
        blog: '📝',
        historia: '📖',
        proceso: '⚗️',
        equipo: '👥',
        contacto: '📞',
        default: '🍇'
    };
    
    // Personalizar mensajes de carga
    const loadingMessages = [
        'Preparando la experiencia UVAMAYU... 🍇',
        'Destilando contenido premium... ⚗️',
        'Cargando tradición artesanal... 🏭',
        'Sirviendo lo mejor del valle... 🌄',
        'Un momento, perfeccionando detalles... ✨'
    ];
    
    // Cambiar mensaje de carga aleatoriamente
    const loaderText = document.querySelector('.loader-text');
    if (loaderText) {
        const randomMessage = loadingMessages[Math.floor(Math.random() * loadingMessages.length)];
        loaderText.textContent = randomMessage;
    }
    
    // Mejorar la experiencia de carga del carrusel
    const carouselSlides = document.querySelectorAll('.carousel-slide');
    carouselSlides.forEach((slide, index) => {
        // Agregar efecto de fade-in progresivo
        setTimeout(() => {
            slide.style.transition = 'opacity 0.8s ease-in-out';
        }, index * 200);
    });
    
    // Optimizar carga de imágenes críticas
    const criticalImages = document.querySelectorAll('.lazy-image[data-critical="true"]');
    criticalImages.forEach(img => {
        // Cargar inmediatamente las imágenes críticas
        if (img.dataset.src) {
            img.src = img.dataset.src;
            img.classList.add('loaded');
        }
    });
    
    // Precargar imágenes de la siguiente sección cuando el usuario hace scroll
    let nextSectionPreloaded = false;
    window.addEventListener('scroll', function() {
        if (!nextSectionPreloaded && window.scrollY > window.innerHeight * 0.5) {
            preloadNextSectionImages();
            nextSectionPreloaded = true;
        }
    });
    
    function preloadNextSectionImages() {
        const nextImages = document.querySelectorAll('.lazy-image:not(.loaded)');
        const imagesToPreload = Array.from(nextImages).slice(0, 3); // Precargar solo las primeras 3
        
        imagesToPreload.forEach(img => {
            if (img.dataset.src) {
                const tempImg = new Image();
                tempImg.onload = () => {
                    img.src = tempImg.src;
                    img.classList.add('loaded');
                    
                    const placeholder = img.closest('.image-container')?.querySelector('.image-placeholder');
                    if (placeholder) {
                        placeholder.classList.add('hidden');
                    }
                };
                tempImg.src = img.dataset.src;
            }
        });
    }
    
    // Mejorar la experiencia en conexiones lentas
    if (navigator.connection && navigator.connection.effectiveType) {
        const connectionType = navigator.connection.effectiveType;
        
        if (connectionType === 'slow-2g' || connectionType === '2g') {
            // Reducir calidad de imágenes para conexiones lentas
            document.querySelectorAll('.lazy-image').forEach(img => {
                if (img.dataset.src) {
                    // Agregar parámetro de calidad reducida si el servidor lo soporta
                    img.dataset.src += '?quality=60';
                }
            });
            
            // Mostrar mensaje de conexión lenta
            const loaderSubtitle = document.querySelector('.loader-subtitle');
            if (loaderSubtitle) {
                loaderSubtitle.textContent = 'Optimizando para tu conexión... 📶';
            }
        }
    }
    
    // Agregar efectos de hover mejorados para las imágenes cargadas
    document.addEventListener('imageLoaded', function(e) {
        const img = e.target;
        img.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        img.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
    
    // Optimización para dispositivos móviles
    if (window.innerWidth <= 768) {
        // Reducir animaciones en móviles para mejor rendimiento
        document.documentElement.style.setProperty('--animation-duration', '0.3s');
        
        // Cargar imágenes más pequeñas en móviles
        document.querySelectorAll('.lazy-image').forEach(img => {
            if (img.dataset.src && !img.dataset.src.includes('mobile')) {
                // Si tienes versiones móviles de las imágenes, úsalas aquí
                // img.dataset.src = img.dataset.src.replace('.png', '_mobile.png');
            }
        });
    }
    
    // Debug mode para desarrollo
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        console.log('🍇 UVAMAYU Loader Debug Mode');
        console.log('Total lazy images:', document.querySelectorAll('.lazy-image').length);
        console.log('Connection type:', navigator.connection?.effectiveType || 'unknown');

        // Debug del carrusel
        const carouselSlides = document.querySelectorAll('.carousel-slide');
        console.log('🎠 Carousel slides found:', carouselSlides.length);

        carouselSlides.forEach((slide, index) => {
            const bgImage = getComputedStyle(slide).backgroundImage;
            console.log(`Slide ${index + 1} background:`, bgImage);

            // Verificar si la imagen existe
            if (bgImage && bgImage !== 'none') {
                const imageUrl = bgImage.slice(4, -1).replace(/"/g, "");
                const img = new Image();
                img.onload = () => console.log(`✅ Slide ${index + 1} image loaded:`, imageUrl);
                img.onerror = () => console.log(`❌ Slide ${index + 1} image failed:`, imageUrl);
                img.src = imageUrl;
            }
        });

        // Agregar indicador visual de imágenes cargadas
        document.addEventListener('imageLoaded', function(e) {
            console.log('✅ Image loaded:', e.detail.src);
        });
    }
});

// Función para forzar la carga de todas las imágenes (útil para testing)
window.forceLoadAllImages = function() {
    document.querySelectorAll('.lazy-image:not(.loaded)').forEach(img => {
        if (img.dataset.src) {
            img.src = img.dataset.src;
            img.classList.add('loaded');
            
            const placeholder = img.closest('.image-container')?.querySelector('.image-placeholder');
            if (placeholder) {
                placeholder.classList.add('hidden');
            }
        }
    });
    console.log('🚀 All images force loaded');
};
