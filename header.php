<?php
$config = require 'config.php';
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="assets/estilos.css">
    <script src="assets/carrito.js"></script>
    <script src="assets/animaciones.js"></script>
</head>
<body>
<header id="mainHeader" style="background: var(--vino-borgona); color: #fff; padding: 0; box-shadow: 0 2px 8px rgba(0,0,0,0.08); position: fixed; top: 0; left: 0; right: 0; z-index: 100; transition: all 0.3s ease;">
  <div style="display: flex; align-items: center; justify-content: space-between; max-width: 1200px; margin: 0 auto; padding: 10px 20px;">
    <a href="/" style="display: flex; align-items: center; text-decoration: none; color: inherit;">
      <img src="logo.png" alt="UVAMAYU" style="height: 48px; margin-right: 16px; filter: drop-shadow(0 0 8px var(--oro-dorado));">
      <span style="font-family: 'Cinzel', serif; font-size: 2em; font-weight: bold; letter-spacing: 2px;"></span>
    </a>
    <nav style="display: flex; align-items: center; gap: 24px;">
      <ul style="display: flex; gap: 24px; list-style: none; margin: 0; padding: 0; font-size: 1.1em;">
        <li><a href="/" style="color: #fff; text-decoration: none; transition: all 0.3s ease;">Inicio</a></li>
        <li><a href="/historia.php" style="color: #fff; text-decoration: none; transition: all 0.3s ease;">Nuestra Historia</a></li>
        <li><a href="/productos.php" style="color: #fff; text-decoration: none; transition: all 0.3s ease;">Productos</a></li>
        <li><a href="/blog.php" style="color: #fff; text-decoration: none; transition: all 0.3s ease;">Blog</a></li>
        <li><a href="/contacto.php" style="color: #fff; text-decoration: none; transition: all 0.3s ease;">Contacto</a></li>
      </ul>
      <button id="carritoBtn" class="carrito-btn" title="Ver carrito">
        🛒
        <span id="carritoContador" class="carrito-contador" style="display: none;">0</span>
      </button>
    </nav>
  </div>
  <style>
    /* Header transparente y efectos */
    .header-transparent {
      background: rgba(136, 27, 53, 0.1) !important;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .header-scrolled {
      background: var(--vino-borgona) !important;
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }

    @keyframes fadeInDown {
      from { opacity: 0; transform: translateY(-30px); }
      to { opacity: 1; transform: translateY(0); }
    }

    header nav ul li a:hover {
      color: var(--oro-dorado);
      text-shadow: 0 0 8px rgba(255,255,255,0.3), 0 0 16px var(--oro-dorado);
      transform: translateY(-2px);
    }

    /* Responsive header */
    @media (max-width: 768px) {
      header div {
        padding: 8px 15px !important;
      }
      header nav ul {
        gap: 15px !important;
        font-size: 0.9em !important;
      }
      header img {
        height: 40px !important;
      }
      .carrito-btn {
        font-size: 1.3em !important;
      }
    }
  </style>

  <script>
    // Efecto de header transparente
    document.addEventListener('DOMContentLoaded', function() {
      const header = document.getElementById('mainHeader');
      const isHomePage = window.location.pathname === '/' || window.location.pathname === '/index.php';

      if (isHomePage) {
        header.classList.add('header-transparent');

        window.addEventListener('scroll', function() {
          if (window.scrollY > 100) {
            header.classList.remove('header-transparent');
            header.classList.add('header-scrolled');
          } else {
            header.classList.add('header-transparent');
            header.classList.remove('header-scrolled');
          }
        });
      } else {
        header.classList.add('header-scrolled');
      }
    });
  </script>
</header> 