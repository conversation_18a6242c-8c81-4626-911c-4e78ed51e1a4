<?php
$config = require 'config.php';
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="assets/estilos.css">
    <script src="assets/carrito.js"></script>
    <script src="assets/animaciones.js"></script>
</head>
<body>
<header id="mainHeader" style="background: var(--vino-borgona); color: #fff; padding: 0; box-shadow: 0 2px 8px rgba(0,0,0,0.08); position: fixed; top: 0; left: 0; right: 0; z-index: 100; transition: all 0.3s ease;">
  <div style="display: flex; align-items: center; justify-content: space-between; max-width: 1200px; margin: 0 auto; padding: 10px 20px;">
    <a href="/" style="display: flex; align-items: center; text-decoration: none; color: inherit;">
      <img src="logo.png" alt="UVAMAYU" style="height: 48px; margin-right: 16px; filter: drop-shadow(0 0 8px var(--oro-dorado));">
      <span style="font-family: 'Cinzel', serif; font-size: 2em; font-weight: bold; letter-spacing: 2px;"></span>
    </a>
    <nav style="display: flex; align-items: center; gap: 24px;">
      <ul style="display: flex; gap: 24px; list-style: none; margin: 0; padding: 0; font-size: 1.1em;">
        <li><a href="/" style="color: #fff; text-decoration: none; transition: all 0.3s ease;">Inicio</a></li>
        <li><a href="/historia.php" style="color: #fff; text-decoration: none; transition: all 0.3s ease;">Nuestra Historia</a></li>
        <li><a href="/productos.php" style="color: #fff; text-decoration: none; transition: all 0.3s ease;">Productos</a></li>
        <li><a href="/blog.php" style="color: #fff; text-decoration: none; transition: all 0.3s ease;">Blog</a></li>
        <li><a href="/contacto.php" style="color: #fff; text-decoration: none; transition: all 0.3s ease;">Contacto</a></li>
      </ul>
      <button id="carritoBtn" class="carrito-btn" title="Ver carrito">
        🛒
        <span id="carritoContador" class="carrito-contador" style="display: none;">0</span>
      </button>
    </nav>
  </div>
  <style>
    /* Header transparente y efectos premium */
    .header-transparent {
      background: linear-gradient(180deg, rgba(136, 27, 53, 0.3), rgba(136, 27, 53, 0.1)) !important;
      backdrop-filter: blur(15px);
      -webkit-backdrop-filter: blur(15px);
      border-bottom: 1px solid rgba(161, 122, 50, 0.3);
    }

    .header-scrolled {
      background: linear-gradient(135deg, var(--vino-borgona), #9a1e3d) !important;
      box-shadow: 0 8px 32px rgba(136, 27, 53, 0.4);
    }

    /* Transición suave del header */
    #mainHeader {
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    /* Efecto glow en el logo cuando está transparente */
    .header-transparent img {
      filter: drop-shadow(0 0 15px var(--oro-dorado)) !important;
    }

    .header-transparent .carrito-btn {
      text-shadow: 0 0 10px rgba(161, 122, 50, 0.8);
    }

    /* Animaciones de entrada */
    @keyframes fadeInDown {
      from { opacity: 0; transform: translateY(-30px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes headerGlow {
      0%, 100% { text-shadow: 0 0 5px rgba(161, 122, 50, 0.5); }
      50% { text-shadow: 0 0 20px rgba(161, 122, 50, 0.8), 0 0 30px rgba(161, 122, 50, 0.6); }
    }

    /* Efectos hover premium para navegación */
    header nav ul li a {
      position: relative;
      overflow: hidden;
    }

    header nav ul li a::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: -100%;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, transparent, var(--oro-dorado), transparent);
      transition: left 0.5s ease;
    }

    header nav ul li a:hover::before {
      left: 100%;
    }

    header nav ul li a:hover {
      color: var(--oro-dorado);
      text-shadow: 0 0 10px rgba(161, 122, 50, 0.8);
      transform: translateY(-2px);
      animation: headerGlow 2s infinite;
    }

    /* Efecto parallax sutil en el header */
    .header-parallax {
      transform: translateY(calc(var(--scroll-y) * 0.1px));
    }

    /* Responsive header mejorado */
    @media (max-width: 768px) {
      header div {
        padding: 8px 15px !important;
        flex-wrap: nowrap;
      }

      header nav ul {
        gap: 12px !important;
        font-size: 0.85em !important;
      }

      header nav ul li a {
        padding: 5px 8px;
        border-radius: 5px;
        transition: all 0.3s ease;
      }

      header img {
        height: 40px !important;
      }

      .carrito-btn {
        font-size: 1.2em !important;
        padding: 8px !important;
      }

      /* Header móvil con mejor legibilidad */
      .header-transparent {
        background: linear-gradient(180deg, rgba(136, 27, 53, 0.8), rgba(136, 27, 53, 0.6)) !important;
      }
    }

    @media (max-width: 480px) {
      header nav ul {
        gap: 8px !important;
        font-size: 0.8em !important;
      }

      header nav ul li a {
        padding: 4px 6px;
      }

      header img {
        height: 35px !important;
        margin-right: 8px !important;
      }
    }
  </style>

  <script>
    // Sistema de header transparente premium
    document.addEventListener('DOMContentLoaded', function() {
      const header = document.getElementById('mainHeader');
      const isHomePage = window.location.pathname === '/' || window.location.pathname === '/index.php' || window.location.pathname.endsWith('/');
      let lastScrollY = 0;
      let ticking = false;

      // Configuración inicial
      if (isHomePage) {
        header.classList.add('header-transparent');

        // Efecto de scroll suave y optimizado
        const updateHeader = () => {
          const scrollY = window.pageYOffset;
          const scrollProgress = Math.min(scrollY / 150, 1); // Transición gradual en 150px

          // Actualizar propiedades CSS custom para transiciones suaves
          document.documentElement.style.setProperty('--scroll-y', scrollY);

          if (scrollProgress > 0.3) {
            header.classList.remove('header-transparent');
            header.classList.add('header-scrolled');

            // Efecto de ocultación al hacer scroll hacia abajo rápido
            if (scrollY > lastScrollY && scrollY > 200) {
              header.style.transform = 'translateY(-100%)';
            } else {
              header.style.transform = 'translateY(0)';
            }
          } else {
            header.classList.add('header-transparent');
            header.classList.remove('header-scrolled');
            header.style.transform = 'translateY(0)';
          }

          // Efecto parallax sutil
          if (scrollProgress < 1) {
            header.style.transform = `translateY(${scrollY * 0.1}px)`;
          }

          lastScrollY = scrollY;
          ticking = false;
        };

        const requestTick = () => {
          if (!ticking) {
            requestAnimationFrame(updateHeader);
            ticking = true;
          }
        };

        // Listener optimizado para scroll
        window.addEventListener('scroll', requestTick, { passive: true });

        // Efecto inicial
        updateHeader();
      } else {
        header.classList.add('header-scrolled');
      }

      // Efecto de glow en hover para elementos del header
      const navLinks = header.querySelectorAll('nav a');
      navLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
          this.style.textShadow = '0 0 15px var(--oro-dorado), 0 0 25px var(--oro-dorado)';
        });

        link.addEventListener('mouseleave', function() {
          this.style.textShadow = '';
        });
      });

      // Efecto de respiración para el carrito cuando tiene items
      const carritoBtn = document.getElementById('carritoBtn');
      const carritoContador = document.getElementById('carritoContador');

      if (carritoBtn && carritoContador) {
        const observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' || mutation.type === 'characterData') {
              const count = parseInt(carritoContador.textContent) || 0;
              if (count > 0) {
                carritoBtn.style.animation = 'pulse 2s infinite';
              } else {
                carritoBtn.style.animation = '';
              }
            }
          });
        });

        observer.observe(carritoContador, {
          childList: true,
          characterData: true,
          subtree: true
        });
      }
    });

    // Efecto de partículas sutiles en el header (solo en home)
    if (window.location.pathname === '/' || window.location.pathname === '/index.php' || window.location.pathname.endsWith('/')) {
      window.addEventListener('load', function() {
        createHeaderParticles();
      });
    }

    function createHeaderParticles() {
      const header = document.getElementById('mainHeader');
      const particleCount = 5;

      for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.style.cssText = `
          position: absolute;
          width: 2px;
          height: 2px;
          background: var(--oro-dorado);
          border-radius: 50%;
          opacity: 0;
          pointer-events: none;
          z-index: -1;
        `;

        header.appendChild(particle);

        // Animación de partícula
        const animateParticle = () => {
          const x = Math.random() * header.offsetWidth;
          const duration = 3000 + Math.random() * 2000;

          particle.style.left = x + 'px';
          particle.style.top = '50%';
          particle.style.opacity = '0.6';

          particle.animate([
            { transform: 'translateY(0) scale(0)', opacity: 0.6 },
            { transform: 'translateY(-20px) scale(1)', opacity: 1, offset: 0.5 },
            { transform: 'translateY(-40px) scale(0)', opacity: 0 }
          ], {
            duration: duration,
            easing: 'ease-out'
          }).onfinish = () => {
            setTimeout(animateParticle, Math.random() * 5000);
          };
        };

        setTimeout(animateParticle, Math.random() * 3000);
      }
    }
  </script>
</header> 