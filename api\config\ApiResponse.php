<?php
/**
 * Clase para manejar respuestas estándar de API
 */
class ApiResponse {
    
    /**
     * Enviar respuesta exitosa
     */
    public static function success($data = null, $message = "Operación exitosa", $code = 200) {
        http_response_code($code);
        header('Content-Type: application/json; charset=utf-8');
        
        $response = [
            'success' => true,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Enviar respuesta de error
     */
    public static function error($message = "Error en la operación", $code = 400, $details = null) {
        http_response_code($code);
        header('Content-Type: application/json; charset=utf-8');
        
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        if ($details !== null) {
            $response['details'] = $details;
        }
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Enviar respuesta de validación
     */
    public static function validationError($errors, $message = "Errores de validación") {
        self::error($message, 422, $errors);
    }
    
    /**
     * Enviar respuesta no encontrado
     */
    public static function notFound($message = "Recurso no encontrado") {
        self::error($message, 404);
    }
    
    /**
     * Enviar respuesta no autorizado
     */
    public static function unauthorized($message = "No autorizado") {
        self::error($message, 401);
    }
    
    /**
     * Enviar respuesta de servidor
     */
    public static function serverError($message = "Error interno del servidor") {
        self::error($message, 500);
    }
    
    /**
     * Validar método HTTP
     */
    public static function validateMethod($allowedMethods) {
        $method = $_SERVER['REQUEST_METHOD'];
        
        if (!in_array($method, $allowedMethods)) {
            self::error("Método no permitido. Métodos permitidos: " . implode(', ', $allowedMethods), 405);
        }
        
        return $method;
    }
    
    /**
     * Obtener datos JSON del cuerpo de la petición
     */
    public static function getJsonInput() {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            self::error("JSON inválido: " . json_last_error_msg(), 400);
        }
        
        return $data;
    }
    
    /**
     * Validar campos requeridos
     */
    public static function validateRequired($data, $requiredFields) {
        $errors = [];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty(trim($data[$field]))) {
                $errors[$field] = "El campo {$field} es requerido";
            }
        }
        
        if (!empty($errors)) {
            self::validationError($errors);
        }
        
        return true;
    }
    
    /**
     * Sanitizar datos de entrada
     */
    public static function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeInput'], $data);
        }
        
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
}
?>
