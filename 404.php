<?php
$page_title = "Página no encontrada";
include 'includes/header.php';
?>

<section class="error-section">
    <div class="container">
        <div class="error-content">
            <div class="error-animation">
                <div class="error-number">404</div>
                <div class="error-bottle">🍷</div>
            </div>
            
            <h1 class="error-title">¡Oops! Esta página se perdió como una uva en la vendimia 🍇</h1>
            <p class="error-message">
                La página que buscas no existe o ha sido movida. Pero no te preocupes, 
                tenemos muchas otras cosas interesantes para mostrarte.
            </p>
            
            <div class="error-suggestions">
                <h3>¿Qué tal si pruebas con:</h3>
                <div class="suggestion-links">
                    <a href="/" class="suggestion-link">
                        🏠 Ir al inicio
                    </a>
                    <a href="/productos.php" class="suggestion-link">
                        🍇 Ver productos
                    </a>
                    <a href="/blog.php" class="suggestion-link">
                        📝 Leer el blog
                    </a>
                    <a href="/contacto.php" class="suggestion-link">
                        📞 Contáctanos
                    </a>
                </div>
            </div>
            
            <div class="error-search">
                <h3>O busca lo que necesitas:</h3>
                <div class="search-form">
                    <input type="text" id="searchInput" placeholder="Buscar productos, artículos..." class="search-input">
                    <button onclick="performSearch()" class="search-btn">🔍 Buscar</button>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .error-section {
        min-height: 80vh;
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, var(--gris-oscuro) 0%, var(--negro) 100%);
        padding: 4rem 0;
    }
    
    .error-content {
        text-align: center;
        max-width: 800px;
        margin: 0 auto;
    }
    
    .error-animation {
        position: relative;
        margin-bottom: 3rem;
        animation: float 3s ease-in-out infinite;
    }
    
    .error-number {
        font-family: 'Playfair Display', serif;
        font-size: 8rem;
        font-weight: bold;
        color: var(--dorado);
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        margin-bottom: 1rem;
        animation: pulse 2s infinite;
    }
    
    .error-bottle {
        font-size: 4rem;
        animation: float 2s ease-in-out infinite reverse;
    }
    
    .error-title {
        font-family: 'Playfair Display', serif;
        font-size: 2.5rem;
        color: var(--dorado);
        margin-bottom: 1.5rem;
        animation: slideInUp 0.6s ease-out;
    }
    
    .error-message {
        font-size: 1.2rem;
        color: #ccc;
        line-height: 1.6;
        margin-bottom: 3rem;
        animation: slideInUp 0.6s ease-out 0.2s both;
    }
    
    .error-suggestions {
        margin-bottom: 3rem;
        animation: slideInUp 0.6s ease-out 0.4s both;
    }
    
    .error-suggestions h3 {
        color: var(--dorado);
        font-size: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .suggestion-links {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        max-width: 600px;
        margin: 0 auto;
    }
    
    .suggestion-link {
        background: var(--gris-medio);
        color: var(--dorado);
        text-decoration: none;
        padding: 1.5rem;
        border-radius: 10px;
        transition: all 0.3s ease;
        font-weight: 500;
        border: 2px solid transparent;
    }
    
    .suggestion-link:hover {
        background: var(--dorado);
        color: var(--negro);
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(205, 170, 88, 0.3);
    }
    
    .error-search {
        animation: slideInUp 0.6s ease-out 0.6s both;
    }
    
    .error-search h3 {
        color: var(--dorado);
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .search-form {
        display: flex;
        max-width: 400px;
        margin: 0 auto;
        gap: 1rem;
    }
    
    .search-input {
        flex: 1;
        background: var(--gris-medio);
        border: 2px solid transparent;
        color: white;
        padding: 1rem;
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .search-input:focus {
        outline: none;
        border-color: var(--dorado);
        box-shadow: 0 0 10px rgba(205, 170, 88, 0.3);
    }
    
    .search-btn {
        background: linear-gradient(45deg, var(--dorado), var(--guinda));
        color: var(--negro);
        border: none;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
    }
    
    .search-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(205, 170, 88, 0.4);
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @media (max-width: 768px) {
        .error-number {
            font-size: 6rem;
        }
        
        .error-title {
            font-size: 2rem;
        }
        
        .suggestion-links {
            grid-template-columns: 1fr;
        }
        
        .search-form {
            flex-direction: column;
        }
    }
</style>

<script>
function performSearch() {
    const query = document.getElementById('searchInput').value.trim().toLowerCase();
    
    if (!query) {
        window.uvamayuApp.showNotification('Por favor ingresa algo para buscar', 'error');
        return;
    }
    
    // Buscar en productos
    if (query.includes('pisco') || query.includes('vino') || query.includes('acholado') || 
        query.includes('quebranta') || query.includes('italia') || query.includes('torontel') ||
        query.includes('mosto') || query.includes('perfecto') || query.includes('borgoña') ||
        query.includes('naranja') || query.includes('ciruela')) {
        window.location.href = '/productos.php';
        return;
    }
    
    // Buscar en blog
    if (query.includes('receta') || query.includes('maridaje') || query.includes('cata') ||
        query.includes('fiestas') || query.includes('patrias') || query.includes('proceso')) {
        window.location.href = '/blog.php';
        return;
    }
    
    // Buscar información de contacto
    if (query.includes('contacto') || query.includes('telefono') || query.includes('email') ||
        query.includes('direccion') || query.includes('ubicacion')) {
        window.location.href = '/contacto.php';
        return;
    }
    
    // Buscar información sobre la empresa
    if (query.includes('nosotros') || query.includes('historia') || query.includes('empresa') ||
        query.includes('tradicion') || query.includes('familia')) {
        window.location.href = '/nosotros.php';
        return;
    }
    
    // Si no encuentra nada específico, ir a productos
    window.uvamayuApp.showNotification('Te llevamos a nuestros productos 🍇', 'info');
    setTimeout(() => {
        window.location.href = '/productos.php';
    }, 1500);
}

// Permitir buscar con Enter
document.getElementById('searchInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        performSearch();
    }
});
</script>

<?php include 'includes/footer.php'; ?>
