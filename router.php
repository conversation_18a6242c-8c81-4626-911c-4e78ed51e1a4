<?php
// Router para UVAMAYU - Maneja las rutas limpias
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);
$path = trim($path, '/');

// Rutas estáticas
$routes = [
    '' => 'index.php',
    'productos' => 'productos.php',
    'nosotros' => 'nosotros.php',
    'blog' => 'blog.php',
    'contacto' => 'contacto.php',
    'sitemap.xml' => 'sitemap.php',
    'comentarios_internos' => 'comentarios_internos.php'
];

// Rutas dinámicas
if (preg_match('/^productos\/(\d+)$/', $path, $matches)) {
    $_GET['id'] = $matches[1];
    include 'productos.php';
    exit;
}

// Verificar si es una ruta estática
if (array_key_exists($path, $routes)) {
    $file = $routes[$path];
    if (file_exists($file)) {
        include $file;
        exit;
    }
}

// Verificar si es un archivo real (CSS, JS, imágenes, etc.)
if (file_exists($path)) {
    return false; // Dejar que PHP sirva el archivo
}

// Si no se encuentra, mostrar 404
include '404.php';
?>
