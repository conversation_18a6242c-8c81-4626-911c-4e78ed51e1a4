// Sistema de carrito mejorado para UVAMAYU
class CarritoUVAMAYU {
    constructor() {
        this.carrito = JSON.parse(localStorage.getItem('carrito') || '{}');
        this.productos = {};
        this.init();
    }

    init() {
        this.crearModalCarrito();
        this.actualizarContadorCarrito();
        this.bindEvents();
    }

    crearModalCarrito() {
        const modalHTML = `
            <div id="carritoModal" class="carrito-modal">
                <div class="carrito-modal-content">
                    <div class="carrito-header">
                        <h2>Mi Carrito</h2>
                        <button class="carrito-close">&times;</button>
                    </div>
                    <div class="carrito-body">
                        <div id="carritoItems"></div>
                        <div class="carrito-total">
                            <div class="total-line">
                                <span>Total: S/ <span id="carritoTotal">0.00</span></span>
                            </div>
                        </div>
                    </div>
                    <div class="carrito-footer">
                        <button id="vaciarCarrito" class="btn-secondary">Vac<PERSON></button>
                        <div class="carrito-actions">
                            <button id="comprarCorreo" class="btn">Comprar por Correo</button>
                            <button id="comprarWhatsApp" class="btn bg-dorado">Comprar por WhatsApp</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    bindEvents() {
        // Botón del carrito en el header
        const carritoBtn = document.getElementById('carritoBtn');
        if (carritoBtn) {
            carritoBtn.addEventListener('click', () => this.mostrarCarrito());
        }

        // Cerrar modal
        document.querySelector('.carrito-close').addEventListener('click', () => this.cerrarCarrito());
        document.getElementById('carritoModal').addEventListener('click', (e) => {
            if (e.target.id === 'carritoModal') this.cerrarCarrito();
        });

        // Vaciar carrito
        document.getElementById('vaciarCarrito').addEventListener('click', () => this.vaciarCarrito());

        // Botones de compra
        document.getElementById('comprarCorreo').addEventListener('click', () => this.comprarPorCorreo());
        document.getElementById('comprarWhatsApp').addEventListener('click', () => this.comprarPorWhatsApp());
    }

    agregarProducto(id, cantidad = 1, producto = null) {
        this.carrito[id] = (this.carrito[id] || 0) + parseInt(cantidad);
        if (producto) {
            this.productos[id] = producto;
        }
        this.guardarCarrito();
        this.actualizarContadorCarrito();
        this.mostrarNotificacion(`Producto añadido al carrito`);
    }

    eliminarProducto(id) {
        delete this.carrito[id];
        delete this.productos[id];
        this.guardarCarrito();
        this.actualizarContadorCarrito();
        this.actualizarVistaCarrito();
    }

    cambiarCantidad(id, cantidad) {
        if (cantidad <= 0) {
            this.eliminarProducto(id);
        } else {
            this.carrito[id] = parseInt(cantidad);
            this.guardarCarrito();
            this.actualizarVistaCarrito();
        }
    }

    vaciarCarrito() {
        if (confirm('¿Estás seguro de que quieres vaciar el carrito?')) {
            this.carrito = {};
            this.productos = {};
            this.guardarCarrito();
            this.actualizarContadorCarrito();
            this.actualizarVistaCarrito();
        }
    }

    mostrarCarrito() {
        this.cargarProductosCarrito().then(() => {
            this.actualizarVistaCarrito();
            document.getElementById('carritoModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        });
    }

    cerrarCarrito() {
        document.getElementById('carritoModal').style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    async cargarProductosCarrito() {
        const productosIds = Object.keys(this.carrito);
        if (productosIds.length === 0) return;

        // Simular carga de productos (en un caso real, harías una petición AJAX)
        // Por ahora, usaremos los datos que ya están en la página
        const productosEnPagina = document.querySelectorAll('[data-producto-id]');
        productosEnPagina.forEach(elem => {
            const id = elem.dataset.productoId;
            if (this.carrito[id]) {
                this.productos[id] = {
                    id: id,
                    nombre: elem.dataset.productoNombre || 'Producto',
                    precio: parseFloat(elem.dataset.productoPrecio || 0),
                    imagen: elem.dataset.productoImagen || 'assets/default-product.jpg'
                };
            }
        });
    }

    actualizarVistaCarrito() {
        const carritoItems = document.getElementById('carritoItems');
        const carritoTotal = document.getElementById('carritoTotal');
        
        if (Object.keys(this.carrito).length === 0) {
            carritoItems.innerHTML = '<div class="carrito-vacio">Tu carrito está vacío</div>';
            carritoTotal.textContent = '0.00';
            return;
        }

        let total = 0;
        let itemsHTML = '';

        Object.keys(this.carrito).forEach(id => {
            const cantidad = this.carrito[id];
            const producto = this.productos[id] || { nombre: 'Producto', precio: 0, imagen: '' };
            const subtotal = producto.precio * cantidad;
            total += subtotal;

            itemsHTML += `
                <div class="carrito-item">
                    <img src="${producto.imagen}" alt="${producto.nombre}" class="carrito-item-img">
                    <div class="carrito-item-info">
                        <h4>${producto.nombre}</h4>
                        <div class="carrito-item-precio">S/ ${producto.precio.toFixed(2)}</div>
                    </div>
                    <div class="carrito-item-controls">
                        <button onclick="carritoUVAMAYU.cambiarCantidad('${id}', ${cantidad - 1})" class="cantidad-btn">-</button>
                        <span class="cantidad">${cantidad}</span>
                        <button onclick="carritoUVAMAYU.cambiarCantidad('${id}', ${cantidad + 1})" class="cantidad-btn">+</button>
                    </div>
                    <div class="carrito-item-subtotal">S/ ${subtotal.toFixed(2)}</div>
                    <button onclick="carritoUVAMAYU.eliminarProducto('${id}')" class="eliminar-btn">&times;</button>
                </div>
            `;
        });

        carritoItems.innerHTML = itemsHTML;
        carritoTotal.textContent = total.toFixed(2);
    }

    actualizarContadorCarrito() {
        const contador = document.getElementById('carritoContador');
        const totalItems = Object.values(this.carrito).reduce((sum, cant) => sum + cant, 0);
        
        if (contador) {
            contador.textContent = totalItems;
            contador.style.display = totalItems > 0 ? 'block' : 'none';
        }
    }

    comprarPorCorreo() {
        const resumen = this.generarResumenCompra();
        const subject = encodeURIComponent('Pedido UVAMAYU');
        const body = encodeURIComponent(resumen);
        window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
    }

    comprarPorWhatsApp() {
        const resumen = this.generarResumenCompra();
        const mensaje = encodeURIComponent(resumen);
        window.open(`https://wa.me/51999999999?text=${mensaje}`);
    }

    generarResumenCompra() {
        let resumen = '🍷 *PEDIDO UVAMAYU* 🍷\n\n';
        let total = 0;

        Object.keys(this.carrito).forEach(id => {
            const cantidad = this.carrito[id];
            const producto = this.productos[id] || { nombre: 'Producto', precio: 0 };
            const subtotal = producto.precio * cantidad;
            total += subtotal;

            resumen += `• ${producto.nombre}\n`;
            resumen += `  Cantidad: ${cantidad}\n`;
            resumen += `  Precio unitario: S/ ${producto.precio.toFixed(2)}\n`;
            resumen += `  Subtotal: S/ ${subtotal.toFixed(2)}\n\n`;
        });

        resumen += `💰 *TOTAL: S/ ${total.toFixed(2)}*\n\n`;
        resumen += '¡Gracias por elegir UVAMAYU! 🇵🇪';

        return resumen;
    }

    mostrarNotificacion(mensaje) {
        const notif = document.createElement('div');
        notif.className = 'carrito-notificacion';
        notif.textContent = mensaje;
        document.body.appendChild(notif);

        setTimeout(() => {
            notif.classList.add('show');
        }, 100);

        setTimeout(() => {
            notif.classList.remove('show');
            setTimeout(() => notif.remove(), 300);
        }, 3000);
    }

    guardarCarrito() {
        localStorage.setItem('carrito', JSON.stringify(this.carrito));
        localStorage.setItem('productos', JSON.stringify(this.productos));
    }
}

// Inicializar carrito global
let carritoUVAMAYU;
document.addEventListener('DOMContentLoaded', () => {
    carritoUVAMAYU = new CarritoUVAMAYU();
});

// Función global para agregar productos (compatibilidad con código existente)
function agregarAlCarrito(id, cantidad = 1) {
    // Obtener datos del producto desde la página
    const productoData = {
        id: id,
        nombre: document.querySelector(`[data-producto-id="${id}"]`)?.dataset.productoNombre || 'Producto',
        precio: parseFloat(document.querySelector(`[data-producto-id="${id}"]`)?.dataset.productoPrecio || 0),
        imagen: document.querySelector(`[data-producto-id="${id}"]`)?.dataset.productoImagen || ''
    };
    
    carritoUVAMAYU.agregarProducto(id, cantidad, productoData);
}
