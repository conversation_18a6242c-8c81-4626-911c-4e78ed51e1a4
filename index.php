<?php
$page_title = "Inicio";
include 'includes/header.php';
?>


<section class="carousel-container">
    <!-- Slide 1: Banner principal sin botón -->
    <div class="carousel-slide active" style="background-image: url('/assets/banners/banner1.png');">
        <div class="carousel-overlay"></div>
        <div class="carousel-content center">
            <h1 class="carousel-title">UVAMAYU</h1>
            <p class="carousel-subtitle">Reserva del sol, espíritu del valle 🍇</p>
        </div>
    </div>
    
    <!-- Slide 2: Productos -->
    <div class="carousel-slide" style="background-image: url('/assets/banners/banner2.png');">
        <div class="carousel-overlay"></div>
        <div class="carousel-content left">
            <h2 class="carousel-title">Conoce nuestros productos</h2>
            <p class="carousel-subtitle">Piscos y vinos artesanales de la más alta calidad 🍷</p>
            <a href="/productos.php" class="btn-premium">Ver Productos</a>
        </div>
    </div>
    
    <!-- Slide 3: Blog Fiestas Patrias -->
    <div class="carousel-slide" style="background-image: url('/assets/banners/banner3.png');">
        <div class="carousel-overlay"></div>
        <div class="carousel-content left">
            <h2 class="carousel-title">Celebra Fiestas Patrias con nosotros</h2>
            <p class="carousel-subtitle">Descubre las mejores recetas y tradiciones peruanas 🇵🇪</p>
            <a href="/blog#fiestas-patrias" class="btn-premium">Leer Más</a>
        </div>
    </div>
    
    <!-- Slide 4: Quiénes somos -->
    <div class="carousel-slide" style="background-image: url('/assets/banners/banner4.png');">
        <div class="carousel-overlay"></div>
        <div class="carousel-content right">
            <h2 class="carousel-title">Tradición que trasciende al tiempo</h2>
            <p class="carousel-subtitle">Nos transformamos para llegar a tu vida 👨‍👩‍👧‍👦</p>
            <a href="/nosotros.php" class="btn-premium">Quiénes Somos</a>
        </div>
    </div>
    
    <!-- Indicadores del carrusel -->
    <div class="carousel-indicators">
        <div class="indicator active" data-slide="0"></div>
        <div class="indicator" data-slide="1"></div>
        <div class="indicator" data-slide="2"></div>
        <div class="indicator" data-slide="3"></div>
    </div>
</section>

<!-- Sección de productos destacados -->
<section class="featured-products" id="productos-destacados">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Productos Destacados ⭐</h2>
            <p class="section-subtitle">Descubre nuestra selección premium</p>
        </div>
        
        <div class="products-grid" id="featured-products-grid">
            <!-- Los productos se cargarán dinámicamente -->
        </div>
        
        <div class="section-cta">
            <a href="/productos.php" class="btn-premium">Ver Todos los Productos</a>
        </div>
    </div>
</section>

<!-- Sección sobre nosotros -->
<section class="about-preview">
    <div class="container">
        <div class="about-content">
            <div class="about-text">
                <h2 class="section-title">Nuestra Historia 📖</h2>
                <p>UVAMAYU nace del amor por la tradición vitivinícola peruana. Cada botella cuenta la historia de nuestro valle, donde el sol y la tierra se unen para crear productos únicos.</p>
                <p>Con técnicas artesanales transmitidas de generación en generación, elaboramos piscos y vinos que capturan la esencia auténtica del Perú. 🇵🇪</p>
                <a href="/nosotros.php" class="btn-premium">Conoce Más</a>
            </div>
            <div class="about-image image-container">
                <div class="image-placeholder">
                    <div class="placeholder-icon">🏭</div>
                </div>
                <img class="lazy-image about-img" data-src="/assets/historia/proceso_tradicion.png" alt="Proceso tradicional de destilación artesanal de piscos UVAMAYU en el valle de Ica, Perú" data-width="500" data-height="400">
            </div>
        </div>
    </div>
</section>

<style>
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
    }
    
    .featured-products {
        padding: 4rem 0;
        background: linear-gradient(135deg, var(--gris-oscuro) 0%, var(--negro) 100%);
    }
    
    .section-header {
        text-align: center;
        margin-bottom: 3rem;
    }
    
    .section-title {
        font-family: 'Playfair Display', serif;
        font-size: 2.5rem;
        color: var(--dorado);
        margin-bottom: 1rem;
        animation: fadeInUp 0.6s ease-out;
    }
    
    .section-subtitle {
        font-size: 1.2rem;
        color: #ccc;
        animation: fadeInUp 0.6s ease-out 0.2s both;
    }
    
    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    .product-card {
        background: var(--gris-medio);
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;
        animation: fadeInUp 0.6s ease-out;
    }
    
    .product-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(205, 170, 88, 0.2);
    }
    
    .product-image {
        width: 100%;
        height: 250px;
        background: var(--gris-oscuro);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--dorado);
        font-size: 1.2rem;
        position: relative;
        overflow: hidden;
    }
    
    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .product-info {
        padding: 1.5rem;
    }
    
    .product-name {
        font-size: 1.3rem;
        font-weight: bold;
        color: var(--dorado);
        margin-bottom: 0.5rem;
    }
    
    .product-description {
        color: #ccc;
        margin-bottom: 1rem;
        font-size: 0.9rem;
    }
    
    .product-price {
        font-size: 1.5rem;
        font-weight: bold;
        color: var(--guinda);
    }
    
    .section-cta {
        text-align: center;
        animation: fadeInUp 0.6s ease-out 0.4s both;
    }
    
    .about-preview {
        padding: 4rem 0;
        background: var(--negro);
    }
    
    .about-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
        align-items: center;
    }
    
    .about-text p {
        color: #ccc;
        margin-bottom: 1.5rem;
        line-height: 1.8;
        animation: fadeInUp 0.6s ease-out;
    }
    
    .about-image {
        animation: fadeInUp 0.6s ease-out 0.3s both;
    }
    
    .about-img {
        width: 100%;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    }
    
    @media (max-width: 768px) {
        .about-content {
            grid-template-columns: 1fr;
            text-align: center;
        }
        
        .products-grid {
            grid-template-columns: 1fr;
        }
        
        .section-title {
            font-size: 2rem;
        }
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadFeaturedProducts();
});

async function loadFeaturedProducts() {
    try {
        const response = await fetch('/productos.json');
        const data = await response.json();
        const featuredProducts = data.productos.filter(p => p.destacado && p.activo).slice(0, 4);
        
        const grid = document.getElementById('featured-products-grid');
        grid.innerHTML = featuredProducts.map(product => `
            <div class="product-card">
                <div class="product-image image-container">
                    <div class="image-placeholder">
                        <div class="placeholder-icon">🍇</div>
                    </div>
                    <img class="lazy-image" data-src="${product.imagen_principal}" alt="${product.nombre} - ${product.categoria} artesanal peruano UVAMAYU"
                         data-width="280" data-height="250">
                </div>
                <div class="product-info">
                    <h3 class="product-name">${product.nombre}</h3>
                    <p class="product-description">${product.descripcion}</p>
                    <div class="product-price">S/ ${product.precio_unitario.toFixed(2)}</div>
                </div>
            </div>
        `).join('');

        // Reinicializar lazy loading para las nuevas imágenes
        if (window.uvamayuLoader) {
            window.uvamayuLoader.setupLazyLoading();
        }
    } catch (error) {
        console.error('Error cargando productos destacados:', error);
    }
}
</script>

<?php include 'includes/footer.php'; ?>
