<?php
$config = require 'config.php';
include 'header.php';
require_once 'conexion.php';
// Seleccionar 3 productos aleatorios activos
$stmt = $pdo->query('SELECT * FROM productos WHERE activo = 1 ORDER BY RAND() LIMIT 3');
$destacados = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<style>
  /* Carrusel pantalla completa */
  .hero-carousel {
    width: 100vw;
    height: 100vh;
    position: relative;
    overflow: hidden;
    margin: 0;
    padding: 0;
  }

  .hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .hero-slide.active {
    opacity: 1;
  }

  .hero-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(136, 27, 53, 0.7), rgba(161, 122, 50, 0.3));
    z-index: 1;
  }

  .hero-content {
    position: absolute;
    top: 50%;
    left: 10%;
    transform: translateY(-50%);
    z-index: 2;
    color: var(--blanco-puro);
    max-width: 600px;
    text-shadow: 0 4px 20px rgba(0,0,0,0.8);
  }

  .hero-content h1 {
    font-size: 3.5em;
    color: var(--oro-dorado);
    margin-bottom: 20px;
    font-weight: 700;
    line-height: 1.2;
    animation: fadeInLeft 1s ease-out 0.5s both;
  }

  .hero-content p {
    font-size: 1.4em;
    margin-bottom: 30px;
    line-height: 1.6;
    animation: fadeInLeft 1s ease-out 0.8s both;
  }

  .hero-content .btn {
    font-size: 1.2em;
    padding: 18px 35px;
    animation: fadeInLeft 1s ease-out 1.1s both;
  }

  .carousel-controls {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    z-index: 3;
  }

  .carousel-dot {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .carousel-dot.active {
    background: var(--oro-dorado);
    transform: scale(1.3);
  }

  .carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(161, 122, 50, 0.8);
    border: none;
    color: var(--blanco-puro);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 1.5em;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 3;
  }

  .carousel-nav:hover {
    background: var(--oro-dorado);
    transform: translateY(-50%) scale(1.1);
  }

  .carousel-prev {
    left: 30px;
  }

  .carousel-next {
    right: 30px;
  }

  /* Ajuste para el contenido después del carrusel */
  .content-section {
    margin-top: 0;
    padding-top: 80px;
  }

  /* Responsive */
  @media (max-width: 768px) {
    .hero-content {
      left: 5%;
      right: 5%;
      max-width: 90%;
    }

    .hero-content h1 {
      font-size: 2.5em;
    }

    .hero-content p {
      font-size: 1.1em;
    }

    .carousel-nav {
      width: 50px;
      height: 50px;
      font-size: 1.2em;
    }

    .carousel-prev {
      left: 15px;
    }

    .carousel-next {
      right: 15px;
    }
  }
</style>

<main>
  <!-- Carrusel Hero Pantalla Completa -->
  <section class="hero-carousel" id="heroCarousel">
    <div class="hero-slide active" style="background-image: url('assets/banner1.png');">
      <div class="hero-content">
        <h1>UVAMAYU – Espíritu del Valle, Orgullo del Perú 🇵🇪</h1>
        <p>Disfruta lo mejor de nuestro pisco y vino artesanal, directo desde el corazón de la tierra peruana.</p>
        <a href="productos.php" class="btn premium-glow">Ver productos</a>
      </div>
    </div>

    <div class="hero-slide" style="background-image: url('assets/banner2.png');">
      <div class="hero-content">
        <h1>🎉 ¡Celebra Fiestas Patrias con sabor peruano!</h1>
        <p>Llévate tu combo de pisco + vino desde S/XX. Promoción válida hasta el 31 de julio.</p>
        <a href="productos.php" class="btn premium-glow">Quiero mi pack</a>
      </div>
    </div>

    <div class="hero-slide" style="background-image: url('assets/banner3.png');">
      <div class="hero-content">
        <h1>Pisco, vino y tradición en cada botella 🍇</h1>
        <p>Elige entre nuestras variedades: Quebranta, Italia, Acholado, Borgoña, Mistela, Ciruela, Naranja y más.</p>
        <a href="productos.php" class="btn premium-glow">Explorar variedades</a>
      </div>
    </div>

    <div class="hero-slide" style="background-image: linear-gradient(45deg, var(--vino-borgona), var(--oro-dorado));">
      <div class="hero-content">
        <h1>Desde 1961, tradición familiar 👨‍👩‍👧‍👦</h1>
        <p>Tres generaciones dedicadas a crear los mejores piscos y vinos del valle peruano con técnicas artesanales.</p>
        <a href="historia.php" class="btn premium-glow">Nuestra historia</a>
      </div>
    </div>

    <!-- Controles del carrusel -->
    <button class="carousel-nav carousel-prev" id="prevBtn">‹</button>
    <button class="carousel-nav carousel-next" id="nextBtn">›</button>

    <!-- Indicadores -->
    <div class="carousel-controls">
      <div class="carousel-dot active" data-slide="0"></div>
      <div class="carousel-dot" data-slide="1"></div>
      <div class="carousel-dot" data-slide="2"></div>
      <div class="carousel-dot" data-slide="3"></div>
    </div>
  </section>
  <!-- Sección principal -->
  <section class="content-section" style="margin: 80px auto; max-width: 900px; text-align: center; padding: 0 20px;">
    <div class="animate-fade-in">
      <h2 style="color: var(--vino-borgona); font-size: 2.5em; margin-bottom: 20px;">Siente el alma del valle en cada copa</h2>
      <p style="font-size: 1.3em; color: var(--verde-hoja); line-height: 1.6; margin-bottom: 30px;">
        Descubre la pasión, tradición y calidad de nuestros piscos y vinos artesanales, elaborados en el corazón del valle peruano.
      </p>
      <a href="historia.php" class="btn">Conoce nuestra historia</a>
    </div>
  </section>

  <!-- Productos destacados -->
  <section style="margin: 80px auto; max-width: 1200px; padding: 0 20px;">
    <div class="animate-slide-up">
      <h2 class="text-center" style="color: var(--vino-borgona); font-size: 2.2em; margin-bottom: 50px;">Productos destacados</h2>
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 30px;">
        <?php foreach ($destacados as $prod): ?>
          <div class="producto-card"
               data-producto-id="<?php echo $prod['id']; ?>"
               data-producto-nombre="<?php echo htmlspecialchars($prod['nombre']); ?>"
               data-producto-precio="<?php echo $prod['precio_unitario']; ?>"
               data-producto-imagen="<?php echo $prod['imagen_principal']; ?>"
               style="background: var(--blanco-puro); border-radius: 20px; box-shadow: 0 8px 30px rgba(136, 27, 53, 0.1); padding: 30px; text-align: center; transition: all 0.3s ease; border: 2px solid transparent;">
            <img src="<?php echo $prod['imagen_principal']; ?>"
                 alt="<?php echo htmlspecialchars($prod['nombre']); ?>"
                 style="width: 140px; height: 240px; object-fit: contain; margin-bottom: 20px; filter: drop-shadow(0 0 15px rgba(161, 122, 50, 0.3));">
            <h3 style="margin: 0 0 10px 0; color: var(--vino-borgona); font-size: 1.3em;"><?php echo htmlspecialchars($prod['nombre']); ?></h3>
            <div style="color: var(--oro-dorado); font-weight: bold; margin-bottom: 10px; font-size: 1.1em;">
              <?php echo $prod['categoria']; ?> | <?php echo $prod['tamanio']; ?> ml
            </div>
            <div style="margin-bottom: 20px; font-size: 1.2em; color: var(--vino-borgona);">
              <strong>S/ <?php echo number_format($prod['precio_unitario'], 2); ?></strong>
              <span style="color: #888; font-size: 0.9em;">(unidad)</span>
            </div>
            <a href="producto.php?id=<?php echo $prod['id']; ?>" class="btn" style="margin-top: 10px;">Ver detalles</a>
          </div>
        <?php endforeach; ?>
      </div>
    </div>
  </section>
</main>
<script>
// Carrusel Hero Premium
document.addEventListener('DOMContentLoaded', function() {
  const slides = document.querySelectorAll('.hero-slide');
  const dots = document.querySelectorAll('.carousel-dot');
  const prevBtn = document.getElementById('prevBtn');
  const nextBtn = document.getElementById('nextBtn');
  let currentSlide = 0;
  let slideInterval;

  function showSlide(index) {
    // Remover clase active de slide actual
    slides[currentSlide].classList.remove('active');
    dots[currentSlide].classList.remove('active');

    // Calcular nuevo índice
    currentSlide = (index + slides.length) % slides.length;

    // Activar nuevo slide
    slides[currentSlide].classList.add('active');
    dots[currentSlide].classList.add('active');
  }

  function nextSlide() {
    showSlide(currentSlide + 1);
  }

  function prevSlide() {
    showSlide(currentSlide - 1);
  }

  function startAutoSlide() {
    slideInterval = setInterval(nextSlide, 5000); // Cambiar cada 5 segundos
  }

  function stopAutoSlide() {
    clearInterval(slideInterval);
  }

  // Event listeners
  nextBtn.addEventListener('click', () => {
    nextSlide();
    stopAutoSlide();
    startAutoSlide(); // Reiniciar auto-slide
  });

  prevBtn.addEventListener('click', () => {
    prevSlide();
    stopAutoSlide();
    startAutoSlide();
  });

  // Dots navigation
  dots.forEach((dot, index) => {
    dot.addEventListener('click', () => {
      showSlide(index);
      stopAutoSlide();
      startAutoSlide();
    });
  });

  // Pausar auto-slide al hacer hover
  const carousel = document.getElementById('heroCarousel');
  carousel.addEventListener('mouseenter', stopAutoSlide);
  carousel.addEventListener('mouseleave', startAutoSlide);

  // Iniciar auto-slide
  startAutoSlide();

  // Animaciones de entrada para elementos
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // Observar elementos para animaciones
  document.querySelectorAll('.animate-fade-in, .animate-slide-up').forEach(el => {
    el.style.opacity = '0';
    el.style.transform = 'translateY(30px)';
    el.style.transition = 'all 0.8s ease-out';
    observer.observe(el);
  });

  // Efecto hover en productos
  document.querySelectorAll('.producto-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-10px)';
      this.style.boxShadow = '0 15px 40px rgba(136, 27, 53, 0.2)';
      this.style.borderColor = 'var(--oro-dorado)';
    });

    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
      this.style.boxShadow = '0 8px 30px rgba(136, 27, 53, 0.1)';
      this.style.borderColor = 'transparent';
    });
  });
});
</script>
<?php include 'footer.php'; ?>
