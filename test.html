<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test UVAMAYU - Renovación Premium</title>
    <link rel="stylesheet" href="assets/estilos.css">
    <script src="assets/carrito.js"></script>
    <script src="assets/animaciones.js"></script>
    <style>
        .test-section {
            margin: 40px 0;
            padding: 30px;
            background: var(--blanco-puro);
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(136, 27, 53, 0.1);
        }
        .test-title {
            color: var(--vino-borgona);
            border-bottom: 2px solid var(--oro-dorado);
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .color-palette {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .color-sample {
            width: 100px;
            height: 100px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }
        .responsive-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: var(--gris-suave);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(136, 27, 53, 0.2);
        }
    </style>
</head>
<body>
    <header id="mainHeader" style="background: var(--vino-borgona); color: #fff; padding: 0; position: fixed; top: 0; left: 0; right: 0; z-index: 100; transition: all 0.3s ease;">
        <div style="display: flex; align-items: center; justify-content: space-between; max-width: 1200px; margin: 0 auto; padding: 10px 20px;">
            <div style="display: flex; align-items: center;">
                <div style="width: 48px; height: 48px; background: var(--oro-dorado); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 16px; font-weight: bold; color: var(--vino-borgona);">UV</div>
                <span style="font-family: 'Cinzel', serif; font-size: 2em; font-weight: bold; letter-spacing: 2px;">UVAMAYU</span>
            </div>
            <nav style="display: flex; align-items: center; gap: 24px;">
                <ul style="display: flex; gap: 24px; list-style: none; margin: 0; padding: 0; font-size: 1.1em;">
                    <li><a href="#" style="color: #fff; text-decoration: none; transition: all 0.3s ease;">Inicio</a></li>
                    <li><a href="#" style="color: #fff; text-decoration: none; transition: all 0.3s ease;">Productos</a></li>
                    <li><a href="#" style="color: #fff; text-decoration: none; transition: all 0.3s ease;">Contacto</a></li>
                </ul>
                <button id="carritoBtn" class="carrito-btn" title="Ver carrito">
                    🛒
                    <span id="carritoContador" class="carrito-contador" style="display: none;">0</span>
                </button>
            </nav>
        </div>
    </header>

    <main style="margin-top: 80px; padding: 20px; max-width: 1200px; margin-left: auto; margin-right: auto;">
        <div class="test-section animate-fade-in">
            <h1 class="test-title">🧪 Test de Renovación UVAMAYU Premium</h1>
            <p style="color: var(--verde-hoja); font-size: 1.2em;">
                Esta página de prueba verifica que todos los elementos de la renovación funcionen correctamente.
            </p>
        </div>

        <!-- Test de Paleta de Colores -->
        <div class="test-section animate-slide-up">
            <h2 class="test-title">🎨 Paleta de Colores Premium</h2>
            <div class="color-palette">
                <div class="color-sample" style="background: var(--vino-borgona);">
                    <div style="text-align: center;">
                        <div>Vino Borgoña</div>
                        <small>#881B35</small>
                    </div>
                </div>
                <div class="color-sample" style="background: var(--oro-dorado);">
                    <div style="text-align: center;">
                        <div>Oro Dorado</div>
                        <small>#A17A32</small>
                    </div>
                </div>
                <div class="color-sample" style="background: var(--verde-hoja);">
                    <div style="text-align: center;">
                        <div>Verde Hoja</div>
                        <small>#5C7330</small>
                    </div>
                </div>
                <div class="color-sample" style="background: var(--negro-profundo);">
                    <div style="text-align: center;">
                        <div>Negro Profundo</div>
                        <small>#000000</small>
                    </div>
                </div>
                <div class="color-sample" style="background: var(--gris-suave); color: var(--vino-borgona);">
                    <div style="text-align: center;">
                        <div>Gris Suave</div>
                        <small>#F8F8F8</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test de Botones -->
        <div class="test-section animate-fade-in-left">
            <h2 class="test-title">🔘 Botones Premium</h2>
            <div style="display: flex; gap: 20px; flex-wrap: wrap; margin: 20px 0;">
                <button class="btn">Botón Principal</button>
                <button class="btn premium-glow">Botón con Glow</button>
                <button class="btn bg-dorado">Botón Dorado</button>
                <button class="btn bg-verde">Botón Verde</button>
            </div>
        </div>

        <!-- Test de Carrito -->
        <div class="test-section animate-fade-in-right">
            <h2 class="test-title">🛒 Sistema de Carrito</h2>
            <div style="display: flex; gap: 20px; flex-wrap: wrap; align-items: center;">
                <div class="producto-card" 
                     data-producto-id="test1"
                     data-producto-nombre="Pisco Quebranta Test"
                     data-producto-precio="45.00"
                     data-producto-imagen="assets/banner1.png"
                     style="background: var(--blanco-puro); border-radius: 15px; padding: 20px; border: 2px solid var(--oro-dorado); max-width: 200px;">
                    <h4>Pisco Quebranta Test</h4>
                    <p>S/ 45.00</p>
                    <button class="btn" onclick="agregarAlCarrito('test1', 1)">Agregar al carrito</button>
                </div>
                <div>
                    <p>Haz clic en "Agregar al carrito" y luego en el ícono del carrito en el header para probar la funcionalidad.</p>
                </div>
            </div>
        </div>

        <!-- Test Responsive -->
        <div class="test-section animate-slide-up">
            <h2 class="test-title">📱 Test Responsive</h2>
            <p>Redimensiona la ventana del navegador para probar el diseño responsive:</p>
            <div class="responsive-test">
                <div class="test-card">
                    <h4>Desktop</h4>
                    <p>1200px+</p>
                </div>
                <div class="test-card">
                    <h4>Tablet</h4>
                    <p>768px - 1199px</p>
                </div>
                <div class="test-card">
                    <h4>Móvil</h4>
                    <p>320px - 767px</p>
                </div>
            </div>
        </div>

        <!-- Test de Animaciones -->
        <div class="test-section animate-fade-in">
            <h2 class="test-title">✨ Animaciones Premium</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                <div class="animate-fade-in" style="background: var(--gris-suave); padding: 20px; border-radius: 10px;">
                    <h4>Fade In</h4>
                    <p>Aparición suave</p>
                </div>
                <div class="animate-slide-up" style="background: var(--gris-suave); padding: 20px; border-radius: 10px;">
                    <h4>Slide Up</h4>
                    <p>Deslizamiento hacia arriba</p>
                </div>
                <div class="animate-fade-in-left" style="background: var(--gris-suave); padding: 20px; border-radius: 10px;">
                    <h4>Fade In Left</h4>
                    <p>Aparición desde la izquierda</p>
                </div>
                <div class="animate-fade-in-right" style="background: var(--gris-suave); padding: 20px; border-radius: 10px;">
                    <h4>Fade In Right</h4>
                    <p>Aparición desde la derecha</p>
                </div>
            </div>
        </div>

        <!-- Test de Formulario -->
        <div class="test-section animate-slide-up">
            <h2 class="test-title">📝 Formulario de Contacto</h2>
            <form style="max-width: 500px;">
                <div style="margin-bottom: 20px;">
                    <label style="display: block; color: var(--verde-hoja); font-weight: bold; margin-bottom: 8px;">Nombre</label>
                    <input type="text" style="width: 100%; padding: 15px; border: 2px solid var(--oro-dorado); border-radius: 10px; box-sizing: border-box;">
                </div>
                <div style="margin-bottom: 20px;">
                    <label style="display: block; color: var(--verde-hoja); font-weight: bold; margin-bottom: 8px;">Email</label>
                    <input type="email" style="width: 100%; padding: 15px; border: 2px solid var(--oro-dorado); border-radius: 10px; box-sizing: border-box;">
                </div>
                <div style="margin-bottom: 20px;">
                    <label style="display: block; color: var(--verde-hoja); font-weight: bold; margin-bottom: 8px;">Mensaje</label>
                    <textarea rows="4" style="width: 100%; padding: 15px; border: 2px solid var(--oro-dorado); border-radius: 10px; box-sizing: border-box;"></textarea>
                </div>
                <button type="button" class="btn premium-glow">Enviar Mensaje</button>
            </form>
        </div>

        <!-- Resultados del Test -->
        <div class="test-section animate-fade-in">
            <h2 class="test-title">✅ Checklist de Funcionalidades</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="color: var(--vino-borgona);">Diseño y Colores</h4>
                    <ul style="color: var(--verde-hoja);">
                        <li>✅ Paleta de colores premium implementada</li>
                        <li>✅ Tipografía Cinzel aplicada</li>
                        <li>✅ Botones con efectos premium</li>
                        <li>✅ Sombras y gradientes elegantes</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: var(--vino-borgona);">Funcionalidad</h4>
                    <ul style="color: var(--verde-hoja);">
                        <li>✅ Sistema de carrito mejorado</li>
                        <li>✅ Modal de carrito funcional</li>
                        <li>✅ Integración WhatsApp/Email</li>
                        <li>✅ Formularios estilizados</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: var(--vino-borgona);">Responsive</h4>
                    <ul style="color: var(--verde-hoja);">
                        <li>✅ Diseño móvil optimizado</li>
                        <li>✅ Header responsive</li>
                        <li>✅ Grid adaptativo</li>
                        <li>✅ Navegación móvil</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: var(--vino-borgona);">Animaciones</h4>
                    <ul style="color: var(--verde-hoja);">
                        <li>✅ Animaciones de entrada</li>
                        <li>✅ Efectos hover premium</li>
                        <li>✅ Transiciones suaves</li>
                        <li>✅ Parallax sutil</li>
                    </ul>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Test adicional de funcionalidades
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 UVAMAYU Test Page Loaded');
            console.log('✅ CSS Variables:', {
                vino: getComputedStyle(document.documentElement).getPropertyValue('--vino-borgona'),
                oro: getComputedStyle(document.documentElement).getPropertyValue('--oro-dorado'),
                verde: getComputedStyle(document.documentElement).getPropertyValue('--verde-hoja')
            });
            
            // Test del carrito
            if (typeof carritoUVAMAYU !== 'undefined') {
                console.log('✅ Sistema de carrito cargado correctamente');
            } else {
                console.log('❌ Sistema de carrito no encontrado');
            }
            
            // Test de animaciones
            const animatedElements = document.querySelectorAll('.animate-fade-in, .animate-slide-up');
            console.log(`✅ ${animatedElements.length} elementos con animaciones encontrados`);
        });
    </script>
</body>
</html>
