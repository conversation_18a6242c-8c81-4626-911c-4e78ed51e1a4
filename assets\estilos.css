@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&display=swap');

:root {
  --vino-borgona: #881B35;
  --oro-dorado: #A17A32;
  --verde-hoja: #5C7330;
  --negro-profundo: #000000;
  --blanco-puro: #FFFFFF;
  --gris-suave: #F8F8F8;

  /* Colores legacy para compatibilidad */
  --vino: #881B35;
  --dorado: #A17A32;
  --negro: #000000;
  --blanco: #FFFFFF;
  --gris: #F8F8F8;
}

body {
  font-family: 'Cinzel', serif;
  background: var(--blanco-puro);
  color: var(--vino-borgona);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  transition: all 0.3s ease;
  overflow-x: hidden;
}

h1, h2, h3, h4, h5 {
  color: var(--vino-borgona);
  font-family: 'Cinzel', serif;
  font-weight: 700;
}

a {
  color: var(--vino-borgona);
  text-decoration: none;
  transition: all 0.3s ease;
}
a:hover {
  color: var(--oro-dorado);
  transform: translateY(-1px);
}

.btn {
  background: linear-gradient(135deg, var(--vino-borgona), #9a1e3d);
  color: var(--blanco-puro);
  border: none;
  padding: 14px 28px;
  border-radius: 12px;
  font-size: 1.1em;
  font-weight: 600;
  font-family: 'Cinzel', serif;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(136, 27, 53, 0.3);
  transition: all 0.3s ease;
  display: inline-block;
  text-align: center;
  position: relative;
  overflow: hidden;
}
.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}
.btn:hover::before {
  left: 100%;
}
.btn:hover {
  background: linear-gradient(135deg, var(--oro-dorado), #b8864a);
  color: var(--vino-borgona);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(161, 122, 50, 0.4);
}

/* Animaciones premium */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}
@keyframes slideUp {
  from { opacity: 0; transform: translateY(50px); }
  to { opacity: 1; transform: translateY(0); }
}
@keyframes fadeInLeft {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}
@keyframes fadeInRight {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}
@keyframes premiumGlow {
  from {
    box-shadow: 0 0 20px rgba(161, 122, 50, 0.6), 0 4px 20px rgba(136, 27, 53, 0.3);
    transform: scale(1);
  }
  to {
    box-shadow: 0 0 30px rgba(161, 122, 50, 0.8), 0 6px 30px rgba(136, 27, 53, 0.4);
    transform: scale(1.02);
  }
}
.premium-glow {
  animation: premiumGlow 3s infinite alternate;
}
.animate-fade-in {
  animation: fadeIn 0.8s ease-out;
}
.animate-slide-up {
  animation: slideUp 0.8s ease-out;
}
.animate-fade-in-left {
  animation: fadeInLeft 0.8s ease-out;
}
.animate-fade-in-right {
  animation: fadeInRight 0.8s ease-out;
}

/* Carrusel Hero Pantalla Completa */
.hero-carousel {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;
  z-index: 1;
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1s ease-in-out;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-slide.active {
  opacity: 1;
}

.hero-slide::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(136, 27, 53, 0.7), rgba(161, 122, 50, 0.3));
  z-index: 1;
}

.hero-content {
  position: absolute;
  top: 50%;
  left: 10%;
  transform: translateY(-50%);
  z-index: 2;
  color: var(--blanco-puro);
  max-width: 600px;
  text-shadow: 0 4px 20px rgba(0,0,0,0.8);
}

.hero-content h1 {
  font-size: 3.5em;
  color: var(--oro-dorado);
  margin-bottom: 20px;
  font-weight: 700;
  line-height: 1.2;
  animation: fadeInLeft 1s ease-out 0.5s both;
}

.hero-content p {
  font-size: 1.4em;
  margin-bottom: 30px;
  line-height: 1.6;
  animation: fadeInLeft 1s ease-out 0.8s both;
}

.hero-content .btn {
  font-size: 1.2em;
  padding: 18px 35px;
  animation: fadeInLeft 1s ease-out 1.1s both;
}

.carousel-controls {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 15px;
  z-index: 3;
}

.carousel-dot {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-dot.active {
  background: var(--oro-dorado);
  transform: scale(1.3);
}

.carousel-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(161, 122, 50, 0.8);
  border: none;
  color: var(--blanco-puro);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  font-size: 1.5em;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 3;
}

.carousel-nav:hover {
  background: var(--oro-dorado);
  transform: translateY(-50%) scale(1.1);
}

.carousel-prev {
  left: 30px;
}

.carousel-next {
  right: 30px;
}

/* Ajuste para el contenido después del carrusel */
.content-section {
  margin-top: 0;
  padding-top: 80px;
}

/* Responsive para carrusel */
@media (max-width: 768px) {
  .hero-content {
    left: 5%;
    right: 5%;
    max-width: 90%;
  }

  .hero-content h1 {
    font-size: 2.5em;
  }

  .hero-content p {
    font-size: 1.1em;
  }

  .carousel-nav {
    width: 50px;
    height: 50px;
    font-size: 1.2em;
  }

  .carousel-prev {
    left: 15px;
  }

  .carousel-next {
    right: 15px;
  }
}

/* Utilidades */
.text-center { text-align: center; }
.text-vino { color: var(--vino-borgona); }
.text-dorado { color: var(--oro-dorado); }
.text-verde { color: var(--verde-hoja); }
.bg-vino { background: var(--vino-borgona); color: var(--blanco-puro); }
.bg-dorado { background: var(--oro-dorado); color: var(--vino-borgona); }
.bg-verde { background: var(--verde-hoja); color: var(--blanco-puro); }
.bg-negro { background: var(--negro-profundo); color: var(--blanco-puro); }
.bg-gris { background: var(--gris-suave); color: var(--vino-borgona); }

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden { display: none !important; }
  .mobile-center { text-align: center !important; }
  .mobile-full-width { width: 100% !important; }
}

/* Responsive Design - Mobile First */
/* Dispositivos móviles pequeños (320px - 480px) */
@media (max-width: 480px) {
  body {
    font-size: 14px;
  }

  .btn {
    padding: 12px 20px;
    font-size: 0.9em;
  }

  h1 { font-size: 1.8em !important; }
  h2 { font-size: 1.5em !important; }
  h3 { font-size: 1.3em !important; }

  /* Header responsive */
  header div {
    padding: 8px 10px !important;
    flex-wrap: wrap;
  }

  header nav ul {
    gap: 8px !important;
    font-size: 0.8em !important;
  }

  header nav ul li a {
    padding: 5px 8px;
    border-radius: 5px;
  }

  header img {
    height: 35px !important;
    margin-right: 8px !important;
  }

  .carrito-btn {
    font-size: 1.2em !important;
    padding: 5px !important;
  }
}

/* Dispositivos móviles medianos (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  body {
    font-size: 15px;
  }

  .btn {
    padding: 13px 24px;
    font-size: 1em;
  }

  h1 { font-size: 2.2em !important; }
  h2 { font-size: 1.8em !important; }
  h3 { font-size: 1.4em !important; }
}

/* Tablets (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .hero-content {
    left: 8%;
    max-width: 500px;
  }

  .hero-content h1 {
    font-size: 3em;
  }

  .hero-content p {
    font-size: 1.2em;
  }
}

/* Responsive específico para móviles 16:9 vertical */
@media (max-width: 768px) and (orientation: portrait) {
  /* Carrusel hero ajustado */
  .hero-carousel {
    height: 100vh;
  }

  .hero-content {
    top: 40%;
    left: 5%;
    right: 5%;
    max-width: 90%;
    text-align: center;
  }

  .hero-content h1 {
    font-size: 2.2em;
    line-height: 1.1;
    margin-bottom: 15px;
  }

  .hero-content p {
    font-size: 1em;
    margin-bottom: 20px;
    line-height: 1.4;
  }

  .hero-content .btn {
    font-size: 1em;
    padding: 14px 25px;
  }

  /* Controles del carrusel */
  .carousel-nav {
    width: 45px;
    height: 45px;
    font-size: 1.1em;
  }

  .carousel-prev { left: 10px; }
  .carousel-next { right: 10px; }

  .carousel-controls {
    bottom: 20px;
  }

  .carousel-dot {
    width: 12px;
    height: 12px;
  }

  /* Secciones de contenido */
  .content-section {
    padding: 40px 15px !important;
    margin: 40px auto !important;
  }

  .content-section h2 {
    font-size: 1.8em !important;
    margin-bottom: 15px !important;
  }

  .content-section p {
    font-size: 1em !important;
    margin-bottom: 20px !important;
  }

  /* Grid de productos responsive */
  section[style*="grid-template-columns"] {
    display: block !important;
  }

  .producto-card {
    margin-bottom: 25px !important;
    padding: 20px !important;
  }

  .producto-card img {
    width: 100px !important;
    height: 180px !important;
  }

  .producto-card h3 {
    font-size: 1.1em !important;
  }

  /* Modal del carrito responsive */
  .carrito-modal-content {
    width: 95% !important;
    max-height: 90vh !important;
    margin: 20px auto !important;
  }

  .carrito-header {
    padding: 15px !important;
  }

  .carrito-header h2 {
    font-size: 1.3em !important;
  }

  .carrito-body {
    padding: 15px !important;
    max-height: 300px !important;
  }

  .carrito-item {
    flex-wrap: wrap;
    gap: 10px;
  }

  .carrito-item-img {
    width: 50px !important;
    height: 70px !important;
  }

  .carrito-item-info {
    min-width: 120px;
  }

  .carrito-item-controls {
    gap: 8px;
  }

  .cantidad-btn {
    width: 25px !important;
    height: 25px !important;
    font-size: 0.9em;
  }

  .carrito-footer {
    padding: 15px !important;
    flex-direction: column;
    gap: 15px;
  }

  .carrito-actions {
    width: 100%;
    justify-content: space-between;
  }

  .carrito-actions .btn {
    flex: 1;
    margin: 0 5px;
    font-size: 0.9em;
    padding: 12px 15px;
  }
}

/* Ajustes específicos para pantallas muy pequeñas */
@media (max-width: 360px) {
  .hero-content h1 {
    font-size: 1.8em !important;
  }

  .hero-content p {
    font-size: 0.9em !important;
  }

  .hero-content .btn {
    font-size: 0.9em !important;
    padding: 12px 20px !important;
  }

  header nav ul {
    flex-wrap: wrap;
    justify-content: center;
  }

  header nav ul li {
    margin: 2px 0;
  }
}

/* Estilos del carrito */
.carrito-btn {
  position: relative;
  background: none;
  border: none;
  color: var(--blanco-puro);
  font-size: 1.5em;
  cursor: pointer;
  padding: 8px;
  transition: all 0.3s ease;
}
.carrito-btn:hover {
  color: var(--oro-dorado);
  transform: scale(1.1);
}
.carrito-contador {
  position: absolute;
  top: -5px;
  right: -5px;
  background: var(--oro-dorado);
  color: var(--vino-borgona);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 0.7em;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 0.5s ease-in-out;
}

.carrito-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.carrito-modal-content {
  background: var(--blanco-puro);
  border-radius: 20px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease;
}

.carrito-header {
  background: linear-gradient(135deg, var(--vino-borgona), #9a1e3d);
  color: var(--blanco-puro);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.carrito-header h2 {
  margin: 0;
  color: var(--blanco-puro);
  font-size: 1.5em;
}

.carrito-close {
  background: none;
  border: none;
  color: var(--blanco-puro);
  font-size: 2em;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.carrito-close:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

.carrito-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.carrito-vacio {
  text-align: center;
  color: var(--vino-borgona);
  font-size: 1.2em;
  padding: 40px 20px;
}

.carrito-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid var(--gris-suave);
}

.carrito-item:last-child {
  border-bottom: none;
}

.carrito-item-img {
  width: 60px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.carrito-item-info {
  flex: 1;
}

.carrito-item-info h4 {
  margin: 0 0 5px 0;
  color: var(--vino-borgona);
  font-size: 1em;
}

.carrito-item-precio {
  color: var(--oro-dorado);
  font-weight: bold;
  font-size: 0.9em;
}

.carrito-item-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.cantidad-btn {
  background: var(--oro-dorado);
  color: var(--vino-borgona);
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.cantidad-btn:hover {
  background: var(--vino-borgona);
  color: var(--blanco-puro);
  transform: scale(1.1);
}

.cantidad {
  font-weight: bold;
  color: var(--vino-borgona);
  min-width: 30px;
  text-align: center;
}

.carrito-item-subtotal {
  font-weight: bold;
  color: var(--vino-borgona);
  min-width: 80px;
  text-align: right;
}

.eliminar-btn {
  background: #dc3545;
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2em;
  transition: all 0.3s ease;
}

.eliminar-btn:hover {
  background: #c82333;
  transform: scale(1.1);
}

.carrito-total {
  border-top: 2px solid var(--oro-dorado);
  padding-top: 15px;
  margin-top: 15px;
}

.total-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.3em;
  font-weight: bold;
  color: var(--vino-borgona);
}

.carrito-footer {
  background: var(--gris-suave);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
}

.btn-secondary {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-family: 'Cinzel', serif;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.carrito-actions {
  display: flex;
  gap: 10px;
}

.carrito-notificacion {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--verde-hoja);
  color: var(--blanco-puro);
  padding: 15px 20px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 1001;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  font-family: 'Cinzel', serif;
  font-weight: 600;
}

.carrito-notificacion.show {
  transform: translateX(0);
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}