<?php
$page_title = "Contacto";
include 'includes/header.php';

// Obtener producto desde URL si existe
$producto_consulta = isset($_GET['producto']) ? $_GET['producto'] : '';
?>

<section class="contact-hero">
    <div class="container">
        <div class="hero-content">
            <h1 class="page-title">Contáctanos 📞</h1>
            <p class="page-subtitle">Estamos aquí para atenderte y resolver todas tus consultas sobre nuestros productos</p>
        </div>
    </div>
</section>

<section class="contact-section">
    <div class="container">
        <div class="contact-content">
            <!-- Formulario de contacto -->
            <div class="contact-form-container">
                <h2 class="form-title">Envíanos un mensaje 💬</h2>
                <form class="contact-form" id="contactForm">
                    <div class="form-group">
                        <label for="nombre">Nombre completo *</label>
                        <input type="text" id="nombre" name="nombre" required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">Email *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="telefono">Teléfono</label>
                            <input type="tel" id="telefono" name="telefono">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="producto">Producto de interés</label>
                        <select id="producto" name="producto">
                            <option value="">Selecciona un producto</option>
                            <option value="Pisco Acholado 750ml" <?php echo $producto_consulta === 'Pisco Acholado 750ml' ? 'selected' : ''; ?>>Pisco Acholado 750ml</option>
                            <option value="Pisco Italia 750ml" <?php echo $producto_consulta === 'Pisco Italia 750ml' ? 'selected' : ''; ?>>Pisco Italia 750ml</option>
                            <option value="Pisco Quebranta 750ml" <?php echo $producto_consulta === 'Pisco Quebranta 750ml' ? 'selected' : ''; ?>>Pisco Quebranta 750ml</option>
                            <option value="Pisco Torontel 750ml" <?php echo $producto_consulta === 'Pisco Torontel 750ml' ? 'selected' : ''; ?>>Pisco Torontel 750ml</option>
                            <option value="Pisco Mosto Verde 750ml" <?php echo $producto_consulta === 'Pisco Mosto Verde 750ml' ? 'selected' : ''; ?>>Pisco Mosto Verde 750ml</option>
                            <option value="Vino Perfecto Amor 750ml" <?php echo $producto_consulta === 'Vino Perfecto Amor 750ml' ? 'selected' : ''; ?>>Vino Perfecto Amor 750ml</option>
                            <option value="Vino Borgoña 750ml" <?php echo $producto_consulta === 'Vino Borgoña 750ml' ? 'selected' : ''; ?>>Vino Borgoña 750ml</option>
                            <option value="Vino Naranja 750ml" <?php echo $producto_consulta === 'Vino Naranja 750ml' ? 'selected' : ''; ?>>Vino Naranja 750ml</option>
                            <option value="Vino Ciruela 750ml" <?php echo $producto_consulta === 'Vino Ciruela 750ml' ? 'selected' : ''; ?>>Vino Ciruela 750ml</option>
                            <option value="Otros tamaños">Otros tamaños (100ml, 3750ml)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="asunto">Asunto *</label>
                        <select id="asunto" name="asunto" required>
                            <option value="">Selecciona un asunto</option>
                            <option value="Consulta de productos">Consulta de productos 🍇</option>
                            <option value="Solicitud de cotización">Solicitud de cotización 💰</option>
                            <option value="Información de distribución">Información de distribución 🚚</option>
                            <option value="Visita a bodega">Visita a bodega 🏭</option>
                            <option value="Colaboraciones">Colaboraciones 🤝</option>
                            <option value="Otros">Otros</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="mensaje">Mensaje *</label>
                        <textarea id="mensaje" name="mensaje" rows="5" required placeholder="Cuéntanos en qué podemos ayudarte..."></textarea>
                    </div>
                    
                    <button type="submit" class="btn-submit">
                        Enviar Mensaje 📤
                    </button>
                </form>
            </div>
            
            <!-- Información de contacto -->
            <div class="contact-info">
                <h2 class="info-title">Información de contacto 📍</h2>
                
                <div class="contact-methods">
                    <div class="contact-method">
                        <div class="method-icon">📧</div>
                        <div class="method-content">
                            <h3>Email</h3>
                            <a href="mailto:<?php echo $config['contacto']['email']; ?>"><?php echo $config['contacto']['email']; ?></a>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <div class="method-icon">📱</div>
                        <div class="method-content">
                            <h3>WhatsApp</h3>
                            <a href="https://wa.me/<?php echo str_replace(['+', ' ', '-'], '', $config['contacto']['whatsapp']); ?>" target="_blank"><?php echo $config['contacto']['whatsapp']; ?></a>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <div class="method-icon">📞</div>
                        <div class="method-content">
                            <h3>Teléfono</h3>
                            <a href="tel:<?php echo $config['contacto']['telefono']; ?>"><?php echo $config['contacto']['telefono']; ?></a>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <div class="method-icon">📍</div>
                        <div class="method-content">
                            <h3>Ubicación</h3>
                            <p><?php echo $config['contacto']['direccion_completa']; ?></p>
                            <a href="<?php echo $config['contacto']['maps_url']; ?>" target="_blank" class="btn-maps">Ver en Maps</a>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <div class="method-icon">🕒</div>
                        <div class="method-content">
                            <h3>Horarios</h3>
                            <p><?php echo $config['contacto']['horarios']; ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="social-contact">
                    <h3>Síguenos en redes sociales 📱</h3>
                    <div class="social-buttons">
                        <a href="<?php echo $config['redes_sociales']['facebook']; ?>" target="_blank" class="social-btn facebook">
                            📘 Facebook
                        </a>
                        <a href="<?php echo $config['redes_sociales']['instagram']; ?>" target="_blank" class="social-btn instagram">
                            📷 Instagram
                        </a>
                        <a href="<?php echo $config['redes_sociales']['tiktok']; ?>" target="_blank" class="social-btn tiktok">
                            🎵 TikTok
                        </a>
                        <a href="<?php echo $config['redes_sociales']['linktree']; ?>" target="_blank" class="social-btn linktree">
                            🔗 Linktree
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="map-section">
    <div class="container">
        <h2 class="map-title">Nuestra Ubicación 📍</h2>
        <p class="map-subtitle">Visítanos en nuestras instalaciones en el corazón del valle</p>

        <div class="map-container">
            <iframe
                src="https://www.google.com/maps/embed?pb=!1m17!1m12!1m3!1d1950.09725950249!2d-76.99020190206714!3d-12.167156295641954!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m2!1m1!2s!5e0!3m2!1ses-419!2spe!4v1752423982203!5m2!1ses-419!2spe"
                width="100%"
                height="450"
                style="border:0;"
                allowfullscreen=""
                loading="lazy"
                referrerpolicy="no-referrer-when-downgrade"
                class="google-map">
            </iframe>
        </div>

        <div class="map-info">
            <div class="map-info-card">
                <h3>🚗 Cómo llegar</h3>
                <p>Estamos ubicados en una zona de fácil acceso. Puedes llegar en transporte público o vehículo particular.</p>
                <a href="<?php echo $config['contacto']['maps_url']; ?>" target="_blank" class="btn-directions">
                    🧭 Obtener Direcciones
                </a>
            </div>

            <div class="map-info-card">
                <h3>🕒 Horarios de Atención</h3>
                <p><?php echo $config['contacto']['horarios']; ?></p>
                <p class="map-note">Te recomendamos llamar antes de visitarnos para coordinar tu visita.</p>
            </div>
        </div>
    </div>
</section>

<style>
    .contact-hero {
        background: linear-gradient(135deg, var(--gris-oscuro) 0%, var(--negro) 100%);
        padding: 8rem 0 4rem;
        text-align: center;
    }
    
    .page-title {
        font-family: 'Playfair Display', serif;
        font-size: 3rem;
        color: var(--dorado);
        margin-bottom: 1rem;
        animation: fadeInUp 0.6s ease-out;
    }
    
    .page-subtitle {
        font-size: 1.3rem;
        color: #ccc;
        max-width: 600px;
        margin: 0 auto;
        animation: fadeInUp 0.6s ease-out 0.2s both;
    }
    
    .contact-section {
        padding: 4rem 0;
        background: var(--negro);
    }
    
    .contact-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        align-items: start;
    }
    
    .contact-form-container {
        background: var(--gris-medio);
        padding: 3rem;
        border-radius: 15px;
        animation: fadeInUp 0.6s ease-out;
    }
    
    .form-title {
        font-family: 'Playfair Display', serif;
        font-size: 2rem;
        color: var(--dorado);
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .contact-form {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
    }
    
    .form-group label {
        color: var(--dorado);
        margin-bottom: 0.5rem;
        font-weight: 500;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        background: var(--gris-oscuro);
        border: 2px solid transparent;
        color: white;
        padding: 1rem;
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: var(--dorado);
        box-shadow: 0 0 10px rgba(205, 170, 88, 0.3);
    }
    
    .form-group textarea {
        resize: vertical;
        min-height: 120px;
    }
    
    .btn-submit {
        background: linear-gradient(45deg, var(--dorado), var(--guinda));
        color: var(--negro);
        border: none;
        padding: 1.2rem 2rem;
        border-radius: 8px;
        font-weight: bold;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        margin-top: 1rem;
    }
    
    .btn-submit:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(205, 170, 88, 0.4);
    }
    
    .contact-info {
        animation: fadeInUp 0.6s ease-out 0.3s both;
    }
    
    .info-title {
        font-family: 'Playfair Display', serif;
        font-size: 2rem;
        color: var(--dorado);
        margin-bottom: 2rem;
    }
    
    .contact-methods {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        margin-bottom: 3rem;
    }
    
    .contact-method {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1.5rem;
        background: var(--gris-medio);
        border-radius: 10px;
        transition: transform 0.3s ease;
    }
    
    .contact-method:hover {
        transform: translateX(10px);
    }
    
    .method-icon {
        font-size: 2rem;
        flex-shrink: 0;
    }
    
    .method-content h3 {
        color: var(--dorado);
        margin-bottom: 0.5rem;
        font-size: 1.2rem;
    }
    
    .method-content p,
    .method-content a {
        color: #ccc;
        text-decoration: none;
        transition: color 0.3s ease;
    }
    
    .method-content a:hover {
        color: var(--dorado);
    }
    
    .btn-maps {
        display: inline-block;
        background: var(--verde);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        text-decoration: none;
        font-size: 0.9rem;
        margin-top: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .btn-maps:hover {
        background: var(--dorado);
        color: var(--negro);
    }
    
    .social-contact h3 {
        color: var(--dorado);
        margin-bottom: 1rem;
        font-size: 1.3rem;
    }
    
    .social-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .social-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 1rem;
        background: var(--gris-medio);
        color: var(--dorado);
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.3s ease;
        font-weight: 500;
    }
    
    .social-btn:hover {
        background: var(--dorado);
        color: var(--negro);
        transform: translateY(-3px);
    }

    .map-section {
        padding: 4rem 0;
        background: var(--gris-oscuro);
    }

    .map-title {
        font-family: 'Playfair Display', serif;
        font-size: 2.5rem;
        color: var(--dorado);
        text-align: center;
        margin-bottom: 1rem;
        animation: fadeInUp 0.6s ease-out;
    }

    .map-subtitle {
        text-align: center;
        color: #ccc;
        font-size: 1.2rem;
        margin-bottom: 3rem;
        animation: fadeInUp 0.6s ease-out 0.2s both;
    }

    .map-container {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.5);
        margin-bottom: 3rem;
        animation: fadeInUp 0.6s ease-out 0.4s both;
        position: relative;
    }

    .google-map {
        width: 100%;
        height: 450px;
        border: none;
        transition: all 0.3s ease;
    }

    .map-container:hover .google-map {
        transform: scale(1.02);
    }

    .map-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        animation: fadeInUp 0.6s ease-out 0.6s both;
    }

    .map-info-card {
        background: var(--gris-medio);
        padding: 2rem;
        border-radius: 12px;
        text-align: center;
        transition: transform 0.3s ease;
    }

    .map-info-card:hover {
        transform: translateY(-5px);
    }

    .map-info-card h3 {
        color: var(--dorado);
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }

    .map-info-card p {
        color: #ccc;
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .map-note {
        font-size: 0.9rem;
        color: #999;
        font-style: italic;
    }

    .btn-directions {
        display: inline-block;
        background: linear-gradient(45deg, var(--verde), var(--dorado));
        color: var(--negro);
        text-decoration: none;
        padding: 0.8rem 1.5rem;
        border-radius: 8px;
        font-weight: bold;
        transition: all 0.3s ease;
        text-transform: uppercase;
        font-size: 0.9rem;
    }

    .btn-directions:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(91, 107, 51, 0.4);
    }
    
    @media (max-width: 768px) {
        .page-title {
            font-size: 2.5rem;
        }
        
        .contact-content {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .contact-form-container {
            padding: 2rem;
        }
        
        .form-row {
            grid-template-columns: 1fr;
        }
        
        .social-buttons {
            grid-template-columns: 1fr;
        }

        .map-title {
            font-size: 2rem;
        }

        .map-info {
            grid-template-columns: 1fr;
        }

        .google-map {
            height: 300px;
        }
    }
</style>

<script>
document.getElementById('contactForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    // Validar formulario
    const nombre = document.getElementById('nombre').value.trim();
    const email = document.getElementById('email').value.trim();
    const asunto = document.getElementById('asunto').value;
    const mensaje = document.getElementById('mensaje').value.trim();
    const telefono = document.getElementById('telefono').value.trim();
    const producto = document.getElementById('producto').value;

    if (!nombre || !email || !asunto || !mensaje) {
        window.uvamayuApp.showNotification('Por favor completa todos los campos obligatorios', 'error');
        return;
    }

    if (!validateEmail(email)) {
        window.uvamayuApp.showNotification('Por favor ingresa un email válido', 'error');
        return;
    }

    // Preparar datos
    const formData = {
        nombre: nombre,
        email: email,
        telefono: telefono,
        producto: producto,
        asunto: asunto,
        mensaje: mensaje
    };

    const submitBtn = document.querySelector('.btn-submit');
    const originalText = submitBtn.textContent;

    submitBtn.textContent = 'Enviando... ⏳';
    submitBtn.disabled = true;

    try {
        const response = await fetch('/procesar_contacto.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            window.uvamayuApp.showNotification(result.message, 'success');
            document.getElementById('contactForm').reset();
        } else {
            window.uvamayuApp.showNotification(result.message || 'Error al enviar el mensaje', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        window.uvamayuApp.showNotification('Error de conexión. Intenta nuevamente.', 'error');
    } finally {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
});
</script>

<?php include 'includes/footer.php'; ?>
