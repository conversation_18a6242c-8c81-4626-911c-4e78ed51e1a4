# 🎯 EJEMPLOS DE USO COMPLETO - SISTEMA UVAMAYU

## 📋 Flujo Completo: Desde Inversión hasta Venta

### 1. 💰 REGISTRAR INVERSIÓN INICIAL

```javascript
// Sebastian invierte S/ 2000 para comprar materiales
const inversion = await fetch('http://tudominio.com/api/presupuesto.php/inversion', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        inversor_id: 1, // <PERSON>
        monto: 2000.00,
        concepto: "Capital inicial para producción",
        fecha_inversion: "2025-01-23",
        tipo_inversion: "capital_inicial",
        notas: "Inversión para arrancar producción de enero"
    })
});
```

### 2. 🛒 REGISTRAR GASTOS EN MATERIALES

#### Comprar Etiquetas
```javascript
const gastoEtiquetas = await fetch('http://tudominio.com/api/presupuesto.php/gasto', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        categoria: "etiquetas",
        subcategoria: "Etiquetas Pisco Quebranta 750ml",
        monto: 60.00,
        cantidad: 200, // 200 etiquetas
        precio_unitario: 0.30,
        proveedor: "Imprenta San Martín",
        fecha_gasto: "2025-01-23",
        descripcion: "Etiquetas adelante y atrás para Pisco Quebranta",
        inversor_id: 1, // Sebastian pagó
        comprobante: "F001-123",
        actualizar_stock: true,
        etiqueta_id: 8 // ID de la etiqueta en inventario
    })
});
```

#### Comprar Botellas y Tapones
```javascript
const gastoBotellas = await fetch('http://tudominio.com/api/presupuesto.php/gasto', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        categoria: "botellas_tapones_capsulas",
        subcategoria: "Botellas transparentes 750ml + corchos + cápsulas",
        monto: 150.00,
        cantidad: 50, // 50 botellas
        precio_unitario: 3.00,
        proveedor: "Distribuidora Vitivinícola",
        fecha_gasto: "2025-01-23",
        descripcion: "50 botellas + corchos + cápsulas negras",
        inversor_id: 2, // Papá pagó
        comprobante: "B002-456"
    })
});
```

#### Comprar Producto (Pisco)
```javascript
const gastoProducto = await fetch('http://tudominio.com/api/presupuesto.php/gasto', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        categoria: "producto_liquido",
        subcategoria: "Pisco Quebranta artesanal",
        monto: 1500.00,
        cantidad: 50000, // 50 litros = 50,000 ml
        precio_unitario: 0.03, // S/ 0.03 por ml
        proveedor: "Destilería Valle Verde",
        fecha_gasto: "2025-01-23",
        descripcion: "50 litros de Pisco Quebranta premium",
        inversor_id: 3, // Empresa pagó
        comprobante: "F003-789",
        actualizar_stock: true,
        liquido_id: 2 // ID del Pisco Quebranta
    })
});
```

### 3. 🏭 PROCESO DE EMBOTELLADO

#### Validar Disponibilidad
```javascript
const validacion = await fetch('http://tudominio.com/api/embotellado.php/validar?liquido_id=2&configuracion_id=1&cantidad_botellas=50');
const resultado = await validacion.json();

if (resultado.data.puede_embotellar) {
    console.log("✅ Se puede embotellar");
} else {
    console.log("❌ Faltan componentes:", resultado.data.faltantes);
}
```

#### Procesar Embotellado
```javascript
const embotellado = await fetch('http://tudominio.com/api/embotellado.php/procesar', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        liquido_id: 2, // Pisco Quebranta
        configuracion_id: 1, // Pisco 750ml
        cantidad_botellas: 50,
        fecha_embotellado: "2025-01-23",
        usuario: "Sebastian",
        notas: "Primer lote de producción 2025"
    })
});

const lote = await embotellado.json();
console.log("Lote creado:", lote.data.codigo_lote); // LOTE-20250123-001
```

### 4. 💼 VERIFICAR ESTADO FINANCIERO

```javascript
const dashboard = await fetch('http://tudominio.com/api/presupuesto.php/dashboard');
const finanzas = await dashboard.json();

console.log("💰 Estado Financiero:");
console.log("Total invertido:", finanzas.data.total_inversiones);
console.log("Gastado en etiquetas:", finanzas.data.gastos_etiquetas_mes);
console.log("Gastado en botellas:", finanzas.data.gastos_botellas_mes);
console.log("Gastado en producto:", finanzas.data.gastos_producto_mes);
console.log("Saldo disponible:", finanzas.data.saldo_disponible);
```

### 5. 🛍️ REALIZAR VENTAS

#### Venta Normal
```javascript
const venta = await fetch('http://tudominio.com/api/ventas.php/procesar', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        productos: [
            {
                lote_id: 1, // Del lote recién embotellado
                producto_nombre: "Pisco Quebranta",
                capacidad_ml: 750,
                cantidad: 2,
                precio_unitario: 23.00,
                es_regalo: false
            }
        ],
        cliente_nombre: "María González",
        cliente_telefono: "+51987654321",
        fecha_venta: "2025-01-24",
        es_regalo: false,
        metodo_pago: "efectivo",
        vendedor: "Sebastian",
        notas: "Cliente frecuente"
    })
});

const ventaResult = await venta.json();
console.log("Venta procesada:", ventaResult.data.codigo_venta); // VENTA-20250124-001
console.log("Total:", ventaResult.data.total); // S/ 46.00
```

#### Venta de Regalo (Promoción)
```javascript
const regalo = await fetch('http://tudominio.com/api/ventas.php/procesar', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        productos: [
            {
                lote_id: 1,
                producto_nombre: "Pisco Quebranta",
                capacidad_ml: 750,
                cantidad: 1,
                precio_unitario: 23.00,
                es_regalo: true
            }
        ],
        cliente_nombre: "Cliente VIP",
        fecha_venta: "2025-01-24",
        es_regalo: true,
        motivo_regalo: "Promoción lanzamiento",
        vendedor: "Sebastian",
        notas: "Regalo por compra anterior de S/ 500"
    })
});
```

### 6. 📊 GENERAR REPORTES

#### Reporte de Producción
```javascript
const produccion = await fetch('http://tudominio.com/api/reportes.php/produccion?fecha_desde=2025-01-01&fecha_hasta=2025-01-31');
const reporteProduccion = await produccion.json();

console.log("📈 Reporte de Producción:");
console.log("Lotes producidos:", reporteProduccion.data.totales.lotes_total);
console.log("Botellas embotelladas:", reporteProduccion.data.totales.botellas_total);
console.log("ML totales usados:", reporteProduccion.data.totales.ml_total);
```

#### Stock Bajo
```javascript
const stockBajo = await fetch('http://tudominio.com/api/reportes.php/stock-bajo');
const alertas = await stockBajo.json();

console.log("⚠️ Componentes con stock bajo:");
alertas.data.forEach(item => {
    if (item.porcentaje_stock < 50) {
        console.log(`${item.nombre}: ${item.stock_actual}/${item.stock_minimo} (${item.porcentaje_stock}%)`);
    }
});
```

#### Resumen Mensual
```javascript
const resumen = await fetch('http://tudominio.com/api/presupuesto.php/resumen-mensual?año=2025&mes=1');
const resumenMes = await resumen.json();

console.log("📅 Resumen Enero 2025:");
console.log("Inversiones:", resumenMes.data.inversiones_mes);
console.log("Gastos totales:", resumenMes.data.total_gastos);
console.log("Ventas:", resumenMes.data.ventas_mes);
console.log("Utilidad:", resumenMes.data.utilidad_mes);
console.log("Flujo neto:", resumenMes.data.flujo_neto);
```

---

## 🔄 FLUJOS ESPECÍFICOS

### Flujo de Reposición de Stock
```javascript
// 1. Verificar qué falta
const stockBajo = await fetch('http://tudominio.com/api/reportes.php/stock-bajo');

// 2. Registrar nueva inversión
const nuevaInversion = await fetch('http://tudominio.com/api/presupuesto.php/inversion', {
    method: 'POST',
    body: JSON.stringify({
        inversor_id: 1,
        monto: 500.00,
        concepto: "Reposición de etiquetas",
        fecha_inversion: "2025-01-25",
        tipo_inversion: "reposicion_stock"
    })
});

// 3. Registrar compra y actualizar stock
const compra = await fetch('http://tudominio.com/api/presupuesto.php/gasto', {
    method: 'POST',
    body: JSON.stringify({
        categoria: "etiquetas",
        monto: 150.00,
        cantidad: 500,
        actualizar_stock: true,
        etiqueta_id: 8
    })
});
```

### Flujo de Control de Calidad
```javascript
// 1. Validar antes de embotellar
const validacion = await fetch('http://tudominio.com/api/validaciones.php/disponibilidad-embotellado?liquido_id=2&configuracion_id=1&cantidad_botellas=100');

// 2. Simular costos
const simulacion = await fetch('http://tudominio.com/api/validaciones.php/simular-embotellado', {
    method: 'POST',
    body: JSON.stringify({
        liquido_id: 2,
        configuracion_id: 1,
        cantidad_botellas: 100
    })
});

// 3. Verificar consistencia de datos
const consistencia = await fetch('http://tudominio.com/api/validaciones.php/consistencia');
```

### Flujo de Análisis de Rentabilidad
```javascript
// 1. Obtener costos de producción
const costos = await fetch('http://tudominio.com/api/reportes.php/costos');

// 2. Obtener ventas del período
const ventas = await fetch('http://tudominio.com/api/ventas.php/historial?fecha_desde=2025-01-01');

// 3. Calcular rentabilidad por producto
const dashboard = await fetch('http://tudominio.com/api/presupuesto.php/dashboard');

// 4. Generar reporte personalizado
const rentabilidad = {
    costos_produccion: costos.data.valor_total_inventario,
    ventas_periodo: ventas.data.reduce((sum, v) => sum + parseFloat(v.total), 0),
    margen_bruto: ventas_periodo - costos_produccion,
    roi: ((ventas_periodo - costos_produccion) / costos_produccion) * 100
};
```

---

## 🎯 CASOS DE USO AVANZADOS

### 1. **Embotellado Masivo con Control de Costos**
```javascript
const lotes = [
    { liquido_id: 2, configuracion_id: 1, cantidad: 50 }, // Quebranta 750ml
    { liquido_id: 1, configuracion_id: 1, cantidad: 30 }, // Acholado 750ml
    { liquido_id: 3, configuracion_id: 1, cantidad: 20 }  // Italia 750ml
];

for (const lote of lotes) {
    // Validar y simular cada lote
    const simulacion = await fetch('http://tudominio.com/api/validaciones.php/simular-embotellado', {
        method: 'POST',
        body: JSON.stringify(lote)
    });
    
    const sim = await simulacion.json();
    console.log(`Costo estimado ${lote.cantidad} botellas: S/ ${sim.data.costos_estimados.total}`);
    
    // Procesar si es rentable
    if (sim.data.puede_embotellar) {
        await fetch('http://tudominio.com/api/embotellado.php/procesar', {
            method: 'POST',
            body: JSON.stringify({
                ...lote,
                fecha_embotellado: "2025-01-25",
                usuario: "Sebastian"
            })
        });
    }
}
```

### 2. **Análisis de Inversores**
```javascript
const inversores = await fetch('http://tudominio.com/api/presupuesto.php/inversores');
const invData = await inversores.json();

for (const inversor of invData.data) {
    console.log(`${inversor.nombre}:`);
    console.log(`  Invertido: S/ ${inversor.total_invertido}`);
    console.log(`  Gastado: S/ ${inversor.total_gastado}`);
    console.log(`  Saldo: S/ ${inversor.total_invertido - inversor.total_gastado}`);
}
```

### 3. **Control de Stock Automático**
```javascript
// Función para verificar y alertar stock bajo
async function verificarStockAutomatico() {
    const stock = await fetch('http://tudominio.com/api/reportes.php/stock-bajo');
    const alertas = await stock.json();
    
    const criticos = alertas.data.filter(item => item.porcentaje_stock < 20);
    
    if (criticos.length > 0) {
        // Enviar notificación (email, WhatsApp, etc.)
        console.log("🚨 ALERTA: Stock crítico detectado");
        criticos.forEach(item => {
            console.log(`- ${item.nombre}: Solo ${item.stock_actual} unidades`);
        });
        
        // Generar orden de compra automática
        return generarOrdenCompra(criticos);
    }
}

// Ejecutar cada día
setInterval(verificarStockAutomatico, 24 * 60 * 60 * 1000);
```

¡Con estos ejemplos puedes implementar un sistema completo de gestión para UVAMAYU! 🍷✨
