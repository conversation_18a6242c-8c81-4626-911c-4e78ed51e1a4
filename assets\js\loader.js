// UVAMAYU - Sistema de Carga y Lazy Loading
class UvamayuLoader {
    constructor() {
        this.imagesLoaded = 0;
        this.totalImages = 0;
        this.pageLoader = null;
        this.progressBar = null;
        this.init();
    }

    init() {
        this.createPageLoader();
        this.preloadCriticalImages();
        // Configurar lazy loading después de que todo esté cargado
        setTimeout(() => {
            this.setupLazyLoading();
        }, 2000);
    }

    createPageLoader() {
        // Crear loader principal de página
        const loader = document.createElement('div');
        loader.className = 'page-loader';
        loader.innerHTML = `
            <div class="loader-content">
                <div class="loader-logo">UVAMAYU</div>
                <div class="loader-subtitle">Reserva del sol, espíritu del valle</div>
                <div class="loader-spinner"></div>
                <div class="loader-progress">
                    <div class="loader-progress-bar"></div>
                </div>
                <div class="loader-text">Cargando experiencia premium... 🍇</div>
            </div>
        `;
        
        document.body.appendChild(loader);
        this.pageLoader = loader;
        this.progressBar = loader.querySelector('.loader-progress-bar');
    }

    preloadCriticalImages() {
        // Simplificar la carga - solo esperar un tiempo fijo
        // para no interferir con el carrusel
        this.updateProgress(30);

        setTimeout(() => {
            this.updateProgress(60);
        }, 500);

        setTimeout(() => {
            this.updateProgress(90);
        }, 1000);

        setTimeout(() => {
            this.updateProgress(100);
            this.hidePageLoader();
        }, 1500);
    }

    updateProgress(percentage) {
        if (this.progressBar) {
            this.progressBar.style.width = `${percentage}%`;
        }
    }

    hidePageLoader() {
        if (this.pageLoader) {
            this.pageLoader.classList.add('hidden');
            document.body.style.overflow = 'auto';
            
            setTimeout(() => {
                this.pageLoader.remove();
            }, 500);
        }
    }

    setupLazyLoading() {
        // Configurar Intersection Observer para lazy loading
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    this.loadImage(img);
                    observer.unobserve(img);
                }
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.1
        });

        // Observar todas las imágenes lazy
        document.querySelectorAll('.lazy-image').forEach(img => {
            imageObserver.observe(img);
        });
    }

    loadImage(img) {
        const container = img.closest('.image-container');
        const placeholder = container?.querySelector('.image-placeholder');
        
        // Crear imagen temporal para precargar
        const tempImg = new Image();
        
        tempImg.onload = () => {
            // Imagen cargada exitosamente
            img.src = tempImg.src;
            img.classList.add('loaded');
            
            if (placeholder) {
                setTimeout(() => {
                    placeholder.classList.add('hidden');
                }, 100);
            }
            
            // Trigger evento personalizado
            img.dispatchEvent(new CustomEvent('imageLoaded', {
                detail: { src: img.src }
            }));
        };
        
        tempImg.onerror = () => {
            // Error al cargar imagen
            img.src = this.createPlaceholderDataURL(img.dataset.width || 300, img.dataset.height || 200);
            img.classList.add('loaded', 'error');
            
            if (placeholder) {
                placeholder.innerHTML = '<div class="placeholder-icon">❌</div>';
                setTimeout(() => {
                    placeholder.classList.add('hidden');
                }, 100);
            }
        };
        
        // Iniciar carga
        tempImg.src = img.dataset.src || img.src;
    }

    createPlaceholderDataURL(width, height) {
        // Crear placeholder SVG
        const svg = `
            <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#333333;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#1a1a1a;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="100%" height="100%" fill="url(#grad)"/>
                <text x="50%" y="50%" font-family="Arial" font-size="16" fill="#CDAA58" text-anchor="middle" dy=".3em">🍇 UVAMAYU</text>
            </svg>
        `;
        
        return `data:image/svg+xml;base64,${btoa(svg)}`;
    }

    setupCarouselLoading() {
        // El carrusel maneja sus propias imágenes de fondo
        // No interferir con el sistema existente del carrusel
        console.log('🎠 Carrusel: usando imágenes de fondo nativas');
    }

    // Método para crear contenedores de imagen con placeholder
    createImageContainer(src, alt, className = '') {
        const container = document.createElement('div');
        container.className = `image-container ${className}`;
        
        const placeholder = document.createElement('div');
        placeholder.className = 'image-placeholder';
        placeholder.innerHTML = '<div class="placeholder-icon">🍇</div>';
        
        const img = document.createElement('img');
        img.className = 'lazy-image';
        img.dataset.src = src;
        img.alt = alt;
        
        container.appendChild(placeholder);
        container.appendChild(img);
        
        return container;
    }

    // Método para mostrar skeleton loading
    showSkeleton(container) {
        const skeleton = document.createElement('div');
        skeleton.className = 'skeleton-card';
        skeleton.innerHTML = `
            <div class="skeleton skeleton-image"></div>
            <div class="skeleton skeleton-text large"></div>
            <div class="skeleton skeleton-text"></div>
            <div class="skeleton skeleton-text small"></div>
        `;
        
        container.appendChild(skeleton);
        return skeleton;
    }

    // Método para ocultar skeleton loading
    hideSkeleton(skeleton) {
        if (skeleton) {
            skeleton.style.opacity = '0';
            setTimeout(() => {
                skeleton.remove();
            }, 300);
        }
    }
}

// Función helper para convertir imágenes existentes a lazy loading
function convertToLazyLoading() {
    document.querySelectorAll('img:not(.lazy-image)').forEach(img => {
        // Excluir imágenes del carrusel y otras que no deben ser lazy
        const isCarouselImage = img.closest('.carousel-slide');
        const isNoLazy = img.classList.contains('no-lazy');
        const isAlreadyLazy = img.classList.contains('lazy-image');

        if (img.src && !isCarouselImage && !isNoLazy && !isAlreadyLazy) {
            // Crear contenedor
            const container = document.createElement('div');
            container.className = 'image-container';

            // Crear placeholder
            const placeholder = document.createElement('div');
            placeholder.className = 'image-placeholder';
            placeholder.innerHTML = '<div class="placeholder-icon">🍇</div>';

            // Modificar imagen original
            const originalSrc = img.src;
            img.dataset.src = originalSrc;
            img.src = '';
            img.classList.add('lazy-image');

            // Insertar en DOM
            img.parentNode.insertBefore(container, img);
            container.appendChild(placeholder);
            container.appendChild(img);
        }
    });
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    // Bloquear scroll inicial
    document.body.style.overflow = 'hidden';

    // Inicializar loader inmediatamente
    window.uvamayuLoader = new UvamayuLoader();

    // Esperar a que todo esté cargado para convertir imágenes
    window.addEventListener('load', () => {
        // Convertir imágenes existentes (excepto las del carrusel)
        convertToLazyLoading();

        // Reinicializar lazy loading
        if (window.uvamayuLoader) {
            window.uvamayuLoader.setupLazyLoading();
        }
    });
});

// Exportar para uso global
window.UvamayuLoader = UvamayuLoader;
