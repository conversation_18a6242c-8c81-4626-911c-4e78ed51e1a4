<?php
/**
 * Archivo de prueba para diagnosticar problemas en el servidor
 */

echo "<h1>🔍 DIAGNÓSTICO DEL SERVIDOR UVAMAYU</h1>";

// 1. Verificar PHP
echo "<h2>1. Información de PHP</h2>";
echo "Versión PHP: " . phpversion() . "<br>";
echo "Servidor: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";

// 2. Verificar extensiones necesarias
echo "<h2>2. Extensiones PHP</h2>";
$extensiones = ['pdo', 'pdo_mysql', 'json', 'curl'];
foreach ($extensiones as $ext) {
    $status = extension_loaded($ext) ? "✅ Instalada" : "❌ NO instalada";
    echo "$ext: $status<br>";
}

// 3. Verificar archivos
echo "<h2>3. Archivos del Sistema</h2>";
$archivos = [
    'config.php',
    'api/config/Database.php',
    'api/config/ApiResponse.php',
    'api/liquidos.php',
    'api/ventas.php',
    'api/presupuesto.php'
];

foreach ($archivos as $archivo) {
    $existe = file_exists($archivo) ? "✅ Existe" : "❌ NO existe";
    echo "$archivo: $existe<br>";
}

// 4. Verificar permisos
echo "<h2>4. Permisos de Archivos</h2>";
foreach ($archivos as $archivo) {
    if (file_exists($archivo)) {
        $permisos = substr(sprintf('%o', fileperms($archivo)), -4);
        echo "$archivo: $permisos<br>";
    }
}

// 5. Test de conexión a base de datos
echo "<h2>5. Test de Conexión a Base de Datos</h2>";
try {
    $host = 'localhost'; // Cambiar si es necesario
    $dbname = 'uvamayuc_db';
    $username = 'uvamayuc_sebas';
    $password = 'Sebastian090101@R';
    
    $pdo = new PDO(
        "mysql:host=$host;dbname=$dbname;charset=utf8",
        $username,
        $password,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "✅ Conexión a base de datos exitosa<br>";
    
    // Verificar tablas
    $stmt = $pdo->query("SHOW TABLES");
    $tablas = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Tablas encontradas: " . count($tablas) . "<br>";
    foreach ($tablas as $tabla) {
        echo "- $tabla<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error de conexión: " . $e->getMessage() . "<br>";
}

// 6. Test de APIs
echo "<h2>6. Test de APIs</h2>";
$apis = [
    'api/liquidos.php',
    'api/ventas.php',
    'api/presupuesto.php'
];

foreach ($apis as $api) {
    if (file_exists($api)) {
        echo "Testing $api...<br>";
        
        // Simular llamada GET
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['PATH_INFO'] = '';
        
        ob_start();
        try {
            include $api;
            $output = ob_get_contents();
            echo "✅ $api responde correctamente<br>";
        } catch (Exception $e) {
            echo "❌ Error en $api: " . $e->getMessage() . "<br>";
        }
        ob_end_clean();
    }
}

// 7. Verificar .htaccess
echo "<h2>7. Configuración .htaccess</h2>";
if (file_exists('.htaccess')) {
    echo "✅ .htaccess existe<br>";
    echo "<pre>" . htmlspecialchars(file_get_contents('.htaccess')) . "</pre>";
} else {
    echo "❌ .htaccess NO existe<br>";
}

// 8. Variables de entorno
echo "<h2>8. Variables del Servidor</h2>";
echo "DOCUMENT_ROOT: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "HTTP_HOST: " . $_SERVER['HTTP_HOST'] . "<br>";
echo "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "<br>";

// 9. Logs de errores
echo "<h2>9. Configuración de Errores</h2>";
echo "display_errors: " . ini_get('display_errors') . "<br>";
echo "log_errors: " . ini_get('log_errors') . "<br>";
echo "error_log: " . ini_get('error_log') . "<br>";

// Activar errores para debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>✅ Diagnóstico Completado</h2>";
echo "<p>Si ves este mensaje, PHP está funcionando correctamente.</p>";
?>
