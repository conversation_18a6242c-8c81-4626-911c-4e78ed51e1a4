<!DOCTYPE html>
<html>
<head>
    <title>Test Básico UVAMAYU</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>🔍 Test Básico UVAMAYU</h1>
    
    <h2>1. Test HTML</h2>
    <p class="success">✅ HTML funciona correctamente</p>
    
    <h2>2. Test JavaScript</h2>
    <p id="js-test" class="error">❌ JavaScript no funciona</p>
    
    <h2>3. Test de Conectividad</h2>
    <button onclick="testPHP()">Probar PHP</button>
    <button onclick="testAPI()">Probar API</button>
    <div id="results"></div>
    
    <h2>4. Información del Navegador</h2>
    <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
    <p><strong>URL Actual:</strong> <span id="current-url"></span></p>
    <p><strong>Hora:</strong> <span id="current-time"></span></p>
    
    <script>
        // Test JavaScript básico
        document.getElementById('js-test').innerHTML = '✅ JavaScript funciona correctamente';
        document.getElementById('js-test').className = 'success';
        
        // Información del navegador
        document.getElementById('user-agent').textContent = navigator.userAgent;
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('current-time').textContent = new Date().toString();
        
        // Test PHP
        function testPHP() {
            const results = document.getElementById('results');
            results.innerHTML = '<p class="info">🔄 Probando PHP...</p>';
            
            fetch('debug.php')
                .then(response => {
                    if (response.ok) {
                        return response.text();
                    }
                    throw new Error('HTTP ' + response.status);
                })
                .then(data => {
                    results.innerHTML = '<h3 class="success">✅ PHP Funciona</h3><div style="max-height: 300px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px;">' + data + '</div>';
                })
                .catch(error => {
                    results.innerHTML = '<h3 class="error">❌ Error PHP</h3><p>' + error.message + '</p>';
                });
        }
        
        // Test API
        function testAPI() {
            const results = document.getElementById('results');
            results.innerHTML = '<p class="info">🔄 Probando API...</p>';
            
            fetch('api/liquidos.php')
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    }
                    throw new Error('HTTP ' + response.status);
                })
                .then(data => {
                    results.innerHTML = '<h3 class="success">✅ API Funciona</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                })
                .catch(error => {
                    results.innerHTML = '<h3 class="error">❌ Error API</h3><p>' + error.message + '</p>';
                });
        }
        
        // Test automático al cargar
        window.onload = function() {
            console.log('🔍 Test básico cargado');
            console.log('URL:', window.location.href);
            console.log('Protocolo:', window.location.protocol);
            console.log('Host:', window.location.host);
        };
    </script>
</body>
</html>
