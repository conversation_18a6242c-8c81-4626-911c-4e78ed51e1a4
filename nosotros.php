<?php
$page_title = "Nosotros";
include 'includes/header.php';
?>

<section class="hero-nosotros">
    <div class="hero-overlay"></div>
    <div class="container">
        <div class="hero-content">
            <h1 class="page-title">Nuestra Historia 📖</h1>
            <p class="page-subtitle">Tradición que trasciende al tiempo, nos transformamos para llegar a tu vida</p>
        </div>
    </div>
</section>

<section class="historia-section">
    <div class="container">
        <div class="historia-content">
            <div class="historia-text">
                <h2 class="section-title">El Origen de UVAMAYU 🍇</h2>
                <p>En el corazón del valle de Ica, donde el sol abraza la tierra con su calor eterno, nace UVAMAYU. Nuestro nombre, que significa "río de uvas" en quechua, refleja la esencia de nuestra misión: crear productos que fluyan como un río de tradición y sabor.</p>
                
                <p>Fundada por una familia apasionada por la vitivinicultura peruana, UVAMAYU representa más de tres generaciones dedicadas al arte de transformar la uva en elixires únicos. Cada botella cuenta una historia, cada sorbo es un viaje a nuestras raíces. 🌱</p>
                
                <p>Desde nuestros inicios, hemos mantenido un compromiso inquebrantable con la calidad y la autenticidad. Utilizamos métodos artesanales transmitidos de padre a hijo, combinados con técnicas modernas que respetan la esencia tradicional de nuestros productos.</p>
            </div>
            <div class="historia-image image-container">
                <div class="image-placeholder">
                    <div class="placeholder-icon">📖</div>
                </div>
                <img class="lazy-image historia-img" data-src="/assets/historia/fundacion.png" alt="Fundación de UVAMAYU - Historia de la destilería artesanal peruana" data-width="500" data-height="400">
            </div>
        </div>
    </div>
</section>

<section class="proceso-section">
    <div class="container">
        <div class="proceso-content">
            <div class="proceso-image image-container">
                <div class="image-placeholder">
                    <div class="placeholder-icon">⚗️</div>
                </div>
                <img class="lazy-image proceso-img" data-src="/assets/historia/proceso_tradicion.png" alt="Proceso tradicional de destilación artesanal UVAMAYU" data-width="500" data-height="400">
            </div>
            <div class="proceso-text">
                <h2 class="section-title">Nuestro Proceso Artesanal ⚗️</h2>
                <div class="proceso-steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>Selección de Uvas 🍇</h3>
                            <p>Cuidadosa selección de las mejores uvas de nuestros viñedos, cosechadas en el momento óptimo de maduración.</p>
                        </div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>Fermentación Natural 🌿</h3>
                            <p>Proceso de fermentación controlada utilizando levaduras naturales que preservan los aromas y sabores únicos.</p>
                        </div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3>Destilación Artesanal 🔥</h3>
                            <p>Destilación en alambiques de cobre siguiendo técnicas tradicionales para obtener la máxima pureza.</p>
                        </div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h3>Reposo y Maduración ⏰</h3>
                            <p>Período de reposo que permite el desarrollo completo de aromas y sabores característicos.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="valores-section">
    <div class="container">
        <h2 class="section-title center">Nuestros Valores 💎</h2>
        <div class="valores-grid">
            <div class="valor-card">
                <div class="valor-icon">🏆</div>
                <h3>Calidad Premium</h3>
                <p>Cada producto pasa por rigurosos controles de calidad para garantizar la excelencia en cada botella.</p>
            </div>
            
            <div class="valor-card">
                <div class="valor-icon">🌱</div>
                <h3>Tradición Familiar</h3>
                <p>Preservamos las técnicas ancestrales transmitidas de generación en generación en nuestra familia.</p>
            </div>
            
            <div class="valor-card">
                <div class="valor-icon">🇵🇪</div>
                <h3>Orgullo Peruano</h3>
                <p>Promovemos la cultura vitivinícola peruana llevando nuestros productos a mesas de todo el país.</p>
            </div>
            
            <div class="valor-card">
                <div class="valor-icon">🤝</div>
                <h3>Compromiso Social</h3>
                <p>Apoyamos a las comunidades locales y promovemos el desarrollo sostenible de la región.</p>
            </div>
        </div>
    </div>
</section>

<section class="equipo-section">
    <div class="container">
        <h2 class="section-title center">Nuestro Equipo 👥</h2>
        <div class="equipo-content">
            <p class="equipo-intro">Detrás de cada botella de UVAMAYU hay un equipo apasionado de artesanos, enólogos y especialistas comprometidos con la excelencia. Desde nuestros maestros destiladores hasta nuestro equipo de ventas, cada miembro de la familia UVAMAYU aporta su experiencia y dedicación para crear productos excepcionales.</p>
            
            <div class="equipo-image image-container">
                <div class="image-placeholder">
                    <div class="placeholder-icon">👥</div>
                </div>
                <img class="lazy-image equipo-img" data-src="/assets/ambiente/reunion_familiar.png" alt="Equipo UVAMAYU - Familia y trabajadores de la destilería artesanal" data-width="600" data-height="400">
            </div>
        </div>
    </div>
</section>

<section class="cta-section">
    <div class="container">
        <div class="cta-content">
            <h2>¿Listo para probar la tradición? 🍷</h2>
            <p>Descubre nuestros productos premium y únete a la familia UVAMAYU</p>
            <div class="cta-buttons">
                <a href="/productos.php" class="btn-premium">Ver Productos</a>
                <a href="/contacto.php" class="btn-secondary">Contáctanos</a>
            </div>
        </div>
    </div>
</section>

<style>
    .hero-nosotros {
        background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), url('/assets/secciones/vinedos.png');
        background-size: cover;
        background-position: center;
        height: 60vh;
        display: flex;
        align-items: center;
        position: relative;
    }
    
    .hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
        text-align: center;
        color: white;
    }
    
    .page-title {
        font-family: 'Playfair Display', serif;
        font-size: 3.5rem;
        color: var(--dorado);
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        animation: fadeInUp 0.6s ease-out;
    }
    
    .page-subtitle {
        font-size: 1.4rem;
        max-width: 800px;
        margin: 0 auto;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        animation: fadeInUp 0.6s ease-out 0.2s both;
    }
    
    .historia-section, .proceso-section {
        padding: 4rem 0;
        background: var(--negro);
    }
    
    .historia-content, .proceso-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        align-items: center;
    }
    
    .section-title {
        font-family: 'Playfair Display', serif;
        font-size: 2.5rem;
        color: var(--dorado);
        margin-bottom: 2rem;
        animation: fadeInUp 0.6s ease-out;
    }
    
    .section-title.center {
        text-align: center;
        margin-bottom: 3rem;
    }
    
    .historia-text p, .proceso-text p {
        color: #ccc;
        line-height: 1.8;
        margin-bottom: 1.5rem;
        animation: fadeInUp 0.6s ease-out;
    }
    
    .historia-img, .proceso-img {
        width: 100%;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.5);
        animation: fadeInUp 0.6s ease-out 0.3s both;
    }
    
    .proceso-steps {
        margin-top: 2rem;
    }
    
    .step {
        display: flex;
        align-items: flex-start;
        margin-bottom: 2rem;
        animation: fadeInUp 0.6s ease-out;
    }
    
    .step-number {
        background: var(--dorado);
        color: var(--negro);
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 1.5rem;
        flex-shrink: 0;
    }
    
    .step-content h3 {
        color: var(--dorado);
        margin-bottom: 0.5rem;
    }
    
    .step-content p {
        color: #ccc;
        margin: 0;
    }
    
    .valores-section {
        padding: 4rem 0;
        background: var(--gris-oscuro);
    }
    
    .valores-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
    }
    
    .valor-card {
        background: var(--gris-medio);
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        transition: transform 0.3s ease;
        animation: fadeInUp 0.6s ease-out;
    }
    
    .valor-card:hover {
        transform: translateY(-10px);
    }
    
    .valor-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .valor-card h3 {
        color: var(--dorado);
        margin-bottom: 1rem;
        font-size: 1.3rem;
    }
    
    .valor-card p {
        color: #ccc;
        line-height: 1.6;
    }
    
    .equipo-section {
        padding: 4rem 0;
        background: var(--negro);
    }
    
    .equipo-intro {
        color: #ccc;
        font-size: 1.1rem;
        line-height: 1.8;
        text-align: center;
        max-width: 800px;
        margin: 0 auto 3rem;
        animation: fadeInUp 0.6s ease-out;
    }
    
    .equipo-image {
        text-align: center;
        animation: fadeInUp 0.6s ease-out 0.3s both;
    }
    
    .equipo-img {
        max-width: 600px;
        width: 100%;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.5);
    }
    
    .cta-section {
        padding: 4rem 0;
        background: linear-gradient(135deg, var(--guinda) 0%, var(--verde) 100%);
        text-align: center;
    }
    
    .cta-content h2 {
        font-family: 'Playfair Display', serif;
        font-size: 2.5rem;
        color: white;
        margin-bottom: 1rem;
        animation: fadeInUp 0.6s ease-out;
    }
    
    .cta-content p {
        font-size: 1.2rem;
        color: white;
        margin-bottom: 2rem;
        animation: fadeInUp 0.6s ease-out 0.2s both;
    }
    
    .cta-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        animation: fadeInUp 0.6s ease-out 0.4s both;
    }
    
    .btn-secondary {
        background: transparent;
        color: white;
        border: 2px solid white;
        padding: 1rem 2rem;
        text-decoration: none;
        border-radius: 8px;
        font-weight: bold;
        transition: all 0.3s ease;
        text-transform: uppercase;
    }
    
    .btn-secondary:hover {
        background: white;
        color: var(--guinda);
        transform: translateY(-2px);
    }
    
    @media (max-width: 768px) {
        .page-title {
            font-size: 2.5rem;
        }
        
        .historia-content, .proceso-content {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .valores-grid {
            grid-template-columns: 1fr;
        }
        
        .cta-buttons {
            flex-direction: column;
            align-items: center;
        }
        
        .btn-premium, .btn-secondary {
            width: 200px;
        }
    }
</style>

<?php include 'includes/footer.php'; ?>
