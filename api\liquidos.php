<?php
/**
 * API para gestión de líquidos (productos base)
 * Endpoints: GET, POST, PUT, DELETE /api/liquidos.php
 */

require_once 'config/Database.php';
require_once 'config/ApiResponse.php';

// Headers CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// Manejar preflight OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $path_info = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
    $id = $path_info ? (int)ltrim($path_info, '/') : null;
    
    switch ($method) {
        case 'GET':
            if ($id) {
                getLiquido($db, $id);
            } else {
                getLiquidos($db);
            }
            break;
            
        case 'POST':
            createLiquido($db);
            break;
            
        case 'PUT':
            if (!$id) {
                ApiResponse::error("ID requerido para actualizar", 400);
            }
            updateLiquido($db, $id);
            break;
            
        case 'DELETE':
            if (!$id) {
                ApiResponse::error("ID requerido para eliminar", 400);
            }
            deleteLiquido($db, $id);
            break;
            
        default:
            ApiResponse::error("Método no permitido", 405);
    }
    
} catch (Exception $e) {
    error_log("Error en API líquidos: " . $e->getMessage());
    ApiResponse::serverError("Error interno del servidor");
}

/**
 * Obtener todos los líquidos
 */
function getLiquidos($db) {
    try {
        $query = "SELECT * FROM liquidos ORDER BY categoria, nombre";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $liquidos = $stmt->fetchAll();
        
        // Agregar información de stock
        foreach ($liquidos as &$liquido) {
            $liquido['stock_status'] = $liquido['stock_ml'] <= $liquido['stock_minimo_ml'] ? 'bajo' : 'normal';
            $liquido['stock_ml'] = (float)$liquido['stock_ml'];
            $liquido['stock_minimo_ml'] = (float)$liquido['stock_minimo_ml'];
            $liquido['costo_por_ml'] = (float)$liquido['costo_por_ml'];
        }
        
        ApiResponse::success($liquidos, "Líquidos obtenidos exitosamente");
        
    } catch (Exception $e) {
        error_log("Error obteniendo líquidos: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Obtener un líquido específico
 */
function getLiquido($db, $id) {
    try {
        $query = "SELECT * FROM liquidos WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        
        $liquido = $stmt->fetch();
        
        if (!$liquido) {
            ApiResponse::notFound("Líquido no encontrado");
        }
        
        // Convertir tipos numéricos
        $liquido['stock_ml'] = (float)$liquido['stock_ml'];
        $liquido['stock_minimo_ml'] = (float)$liquido['stock_minimo_ml'];
        $liquido['costo_por_ml'] = (float)$liquido['costo_por_ml'];
        $liquido['stock_status'] = $liquido['stock_ml'] <= $liquido['stock_minimo_ml'] ? 'bajo' : 'normal';
        
        ApiResponse::success($liquido, "Líquido obtenido exitosamente");
        
    } catch (Exception $e) {
        error_log("Error obteniendo líquido: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Crear nuevo líquido
 */
function createLiquido($db) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        // Validar campos requeridos
        ApiResponse::validateRequired($data, ['nombre', 'categoria']);
        
        // Validar categoría
        if (!in_array($data['categoria'], ['pisco', 'vino'])) {
            ApiResponse::validationError(['categoria' => 'Categoría debe ser pisco o vino']);
        }
        
        // Verificar que no exista el nombre
        $checkQuery = "SELECT id FROM liquidos WHERE nombre = :nombre";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->bindParam(':nombre', $data['nombre']);
        $checkStmt->execute();
        
        if ($checkStmt->fetch()) {
            ApiResponse::validationError(['nombre' => 'Ya existe un líquido con este nombre']);
        }
        
        $query = "INSERT INTO liquidos (nombre, categoria, descripcion, stock_ml, stock_minimo_ml, costo_por_ml, activo) 
                  VALUES (:nombre, :categoria, :descripcion, :stock_ml, :stock_minimo_ml, :costo_por_ml, :activo)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':nombre', $data['nombre']);
        $stmt->bindParam(':categoria', $data['categoria']);
        $stmt->bindParam(':descripcion', $data['descripcion'] ?? '');
        $stmt->bindParam(':stock_ml', $data['stock_ml'] ?? 0);
        $stmt->bindParam(':stock_minimo_ml', $data['stock_minimo_ml'] ?? 1000);
        $stmt->bindParam(':costo_por_ml', $data['costo_por_ml'] ?? 0);
        $stmt->bindParam(':activo', $data['activo'] ?? true, PDO::PARAM_BOOL);
        
        if ($stmt->execute()) {
            $newId = $db->lastInsertId();
            
            // Registrar movimiento
            registrarMovimiento($db, 'liquido', $newId, 'entrada', 0, $data['stock_ml'] ?? 0, $data['stock_ml'] ?? 0, 'Creación inicial');
            
            ApiResponse::success(['id' => $newId], "Líquido creado exitosamente", 201);
        } else {
            ApiResponse::serverError("Error al crear el líquido");
        }
        
    } catch (Exception $e) {
        error_log("Error creando líquido: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Actualizar líquido
 */
function updateLiquido($db, $id) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        // Verificar que existe
        $checkQuery = "SELECT stock_ml FROM liquidos WHERE id = :id";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->bindParam(':id', $id, PDO::PARAM_INT);
        $checkStmt->execute();
        $current = $checkStmt->fetch();
        
        if (!$current) {
            ApiResponse::notFound("Líquido no encontrado");
        }
        
        $fields = [];
        $params = [':id' => $id];
        
        // Campos actualizables
        $allowedFields = ['nombre', 'categoria', 'descripcion', 'stock_ml', 'stock_minimo_ml', 'costo_por_ml', 'activo'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = :$field";
                $params[":$field"] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            ApiResponse::error("No hay campos para actualizar", 400);
        }
        
        $query = "UPDATE liquidos SET " . implode(', ', $fields) . " WHERE id = :id";
        $stmt = $db->prepare($query);
        
        if ($stmt->execute($params)) {
            // Si se actualizó el stock, registrar movimiento
            if (isset($data['stock_ml']) && $data['stock_ml'] != $current['stock_ml']) {
                $diferencia = $data['stock_ml'] - $current['stock_ml'];
                $tipoMovimiento = $diferencia > 0 ? 'entrada' : 'salida';
                registrarMovimiento($db, 'liquido', $id, $tipoMovimiento, $current['stock_ml'], abs($diferencia), $data['stock_ml'], 'Ajuste manual');
            }
            
            ApiResponse::success(null, "Líquido actualizado exitosamente");
        } else {
            ApiResponse::serverError("Error al actualizar el líquido");
        }
        
    } catch (Exception $e) {
        error_log("Error actualizando líquido: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Eliminar líquido (soft delete)
 */
function deleteLiquido($db, $id) {
    try {
        $query = "UPDATE liquidos SET activo = FALSE WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        
        if ($stmt->execute() && $stmt->rowCount() > 0) {
            ApiResponse::success(null, "Líquido eliminado exitosamente");
        } else {
            ApiResponse::notFound("Líquido no encontrado");
        }
        
    } catch (Exception $e) {
        error_log("Error eliminando líquido: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Registrar movimiento de inventario
 */
function registrarMovimiento($db, $tipoComponente, $componenteId, $tipoMovimiento, $cantidadAnterior, $cantidadMovimiento, $cantidadNueva, $motivo) {
    try {
        $query = "INSERT INTO movimientos_inventario (tipo_componente, componente_id, tipo_movimiento, cantidad_anterior, cantidad_movimiento, cantidad_nueva, motivo) 
                  VALUES (:tipo_componente, :componente_id, :tipo_movimiento, :cantidad_anterior, :cantidad_movimiento, :cantidad_nueva, :motivo)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':tipo_componente', $tipoComponente);
        $stmt->bindParam(':componente_id', $componenteId);
        $stmt->bindParam(':tipo_movimiento', $tipoMovimiento);
        $stmt->bindParam(':cantidad_anterior', $cantidadAnterior);
        $stmt->bindParam(':cantidad_movimiento', $cantidadMovimiento);
        $stmt->bindParam(':cantidad_nueva', $cantidadNueva);
        $stmt->bindParam(':motivo', $motivo);
        
        $stmt->execute();
    } catch (Exception $e) {
        error_log("Error registrando movimiento: " . $e->getMessage());
    }
}
?>
