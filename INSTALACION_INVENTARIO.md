# 🚀 GUÍA DE INSTALACIÓN - SISTEMA DE INVENTARIO UVAMAYU

## 📋 Requisitos del Sistema

### Servidor
- **PHP**: 7.4 o superior
- **MySQL**: 5.7 o superior (o MariaDB 10.2+)
- **Servidor Web**: Apache o Nginx
- **Extensiones PHP**: PDO, PDO_MySQL, JSON

### Opcional
- **SSL**: Certificado para HTTPS (recomendado para producción)
- **Composer**: Para gestión de dependencias (futuras actualizaciones)

---

## 🔧 INSTALACIÓN PASO A PASO

### 1. Preparar Base de Datos

```bash
# Crear base de datos
mysql -u root -p
CREATE DATABASE uvamayu_inventario CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'uvamayu_user'@'localhost' IDENTIFIED BY 'tu_password_seguro';
GRANT ALL PRIVILEGES ON uvamayu_inventario.* TO 'uvamayu_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# Importar estructura y datos iniciales
mysql -u uvamayu_user -p uvamayu_inventario < inventario_db.sql
```

### 2. Configurar Archivos

```bash
# Copiar configuración
cp config.example.php config.php

# Editar configuración
nano config.php
```

**Actualizar config.php:**
```php
<?php
return [
    // Base de datos
    'db_host' => 'localhost',
    'db_name' => 'uvamayu_inventario',
    'db_user' => 'uvamayu_user',
    'db_pass' => 'tu_password_seguro',
    
    // Contacto (mantener configuración existente)
    'correo_contacto' => '<EMAIL>',
    'whatsapp' => '+51 999 999 999',
    
    // Resto de configuración...
];
?>
```

### 3. Configurar Permisos

```bash
# Permisos para archivos PHP
chmod 644 api/*.php
chmod 644 api/config/*.php
chmod 755 api/
chmod 755 api/config/

# Permisos para logs (si los usas)
mkdir logs
chmod 755 logs
```

### 4. Configurar Servidor Web

#### Apache (.htaccess)
```apache
# En la carpeta api/
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)/(.*)$ $1.php/$2 [L,QSA]

# Headers de seguridad
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# CORS (ajustar según necesidades)
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
```

#### Nginx
```nginx
location /api/ {
    try_files $uri $uri/ @api;
}

location @api {
    rewrite ^/api/([^/]+)/(.*)$ /api/$1.php/$2 last;
}

# Headers de seguridad
add_header X-Content-Type-Options nosniff;
add_header X-Frame-Options DENY;
add_header X-XSS-Protection "1; mode=block";

# CORS
add_header Access-Control-Allow-Origin "*";
add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
add_header Access-Control-Allow-Headers "Content-Type, Authorization";
```

---

## 🧪 VERIFICACIÓN DE INSTALACIÓN

### 1. Test de Conexión a Base de Datos

```bash
# Crear archivo test_db.php
cat > test_db.php << 'EOF'
<?php
try {
    $config = require 'config.php';
    $pdo = new PDO(
        "mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8",
        $config['db_user'],
        $config['db_pass']
    );
    echo "✅ Conexión a base de datos exitosa\n";
    
    // Verificar tablas
    $tables = ['liquidos', 'botellas', 'tapones', 'capsulas', 'etiquetas'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "✅ Tabla $table: $count registros\n";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
EOF

php test_db.php
```

### 2. Test de APIs

```bash
# Test API líquidos
curl -X GET "http://tudominio.com/api/liquidos.php" \
     -H "Content-Type: application/json"

# Debería devolver JSON con lista de líquidos
```

### 3. Test de Embotellado

```bash
# Test configuraciones
curl -X GET "http://tudominio.com/api/embotellado.php/configuraciones" \
     -H "Content-Type: application/json"

# Test validación
curl -X GET "http://tudominio.com/api/embotellado.php/validar?liquido_id=1&configuracion_id=1&cantidad_botellas=10" \
     -H "Content-Type: application/json"
```

---

## 📊 DATOS INICIALES

### Cargar Stock Inicial

```sql
-- Actualizar stock de líquidos (en ml)
UPDATE liquidos SET stock_ml = 10000 WHERE nombre = 'Pisco Quebranta';
UPDATE liquidos SET stock_ml = 8000 WHERE nombre = 'Pisco Acholado';
UPDATE liquidos SET stock_ml = 5000 WHERE nombre = 'Pisco Italia';
UPDATE liquidos SET stock_ml = 6000 WHERE nombre = 'Vino Naranja';
UPDATE liquidos SET stock_ml = 4000 WHERE nombre = 'Vino Mistela';
UPDATE liquidos SET stock_ml = 3000 WHERE nombre = 'Vino Ciruela';
UPDATE liquidos SET stock_ml = 2000 WHERE nombre = 'Vino Perfecto Amor';

-- Actualizar stock de botellas
UPDATE botellas SET stock_cantidad = 500 WHERE nombre = 'Botella Transparente 750ml';
UPDATE botellas SET stock_cantidad = 300 WHERE nombre = 'Botella Verde 750ml';
UPDATE botellas SET stock_cantidad = 100 WHERE nombre = 'Botella Transparente 4000ml';
UPDATE botellas SET stock_cantidad = 1000 WHERE nombre = 'Botella Transparente 50ml';
UPDATE botellas SET stock_cantidad = 50 WHERE nombre = 'Porrón';

-- Actualizar stock de tapones
UPDATE tapones SET stock_cantidad = 1000 WHERE nombre = 'Corcho Sintético';
UPDATE tapones SET stock_cantidad = 1500 WHERE nombre = 'Tapa Rosca Negra';
UPDATE tapones SET stock_cantidad = 100 WHERE nombre = 'Tapa Porrón';

-- Actualizar stock de cápsulas
UPDATE capsulas SET stock_cantidad = 500 WHERE nombre = 'Cápsula 750ml Negro';
UPDATE capsulas SET stock_cantidad = 400 WHERE nombre = 'Cápsula 750ml Dorado';
UPDATE capsulas SET stock_cantidad = 200 WHERE nombre = 'Cápsula 4000ml Negro';
UPDATE capsulas SET stock_cantidad = 150 WHERE nombre = 'Cápsula 4000ml Dorado';

-- Actualizar stock de etiquetas (ejemplo para algunas)
UPDATE etiquetas SET stock_cantidad = 1000 WHERE producto_liquido = 'quebranta' AND tipo_etiqueta = 'botella_750ml_adelante';
UPDATE etiquetas SET stock_cantidad = 1000 WHERE producto_liquido = 'quebranta' AND tipo_etiqueta = 'botella_750ml_atras';
UPDATE etiquetas SET stock_cantidad = 800 WHERE producto_liquido = 'acholado' AND tipo_etiqueta = 'botella_750ml_adelante';
UPDATE etiquetas SET stock_cantidad = 800 WHERE producto_liquido = 'acholado' AND tipo_etiqueta = 'botella_750ml_atras';
```

---

## 🔒 SEGURIDAD

### 1. Configuración de Producción

```php
// En config.php para producción
return [
    // ... configuración existente ...
    
    // Seguridad
    'environment' => 'production',
    'debug' => false,
    'log_errors' => true,
    'error_log_path' => '/var/log/uvamayu/api.log',
];
```

### 2. Restricciones de Acceso

```apache
# .htaccess para proteger archivos sensibles
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>
```

### 3. Rate Limiting (Opcional)

```php
// Agregar al inicio de cada API
session_start();
$max_requests = 100; // por hora
$time_window = 3600; // 1 hora

if (!isset($_SESSION['api_requests'])) {
    $_SESSION['api_requests'] = [];
}

$now = time();
$_SESSION['api_requests'] = array_filter($_SESSION['api_requests'], function($time) use ($now, $time_window) {
    return ($now - $time) < $time_window;
});

if (count($_SESSION['api_requests']) >= $max_requests) {
    http_response_code(429);
    echo json_encode(['error' => 'Rate limit exceeded']);
    exit;
}

$_SESSION['api_requests'][] = $now;
```

---

## 📱 INTEGRACIÓN CON APLICACIONES

### Headers Requeridos

```javascript
// JavaScript/React/Vue
const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
};

// Ejemplo de llamada
fetch('http://tudominio.com/api/liquidos.php', {
    method: 'GET',
    headers: headers
})
.then(response => response.json())
.then(data => console.log(data));
```

### Manejo de Errores

```javascript
// Función helper para llamadas API
async function apiCall(endpoint, method = 'GET', data = null) {
    try {
        const config = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };
        
        if (data) {
            config.body = JSON.stringify(data);
        }
        
        const response = await fetch(`http://tudominio.com/api/${endpoint}`, config);
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.message);
        }
        
        return result.data;
    } catch (error) {
        console.error('API Error:', error);
        throw error;
    }
}
```

---

## 🔧 MANTENIMIENTO

### Backup Automático

```bash
#!/bin/bash
# backup_inventario.sh
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u uvamayu_user -p uvamayu_inventario > backup_inventario_$DATE.sql
gzip backup_inventario_$DATE.sql

# Mantener solo últimos 30 backups
find . -name "backup_inventario_*.sql.gz" -mtime +30 -delete
```

### Monitoreo de Stock

```sql
-- Query para alertas diarias
SELECT 
    'ALERTA STOCK BAJO' as tipo,
    tipo_componente,
    nombre_componente,
    stock_actual,
    stock_minimo
FROM alertas_stock 
WHERE estado = 'activa'
ORDER BY (stock_actual/stock_minimo) ASC;
```

### Logs de Auditoría

```sql
-- Consultar movimientos recientes
SELECT 
    mi.*,
    CASE 
        WHEN mi.tipo_componente = 'liquido' THEN l.nombre
        WHEN mi.tipo_componente = 'botella' THEN b.nombre
        WHEN mi.tipo_componente = 'tapon' THEN t.nombre
        WHEN mi.tipo_componente = 'capsula' THEN c.nombre
        WHEN mi.tipo_componente = 'etiqueta' THEN e.nombre
    END as nombre_componente
FROM movimientos_inventario mi
LEFT JOIN liquidos l ON mi.tipo_componente = 'liquido' AND mi.componente_id = l.id
LEFT JOIN botellas b ON mi.tipo_componente = 'botella' AND mi.componente_id = b.id
LEFT JOIN tapones t ON mi.tipo_componente = 'tapon' AND mi.componente_id = t.id
LEFT JOIN capsulas c ON mi.tipo_componente = 'capsula' AND mi.componente_id = c.id
LEFT JOIN etiquetas e ON mi.tipo_componente = 'etiqueta' AND mi.componente_id = e.id
WHERE DATE(mi.created_at) = CURDATE()
ORDER BY mi.created_at DESC;
```

---

## ✅ CHECKLIST DE INSTALACIÓN

- [ ] Base de datos creada e importada
- [ ] Archivo config.php configurado
- [ ] Permisos de archivos establecidos
- [ ] Servidor web configurado
- [ ] Test de conexión exitoso
- [ ] APIs respondiendo correctamente
- [ ] Stock inicial cargado
- [ ] Configuraciones de seguridad aplicadas
- [ ] Backup configurado
- [ ] Documentación revisada

¡Sistema de inventario UVAMAYU listo para producción! 🎉
