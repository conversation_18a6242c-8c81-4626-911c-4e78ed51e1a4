# 📋 DOCUMENTACIÓN COMPLETA - APIs SISTEMA DE INVENTARIO Y VENTAS UVAMAYU

## 🎯 Resumen del Sistema

Sistema completo de control de inventario, producción, ventas y presupuesto para UVAMAYU que maneja:
- **7 productos líquidos**: <PERSON>sco (Acholado, Quebranta, Italia) + <PERSON><PERSON> (Naranja, Mistela, Ciruela, Perfecto Amor)
- **5 tipos de botellas**: Transparente 750ml, Verde 750ml, Transparente 4000ml, Transparente 50ml, Porrón
- **3 tipos de tapones**: Corcho sintético, Tapa rosca negra, Tapa porrón
- **4 tipos de cápsulas**: 750ml (Negro/Dorado), 4000ml (Negro/Dorado)
- **28 tipos de etiquetas**: 7 productos × 4 tipos (Porrón, 750ml adelante/atrás, 50ml regalo)
- **Sistema de ventas**: Con opción de regalo y control de stock
- **Control de presupuesto**: Inversion<PERSON>, gastos y saldo disponible
- **3 inversores**: <PERSON>, <PERSON>p<PERSON>, Empresa UVAMAYU

---

## 🔗 ENDPOINTS PRINCIPALES

### Base URL: `https://tudominio.com/api/`

## 1. 🧪 LÍQUIDOS - `/api/liquidos.php`

### GET - Obtener todos los líquidos
```http
GET /api/liquidos.php
```

**Respuesta:**
```json
{
  "success": true,
  "message": "Líquidos obtenidos exitosamente",
  "data": [
    {
      "id": 1,
      "nombre": "Pisco Quebranta",
      "categoria": "pisco",
      "stock_ml": 5000.00,
      "stock_minimo_ml": 1000.00,
      "stock_status": "normal",
      "costo_por_ml": 0.05
    }
  ]
}
```

### GET - Obtener líquido específico
```http
GET /api/liquidos.php/1
```

### POST - Crear nuevo líquido
```http
POST /api/liquidos.php
Content-Type: application/json

{
  "nombre": "Pisco Acholado Premium",
  "categoria": "pisco",
  "descripcion": "Pisco Acholado artesanal premium",
  "stock_ml": 2000,
  "stock_minimo_ml": 500,
  "costo_por_ml": 0.08
}
```

### PUT - Actualizar líquido
```http
PUT /api/liquidos.php/1
Content-Type: application/json

{
  "stock_ml": 3000,
  "costo_por_ml": 0.06
}
```

### DELETE - Eliminar líquido (soft delete)
```http
DELETE /api/liquidos.php/1
```

---

## 2. 🍾 BOTELLAS - `/api/botellas.php`

### GET - Obtener todas las botellas
```http
GET /api/botellas.php
```

### POST - Crear nueva botella
```http
POST /api/botellas.php
Content-Type: application/json

{
  "nombre": "Botella Transparente 750ml Premium",
  "capacidad_ml": 750,
  "color": "transparente",
  "tipo_uso": "pisco",
  "stock_cantidad": 100,
  "stock_minimo": 50,
  "costo_unitario": 2.50
}
```

**Valores válidos:**
- `color`: "transparente", "verde"
- `tipo_uso`: "pisco", "vino", "ambos"

---

## 3. 🔌 TAPONES - `/api/tapones.php`

### POST - Crear nuevo tapón
```http
POST /api/tapones.php
Content-Type: application/json

{
  "nombre": "Corcho Sintético Premium",
  "tipo": "corcho_sintetico",
  "compatible_con": "Botellas 750ml y 4000ml",
  "stock_cantidad": 500,
  "stock_minimo": 100,
  "costo_unitario": 0.30
}
```

**Valores válidos:**
- `tipo`: "corcho_sintetico", "tapa_rosca_negra", "tapa_porron"

---

## 4. 🎯 CÁPSULAS - `/api/capsulas.php`

### POST - Crear nueva cápsula
```http
POST /api/capsulas.php
Content-Type: application/json

{
  "nombre": "Cápsula 750ml Dorado Premium",
  "capacidad_botella": 750,
  "color": "dorado",
  "tipo_producto": "vino",
  "stock_cantidad": 200,
  "stock_minimo": 50,
  "costo_unitario": 0.15
}
```

**Valores válidos:**
- `capacidad_botella`: 750, 4000
- `color`: "negro", "dorado"
- `tipo_producto`: "pisco", "vino"

---

## 5. 🏷️ ETIQUETAS - `/api/etiquetas.php`

### GET - Obtener etiquetas con filtros
```http
GET /api/etiquetas.php?categoria_producto=pisco&producto_liquido=quebranta
```

### POST - Crear nueva etiqueta
```http
POST /api/etiquetas.php
Content-Type: application/json

{
  "nombre": "Etiqueta 750ml Adelante Pisco Quebranta Premium",
  "tipo_etiqueta": "botella_750ml_adelante",
  "producto_liquido": "quebranta",
  "categoria_producto": "pisco",
  "stock_cantidad": 300,
  "stock_minimo": 100,
  "costo_unitario": 0.25
}
```

**Valores válidos:**
- `tipo_etiqueta`: "porron", "botella_750ml_adelante", "botella_750ml_atras", "botella_regalo_50ml"
- `producto_liquido`: "acholado", "quebranta", "italia", "naranja", "mistela", "ciruela", "perfecto_amor"
- `categoria_producto`: "pisco", "vino"

---

## 6. 🏭 EMBOTELLADO - `/api/embotellado.php`

### GET - Obtener configuraciones de embotellado
```http
GET /api/embotellado.php/configuraciones
```

### GET - Validar disponibilidad para embotellado
```http
GET /api/embotellado.php/validar?liquido_id=1&configuracion_id=1&cantidad_botellas=50
```

**Respuesta:**
```json
{
  "success": true,
  "data": {
    "puede_embotellar": true,
    "componentes_necesarios": {
      "liquido_ml": 37500,
      "botellas": 50,
      "tapones": 50,
      "capsulas": 50,
      "etiquetas": [
        {
          "id": 8,
          "nombre": "Etiqueta 750ml Adelante Pisco Quebranta",
          "cantidad_necesaria": 50,
          "stock_disponible": 200
        }
      ]
    },
    "faltantes": []
  }
}
```

### POST - Simular embotellado
```http
POST /api/embotellado.php/simular
Content-Type: application/json

{
  "liquido_id": 1,
  "configuracion_id": 1,
  "cantidad_botellas": 50
}
```

### POST - Procesar embotellado real
```http
POST /api/embotellado.php/procesar
Content-Type: application/json

{
  "liquido_id": 1,
  "configuracion_id": 1,
  "cantidad_botellas": 50,
  "fecha_embotellado": "2025-01-23",
  "usuario": "Juan Pérez",
  "notas": "Lote de prueba para mercado local"
}
```

**Respuesta exitosa:**
```json
{
  "success": true,
  "message": "Embotellado procesado exitosamente",
  "data": {
    "lote_id": 15,
    "codigo_lote": "LOTE-20250123-001",
    "componentes_descontados": {
      "liquido_ml": 37500,
      "botellas": 50,
      "tapones": 50,
      "capsulas": 50,
      "etiquetas": [...]
    }
  }
}
```

### GET - Obtener lotes de embotellado
```http
GET /api/embotellado.php/lotes
```

---

## 7. 📊 REPORTES - `/api/reportes.php`

### GET - Stock bajo
```http
GET /api/reportes.php/stock-bajo
```

### GET - Resumen de inventario
```http
GET /api/reportes.php/resumen-inventario
```

### GET - Movimientos de inventario
```http
GET /api/reportes.php/movimientos?limite=50&tipo_componente=liquido&fecha_desde=2025-01-01
```

### GET - Alertas activas
```http
GET /api/reportes.php/alertas
```

### GET - Reporte de producción
```http
GET /api/reportes.php/produccion?fecha_desde=2025-01-01&fecha_hasta=2025-01-31
```

### GET - Reporte de costos
```http
GET /api/reportes.php/costos
```

---

## 8. ✅ VALIDACIONES - `/api/validaciones.php`

### GET - Validar disponibilidad para embotellado
```http
GET /api/validaciones.php/disponibilidad-embotellado?liquido_id=1&configuracion_id=1&cantidad_botellas=100
```

### GET - Validar stock mínimo
```http
GET /api/validaciones.php/stock-minimo
```

### GET - Validar consistencia de datos
```http
GET /api/validaciones.php/consistencia
```

### POST - Simular embotellado con costos
```http
POST /api/validaciones.php/simular-embotellado
Content-Type: application/json

{
  "liquido_id": 1,
  "configuracion_id": 1,
  "cantidad_botellas": 100
}
```

### POST - Verificar componentes específicos
```http
POST /api/validaciones.php/verificar-componentes
Content-Type: application/json

{
  "componentes": [
    {
      "tipo": "liquido",
      "id": 1,
      "cantidad_requerida": 5000
    },
    {
      "tipo": "botella",
      "id": 1,
      "cantidad_requerida": 50
    }
  ]
}
```

---

## 🔧 CONFIGURACIÓN

### 1. Base de datos
```bash
# Importar estructura
mysql -u usuario -p uvamayu < inventario_db.sql
```

### 2. Configuración PHP
Asegurar que `config.php` tenga la configuración correcta de base de datos.

### 3. Headers CORS
Todas las APIs incluyen headers CORS para permitir consumo desde aplicaciones externas.

---

## 📝 NOTAS IMPORTANTES

### Reglas de Embotellado:
1. **Pisco 750ml**: Botella transparente + Corcho sintético + Cápsula negra + 2 etiquetas (adelante/atrás)
2. **Vino 750ml**: Botella verde + Corcho sintético + Cápsula dorada + 2 etiquetas (adelante/atrás)
3. **Regalo 50ml**: Botella transparente + Tapa rosca negra + 1 etiqueta
4. **Porrón**: Botella porrón + Tapa porrón + 1 etiqueta

### Validaciones Automáticas:
- Stock suficiente antes de embotellado
- Consistencia de datos
- Alertas de stock bajo
- Registro de todos los movimientos

### Seguridad:
- Validación de entrada en todas las APIs
- Sanitización de datos
- Manejo de errores robusto
- Logs de auditoría completos

---

## 🚀 EJEMPLOS DE USO

### Flujo completo de embotellado:

1. **Consultar configuraciones disponibles**
2. **Validar disponibilidad de componentes**
3. **Simular embotellado (opcional)**
4. **Procesar embotellado real**
5. **Verificar lote creado**

### Gestión de inventario:

1. **Consultar stock bajo**
2. **Actualizar inventarios**
3. **Generar reportes**
4. **Revisar alertas**

---

## 9. 💰 VENTAS - `/api/ventas.php`

### GET - Obtener stock disponible
```http
GET /api/ventas.php/stock-disponible
```

**Respuesta:**
```json
{
  "success": true,
  "data": [
    {
      "producto_nombre": "Pisco Quebranta",
      "capacidad_ml": 750,
      "precio_actual": 23.00,
      "cantidad_total": 48,
      "lotes": [
        {
          "lote_id": 1,
          "codigo_lote": "LOTE-20250123-001",
          "cantidad_disponible": 48,
          "fecha_embotellado": "2025-01-23"
        }
      ]
    }
  ]
}
```

### GET - Obtener precios de productos
```http
GET /api/ventas.php/precios
```

### POST - Procesar venta
```http
POST /api/ventas.php/procesar
Content-Type: application/json

{
  "productos": [
    {
      "lote_id": 1,
      "producto_nombre": "Pisco Quebranta",
      "capacidad_ml": 750,
      "cantidad": 2,
      "precio_unitario": 23.00,
      "es_regalo": false
    }
  ],
  "cliente_nombre": "Juan Pérez",
  "cliente_telefono": "+51999999999",
  "fecha_venta": "2025-01-23",
  "es_regalo": false,
  "motivo_regalo": "",
  "metodo_pago": "efectivo",
  "vendedor": "Sebastian",
  "notas": "Venta local"
}
```

**Respuesta exitosa:**
```json
{
  "success": true,
  "message": "Venta procesada exitosamente",
  "data": {
    "venta_id": 1,
    "codigo_venta": "VENTA-20250123-001",
    "total": 46.00,
    "es_regalo": false
  }
}
```

### POST - Validar venta antes de procesar
```http
POST /api/ventas.php/validar
Content-Type: application/json

{
  "productos": [
    {
      "lote_id": 1,
      "cantidad": 100
    }
  ]
}
```

### GET - Historial de ventas
```http
GET /api/ventas.php/historial?fecha_desde=2025-01-01&solo_regalos=false&limite=50
```

### PUT - Actualizar venta
```http
PUT /api/ventas.php/1
Content-Type: application/json

{
  "estado": "entregado",
  "notas": "Entregado al cliente"
}
```

### DELETE - Cancelar venta (restaura stock)
```http
DELETE /api/ventas.php/1
```

---

## 10. 💼 PRESUPUESTO - `/api/presupuesto.php`

### GET - Dashboard financiero completo
```http
GET /api/presupuesto.php/dashboard
```

**Respuesta:**
```json
{
  "success": true,
  "data": {
    "total_inversiones": 5000.00,
    "gastos_etiquetas_mes": 60.00,
    "gastos_botellas_mes": 38.00,
    "gastos_producto_mes": 3000.00,
    "ventas_mes": 460.00,
    "regalos_mes": 0.00,
    "saldo_disponible": 1902.00,
    "utilidad_mes": -2638.00,
    "desglose_inversores": [
      {
        "nombre": "Sebastian",
        "total_gastado_mes": 1500.00,
        "total_invertido": 2000.00
      },
      {
        "nombre": "Papa",
        "total_gastado_mes": 800.00,
        "total_invertido": 1500.00
      },
      {
        "nombre": "Empresa UVAMAYU",
        "total_gastado_mes": 798.00,
        "total_invertido": 1500.00
      }
    ]
  }
}
```

### GET - Obtener inversores
```http
GET /api/presupuesto.php/inversores
```

### POST - Registrar nueva inversión
```http
POST /api/presupuesto.php/inversion
Content-Type: application/json

{
  "inversor_id": 1,
  "monto": 1000.00,
  "concepto": "Capital para compra de botellas",
  "fecha_inversion": "2025-01-23",
  "tipo_inversion": "reposicion_stock",
  "notas": "Inversión para aumentar producción"
}
```

### POST - Registrar nuevo gasto
```http
POST /api/presupuesto.php/gasto
Content-Type: application/json

{
  "categoria": "etiquetas",
  "subcategoria": "Etiquetas Pisco Quebranta",
  "monto": 60.00,
  "cantidad": 200,
  "precio_unitario": 0.30,
  "proveedor": "Imprenta San Martín",
  "fecha_gasto": "2025-01-23",
  "descripcion": "Compra de etiquetas para Pisco Quebranta 750ml",
  "inversor_id": 1,
  "comprobante": "F001-123",
  "actualizar_stock": true,
  "etiqueta_id": 8
}
```

**Categorías de gastos válidas:**
- `etiquetas` - Gastos en etiquetas
- `botellas_tapones_capsulas` - Gastos en envases y cierres
- `producto_liquido` - Gastos en pisco y vino
- `otros` - Otros gastos operativos

### GET - Obtener gastos con filtros
```http
GET /api/presupuesto.php/gastos?categoria=etiquetas&fecha_desde=2025-01-01&inversor_id=1
```

### GET - Obtener inversiones
```http
GET /api/presupuesto.php/inversiones?inversor_id=1&fecha_desde=2025-01-01
```

### GET - Resumen mensual
```http
GET /api/presupuesto.php/resumen-mensual?año=2025&mes=1
```

### GET - Saldo disponible actual
```http
GET /api/presupuesto.php/saldo-disponible
```

### POST - Crear nuevo inversor
```http
POST /api/presupuesto.php/inversor
Content-Type: application/json

{
  "nombre": "Nuevo Inversor",
  "tipo": "persona"
}
```

**Tipos válidos:** `persona`, `empresa`

---

## 📊 TABLA DE PRECIOS IMPLEMENTADA

| Producto | Tipo | 750ml | Caja x12 | 4000ml | 50ml |
|----------|------|-------|----------|--------|------|
| Pisco | Acholado | S/ 23 | S/ 250 | S/ 100 | S/ 2.5 |
| Pisco | Italia | S/ 30 | S/ 320 | S/ 140 | S/ 3.0 |
| Pisco | Quebranta | S/ 23 | S/ 245 | S/ 100 | S/ 2.5 |
| Vino | Perfecto Amor | S/ 22 | S/ 230 | S/ 100 | S/ 2.5 |
| Vino | Mistela | S/ 34 | S/ 355 | S/ 165 | S/ 3.5 |
| Vino | Naranja | S/ 34 | S/ 355 | S/ 165 | S/ 3.5 |
| Vino | Ciruela | S/ 34 | S/ 355 | S/ 152 | S/ 3.5 |

¡El sistema está listo para ser consumido desde cualquier aplicación externa! 🎉
