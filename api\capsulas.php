<?php
/**
 * API para gestión de cápsulas
 */

require_once 'config/Database.php';
require_once 'config/ApiResponse.php';

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $path_info = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
    $id = $path_info ? (int)ltrim($path_info, '/') : null;
    
    switch ($method) {
        case 'GET':
            if ($id) {
                getCapsula($db, $id);
            } else {
                getCapsulas($db);
            }
            break;
        case 'POST':
            createCapsula($db);
            break;
        case 'PUT':
            if (!$id) ApiResponse::error("ID requerido", 400);
            updateCapsula($db, $id);
            break;
        case 'DELETE':
            if (!$id) ApiResponse::error("ID requerido", 400);
            deleteCapsula($db, $id);
            break;
        default:
            ApiResponse::error("Método no permitido", 405);
    }
    
} catch (Exception $e) {
    error_log("Error en API cápsulas: " . $e->getMessage());
    ApiResponse::serverError();
}

function getCapsulas($db) {
    try {
        $query = "SELECT * FROM capsulas ORDER BY capacidad_botella, tipo_producto, color";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $capsulas = $stmt->fetchAll();
        
        foreach ($capsulas as &$capsula) {
            $capsula['stock_status'] = $capsula['stock_cantidad'] <= $capsula['stock_minimo'] ? 'bajo' : 'normal';
            $capsula['stock_cantidad'] = (int)$capsula['stock_cantidad'];
            $capsula['stock_minimo'] = (int)$capsula['stock_minimo'];
            $capsula['capacidad_botella'] = (int)$capsula['capacidad_botella'];
            $capsula['costo_unitario'] = (float)$capsula['costo_unitario'];
        }
        
        ApiResponse::success($capsulas);
    } catch (Exception $e) {
        ApiResponse::serverError();
    }
}

function getCapsula($db, $id) {
    try {
        $query = "SELECT * FROM capsulas WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        
        $capsula = $stmt->fetch();
        if (!$capsula) ApiResponse::notFound();
        
        $capsula['stock_cantidad'] = (int)$capsula['stock_cantidad'];
        $capsula['stock_minimo'] = (int)$capsula['stock_minimo'];
        $capsula['capacidad_botella'] = (int)$capsula['capacidad_botella'];
        $capsula['costo_unitario'] = (float)$capsula['costo_unitario'];
        $capsula['stock_status'] = $capsula['stock_cantidad'] <= $capsula['stock_minimo'] ? 'bajo' : 'normal';
        
        ApiResponse::success($capsula);
    } catch (Exception $e) {
        ApiResponse::serverError();
    }
}

function createCapsula($db) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        ApiResponse::validateRequired($data, ['nombre', 'capacidad_botella', 'color', 'tipo_producto']);
        
        if (!in_array($data['color'], ['negro', 'dorado'])) {
            ApiResponse::validationError(['color' => 'Color debe ser negro o dorado']);
        }
        
        if (!in_array($data['tipo_producto'], ['pisco', 'vino'])) {
            ApiResponse::validationError(['tipo_producto' => 'Tipo debe ser pisco o vino']);
        }
        
        if (!in_array($data['capacidad_botella'], [750, 4000])) {
            ApiResponse::validationError(['capacidad_botella' => 'Capacidad debe ser 750 o 4000']);
        }
        
        $query = "INSERT INTO capsulas (nombre, capacidad_botella, color, tipo_producto, stock_cantidad, stock_minimo, costo_unitario, activo) 
                  VALUES (:nombre, :capacidad_botella, :color, :tipo_producto, :stock_cantidad, :stock_minimo, :costo_unitario, :activo)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':nombre', $data['nombre']);
        $stmt->bindParam(':capacidad_botella', $data['capacidad_botella'], PDO::PARAM_INT);
        $stmt->bindParam(':color', $data['color']);
        $stmt->bindParam(':tipo_producto', $data['tipo_producto']);
        $stmt->bindParam(':stock_cantidad', $data['stock_cantidad'] ?? 0, PDO::PARAM_INT);
        $stmt->bindParam(':stock_minimo', $data['stock_minimo'] ?? 100, PDO::PARAM_INT);
        $stmt->bindParam(':costo_unitario', $data['costo_unitario'] ?? 0);
        $stmt->bindParam(':activo', $data['activo'] ?? true, PDO::PARAM_BOOL);
        
        if ($stmt->execute()) {
            $newId = $db->lastInsertId();
            if (($data['stock_cantidad'] ?? 0) > 0) {
                registrarMovimiento($db, 'capsula', $newId, 'entrada', 0, $data['stock_cantidad'], $data['stock_cantidad'], 'Creación inicial');
            }
            ApiResponse::success(['id' => $newId], "Cápsula creada exitosamente", 201);
        } else {
            ApiResponse::serverError();
        }
    } catch (Exception $e) {
        ApiResponse::serverError();
    }
}

function updateCapsula($db, $id) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        $checkQuery = "SELECT stock_cantidad FROM capsulas WHERE id = :id";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->bindParam(':id', $id, PDO::PARAM_INT);
        $checkStmt->execute();
        $current = $checkStmt->fetch();
        
        if (!$current) ApiResponse::notFound();
        
        $fields = [];
        $params = [':id' => $id];
        
        $allowedFields = ['nombre', 'capacidad_botella', 'color', 'tipo_producto', 'stock_cantidad', 'stock_minimo', 'costo_unitario', 'activo'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = :$field";
                $params[":$field"] = $data[$field];
            }
        }
        
        if (empty($fields)) ApiResponse::error("No hay campos para actualizar", 400);
        
        $query = "UPDATE capsulas SET " . implode(', ', $fields) . " WHERE id = :id";
        $stmt = $db->prepare($query);
        
        if ($stmt->execute($params)) {
            if (isset($data['stock_cantidad']) && $data['stock_cantidad'] != $current['stock_cantidad']) {
                $diferencia = $data['stock_cantidad'] - $current['stock_cantidad'];
                $tipoMovimiento = $diferencia > 0 ? 'entrada' : 'salida';
                registrarMovimiento($db, 'capsula', $id, $tipoMovimiento, $current['stock_cantidad'], abs($diferencia), $data['stock_cantidad'], 'Ajuste manual');
            }
            ApiResponse::success(null, "Cápsula actualizada exitosamente");
        } else {
            ApiResponse::serverError();
        }
    } catch (Exception $e) {
        ApiResponse::serverError();
    }
}

function deleteCapsula($db, $id) {
    try {
        $query = "UPDATE capsulas SET activo = FALSE WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        
        if ($stmt->execute() && $stmt->rowCount() > 0) {
            ApiResponse::success(null, "Cápsula eliminada exitosamente");
        } else {
            ApiResponse::notFound();
        }
    } catch (Exception $e) {
        ApiResponse::serverError();
    }
}

function registrarMovimiento($db, $tipoComponente, $componenteId, $tipoMovimiento, $cantidadAnterior, $cantidadMovimiento, $cantidadNueva, $motivo) {
    try {
        $query = "INSERT INTO movimientos_inventario (tipo_componente, componente_id, tipo_movimiento, cantidad_anterior, cantidad_movimiento, cantidad_nueva, motivo) 
                  VALUES (:tipo_componente, :componente_id, :tipo_movimiento, :cantidad_anterior, :cantidad_movimiento, :cantidad_nueva, :motivo)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':tipo_componente', $tipoComponente);
        $stmt->bindParam(':componente_id', $componenteId);
        $stmt->bindParam(':tipo_movimiento', $tipoMovimiento);
        $stmt->bindParam(':cantidad_anterior', $cantidadAnterior);
        $stmt->bindParam(':cantidad_movimiento', $cantidadMovimiento);
        $stmt->bindParam(':cantidad_nueva', $cantidadNueva);
        $stmt->bindParam(':motivo', $motivo);
        
        $stmt->execute();
    } catch (Exception $e) {
        error_log("Error registrando movimiento: " . $e->getMessage());
    }
}
?>
