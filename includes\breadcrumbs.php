<?php
// Breadcrumbs visuales para UVAMAYU

function renderBreadcrumbs($breadcrumbs) {
    if (empty($breadcrumbs) || count($breadcrumbs) <= 1) {
        return;
    }
    
    echo '<nav class="breadcrumbs" aria-label="Navegación de migas de pan">' . "\n";
    echo '    <div class="container">' . "\n";
    echo '        <ol class="breadcrumb-list">' . "\n";
    
    foreach ($breadcrumbs as $index => $crumb) {
        $isLast = ($index === count($breadcrumbs) - 1);
        
        echo '            <li class="breadcrumb-item' . ($isLast ? ' active' : '') . '">' . "\n";
        
        if (!$isLast) {
            echo '                <a href="' . htmlspecialchars($crumb['url']) . '" class="breadcrumb-link">';
            echo htmlspecialchars($crumb['name']);
            echo '</a>' . "\n";
            echo '                <span class="breadcrumb-separator">🍇</span>' . "\n";
        } else {
            echo '                <span class="breadcrumb-current" aria-current="page">';
            echo htmlspecialchars($crumb['name']);
            echo '</span>' . "\n";
        }
        
        echo '            </li>' . "\n";
    }
    
    echo '        </ol>' . "\n";
    echo '    </div>' . "\n";
    echo '</nav>' . "\n";
    
    // CSS para breadcrumbs
    echo '<style>' . "\n";
    echo '.breadcrumbs {' . "\n";
    echo '    background: var(--gris-oscuro);' . "\n";
    echo '    padding: 1rem 0;' . "\n";
    echo '    margin-top: 80px;' . "\n";
    echo '}' . "\n";
    echo '' . "\n";
    echo '.breadcrumb-list {' . "\n";
    echo '    display: flex;' . "\n";
    echo '    flex-wrap: wrap;' . "\n";
    echo '    align-items: center;' . "\n";
    echo '    list-style: none;' . "\n";
    echo '    margin: 0;' . "\n";
    echo '    padding: 0;' . "\n";
    echo '    gap: 0.5rem;' . "\n";
    echo '}' . "\n";
    echo '' . "\n";
    echo '.breadcrumb-item {' . "\n";
    echo '    display: flex;' . "\n";
    echo '    align-items: center;' . "\n";
    echo '    gap: 0.5rem;' . "\n";
    echo '}' . "\n";
    echo '' . "\n";
    echo '.breadcrumb-link {' . "\n";
    echo '    color: var(--dorado);' . "\n";
    echo '    text-decoration: none;' . "\n";
    echo '    transition: color 0.3s ease;' . "\n";
    echo '    font-size: 0.9rem;' . "\n";
    echo '}' . "\n";
    echo '' . "\n";
    echo '.breadcrumb-link:hover {' . "\n";
    echo '    color: var(--guinda);' . "\n";
    echo '    text-decoration: underline;' . "\n";
    echo '}' . "\n";
    echo '' . "\n";
    echo '.breadcrumb-separator {' . "\n";
    echo '    color: var(--verde);' . "\n";
    echo '    font-size: 0.8rem;' . "\n";
    echo '}' . "\n";
    echo '' . "\n";
    echo '.breadcrumb-current {' . "\n";
    echo '    color: #ccc;' . "\n";
    echo '    font-size: 0.9rem;' . "\n";
    echo '}' . "\n";
    echo '' . "\n";
    echo '@media (max-width: 768px) {' . "\n";
    echo '    .breadcrumbs {' . "\n";
    echo '        padding: 0.5rem 0;' . "\n";
    echo '    }' . "\n";
    echo '    ' . "\n";
    echo '    .breadcrumb-list {' . "\n";
    echo '        font-size: 0.8rem;' . "\n";
    echo '    }' . "\n";
    echo '}' . "\n";
    echo '</style>' . "\n";
}
?>
