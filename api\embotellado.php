<?php
/**
 * API para el proceso de embotellado
 * Controla el embotellado y descuenta automáticamente los componentes
 */

require_once 'config/Database.php';
require_once 'config/ApiResponse.php';

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $path_info = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
    
    // Extraer acción de la URL
    $action = '';
    if ($path_info) {
        $parts = explode('/', trim($path_info, '/'));
        $action = $parts[0] ?? '';
    }
    
    switch ($method) {
        case 'GET':
            if ($action === 'configuraciones') {
                getConfiguraciones($db);
            } elseif ($action === 'validar') {
                validarDisponibilidad($db);
            } elseif ($action === 'lotes') {
                getLotes($db);
            } else {
                ApiResponse::error("Acción no válida", 400);
            }
            break;
            
        case 'POST':
            if ($action === 'procesar') {
                procesarEmbotellado($db);
            } elseif ($action === 'simular') {
                simularEmbotellado($db);
            } else {
                ApiResponse::error("Acción no válida", 400);
            }
            break;
            
        default:
            ApiResponse::error("Método no permitido", 405);
    }
    
} catch (Exception $e) {
    error_log("Error en API embotellado: " . $e->getMessage());
    ApiResponse::serverError("Error interno del servidor");
}

/**
 * Obtener configuraciones de embotellado disponibles
 */
function getConfiguraciones($db) {
    try {
        $query = "SELECT ce.*, 
                         b.nombre as botella_nombre, b.capacidad_ml, b.color,
                         t.nombre as tapon_nombre, t.tipo as tapon_tipo,
                         c.nombre as capsula_nombre, c.color as capsula_color
                  FROM configuracion_embotellado ce
                  JOIN botellas b ON ce.botella_id = b.id
                  JOIN tapones t ON ce.tapon_id = t.id
                  LEFT JOIN capsulas c ON ce.capsula_id = c.id
                  WHERE ce.activo = TRUE
                  ORDER BY ce.categoria_producto, ce.capacidad_ml";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $configuraciones = $stmt->fetchAll();
        
        ApiResponse::success($configuraciones, "Configuraciones obtenidas exitosamente");
        
    } catch (Exception $e) {
        error_log("Error obteniendo configuraciones: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Validar disponibilidad de componentes para embotellado
 */
function validarDisponibilidad($db) {
    try {
        $liquidoId = $_GET['liquido_id'] ?? null;
        $configuracionId = $_GET['configuracion_id'] ?? null;
        $cantidadBotellas = (int)($_GET['cantidad_botellas'] ?? 0);
        
        if (!$liquidoId || !$configuracionId || $cantidadBotellas <= 0) {
            ApiResponse::error("Parámetros requeridos: liquido_id, configuracion_id, cantidad_botellas", 400);
        }
        
        $resultado = validarComponentes($db, $liquidoId, $configuracionId, $cantidadBotellas);
        
        ApiResponse::success($resultado, "Validación completada");
        
    } catch (Exception $e) {
        error_log("Error validando disponibilidad: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Simular embotellado (sin afectar inventario)
 */
function simularEmbotellado($db) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        ApiResponse::validateRequired($data, ['liquido_id', 'configuracion_id', 'cantidad_botellas']);
        
        $resultado = validarComponentes($db, $data['liquido_id'], $data['configuracion_id'], $data['cantidad_botellas']);
        
        if (!$resultado['puede_embotellar']) {
            ApiResponse::success($resultado, "Simulación completada - No se puede embotellar");
        } else {
            ApiResponse::success($resultado, "Simulación completada - Embotellado posible");
        }
        
    } catch (Exception $e) {
        error_log("Error simulando embotellado: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Procesar embotellado real
 */
function procesarEmbotellado($db) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        ApiResponse::validateRequired($data, ['liquido_id', 'configuracion_id', 'cantidad_botellas', 'fecha_embotellado']);
        
        $liquidoId = $data['liquido_id'];
        $configuracionId = $data['configuracion_id'];
        $cantidadBotellas = (int)$data['cantidad_botellas'];
        $fechaEmbotellado = $data['fecha_embotellado'];
        $usuario = $data['usuario'] ?? 'Sistema';
        $notas = $data['notas'] ?? '';
        
        // Validar disponibilidad
        $validacion = validarComponentes($db, $liquidoId, $configuracionId, $cantidadBotellas);
        
        if (!$validacion['puede_embotellar']) {
            ApiResponse::error("No se puede procesar el embotellado", 400, $validacion['faltantes']);
        }
        
        // Iniciar transacción
        $db->beginTransaction();
        
        try {
            // Crear lote de embotellado
            $codigoLote = generarCodigoLote($db, $fechaEmbotellado);
            $mlPorBotella = $validacion['configuracion']['capacidad_ml'];
            $mlTotal = $mlPorBotella * $cantidadBotellas;
            
            $queryLote = "INSERT INTO lotes_embotellado (codigo_lote, liquido_id, configuracion_id, cantidad_botellas, ml_por_botella, ml_total_usado, fecha_embotellado, usuario_embotellado, notas, estado) 
                          VALUES (:codigo_lote, :liquido_id, :configuracion_id, :cantidad_botellas, :ml_por_botella, :ml_total, :fecha_embotellado, :usuario, :notas, 'completado')";
            
            $stmtLote = $db->prepare($queryLote);
            $stmtLote->bindParam(':codigo_lote', $codigoLote);
            $stmtLote->bindParam(':liquido_id', $liquidoId);
            $stmtLote->bindParam(':configuracion_id', $configuracionId);
            $stmtLote->bindParam(':cantidad_botellas', $cantidadBotellas);
            $stmtLote->bindParam(':ml_por_botella', $mlPorBotella);
            $stmtLote->bindParam(':ml_total', $mlTotal);
            $stmtLote->bindParam(':fecha_embotellado', $fechaEmbotellado);
            $stmtLote->bindParam(':usuario', $usuario);
            $stmtLote->bindParam(':notas', $notas);
            
            $stmtLote->execute();
            $loteId = $db->lastInsertId();
            
            // Descontar componentes
            descontarComponentes($db, $validacion, $cantidadBotellas, $loteId);
            
            $db->commit();
            
            ApiResponse::success([
                'lote_id' => $loteId,
                'codigo_lote' => $codigoLote,
                'componentes_descontados' => $validacion['componentes_necesarios']
            ], "Embotellado procesado exitosamente", 201);
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
        
    } catch (Exception $e) {
        error_log("Error procesando embotellado: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Validar que hay suficientes componentes
 */
function validarComponentes($db, $liquidoId, $configuracionId, $cantidadBotellas) {
    // Obtener configuración
    $queryConfig = "SELECT ce.*, 
                           b.nombre as botella_nombre, b.stock_cantidad as botella_stock,
                           t.nombre as tapon_nombre, t.stock_cantidad as tapon_stock,
                           c.nombre as capsula_nombre, c.stock_cantidad as capsula_stock
                    FROM configuracion_embotellado ce
                    JOIN botellas b ON ce.botella_id = b.id
                    JOIN tapones t ON ce.tapon_id = t.id
                    LEFT JOIN capsulas c ON ce.capsula_id = c.id
                    WHERE ce.id = :configuracion_id";
    
    $stmtConfig = $db->prepare($queryConfig);
    $stmtConfig->bindParam(':configuracion_id', $configuracionId);
    $stmtConfig->execute();
    $config = $stmtConfig->fetch();
    
    if (!$config) {
        throw new Exception("Configuración no encontrada");
    }
    
    // Obtener líquido
    $queryLiquido = "SELECT * FROM liquidos WHERE id = :liquido_id";
    $stmtLiquido = $db->prepare($queryLiquido);
    $stmtLiquido->bindParam(':liquido_id', $liquidoId);
    $stmtLiquido->execute();
    $liquido = $stmtLiquido->fetch();
    
    if (!$liquido) {
        throw new Exception("Líquido no encontrado");
    }
    
    // Calcular necesidades
    $mlNecesarios = $config['capacidad_ml'] * $cantidadBotellas;
    $botellasNecesarias = $cantidadBotellas;
    $taponesNecesarios = $cantidadBotellas;
    $capsulasNecesarias = $config['capsula_id'] ? $cantidadBotellas : 0;
    
    // Obtener etiquetas necesarias
    $etiquetasNecesarias = obtenerEtiquetasNecesarias($db, $liquido['nombre'], $config, $cantidadBotellas);
    
    $faltantes = [];
    $puedeEmbotellar = true;
    
    // Validar líquido
    if ($liquido['stock_ml'] < $mlNecesarios) {
        $faltantes[] = [
            'componente' => 'liquido',
            'nombre' => $liquido['nombre'],
            'necesario' => $mlNecesarios,
            'disponible' => $liquido['stock_ml'],
            'faltante' => $mlNecesarios - $liquido['stock_ml']
        ];
        $puedeEmbotellar = false;
    }
    
    // Validar botella
    if ($config['botella_stock'] < $botellasNecesarias) {
        $faltantes[] = [
            'componente' => 'botella',
            'nombre' => $config['botella_nombre'],
            'necesario' => $botellasNecesarias,
            'disponible' => $config['botella_stock'],
            'faltante' => $botellasNecesarias - $config['botella_stock']
        ];
        $puedeEmbotellar = false;
    }
    
    // Validar tapón
    if ($config['tapon_stock'] < $taponesNecesarios) {
        $faltantes[] = [
            'componente' => 'tapon',
            'nombre' => $config['tapon_nombre'],
            'necesario' => $taponesNecesarios,
            'disponible' => $config['tapon_stock'],
            'faltante' => $taponesNecesarios - $config['tapon_stock']
        ];
        $puedeEmbotellar = false;
    }
    
    // Validar cápsula (si aplica)
    if ($capsulasNecesarias > 0 && $config['capsula_stock'] < $capsulasNecesarias) {
        $faltantes[] = [
            'componente' => 'capsula',
            'nombre' => $config['capsula_nombre'],
            'necesario' => $capsulasNecesarias,
            'disponible' => $config['capsula_stock'],
            'faltante' => $capsulasNecesarias - $config['capsula_stock']
        ];
        $puedeEmbotellar = false;
    }
    
    // Validar etiquetas
    foreach ($etiquetasNecesarias as $etiqueta) {
        if ($etiqueta['stock_disponible'] < $etiqueta['cantidad_necesaria']) {
            $faltantes[] = [
                'componente' => 'etiqueta',
                'nombre' => $etiqueta['nombre'],
                'necesario' => $etiqueta['cantidad_necesaria'],
                'disponible' => $etiqueta['stock_disponible'],
                'faltante' => $etiqueta['cantidad_necesaria'] - $etiqueta['stock_disponible']
            ];
            $puedeEmbotellar = false;
        }
    }
    
    return [
        'puede_embotellar' => $puedeEmbotellar,
        'configuracion' => $config,
        'liquido' => $liquido,
        'componentes_necesarios' => [
            'liquido_ml' => $mlNecesarios,
            'botellas' => $botellasNecesarias,
            'tapones' => $taponesNecesarios,
            'capsulas' => $capsulasNecesarias,
            'etiquetas' => $etiquetasNecesarias
        ],
        'faltantes' => $faltantes
    ];
}

/**
 * Obtener etiquetas necesarias según el producto y configuración
 */
function obtenerEtiquetasNecesarias($db, $nombreLiquido, $config, $cantidadBotellas) {
    // Mapear nombre de líquido a producto_liquido
    $mapeoProductos = [
        'Pisco Acholado' => 'acholado',
        'Pisco Quebranta' => 'quebranta',
        'Pisco Italia' => 'italia',
        'Vino Naranja' => 'naranja',
        'Vino Mistela' => 'mistela',
        'Vino Ciruela' => 'ciruela',
        'Vino Perfecto Amor' => 'perfecto_amor'
    ];
    
    $productoLiquido = $mapeoProductos[$nombreLiquido] ?? null;
    if (!$productoLiquido) {
        throw new Exception("Producto líquido no reconocido: $nombreLiquido");
    }
    
    // Determinar tipos de etiqueta según capacidad
    $tiposEtiqueta = [];
    switch ($config['capacidad_ml']) {
        case 750:
            $tiposEtiqueta = ['botella_750ml_adelante', 'botella_750ml_atras'];
            break;
        case 50:
            $tiposEtiqueta = ['botella_regalo_50ml'];
            break;
        case 1000:
            $tiposEtiqueta = ['porron'];
            break;
        default:
            $tiposEtiqueta = ['botella_750ml_adelante']; // Por defecto
    }
    
    $etiquetas = [];
    
    foreach ($tiposEtiqueta as $tipoEtiqueta) {
        $query = "SELECT * FROM etiquetas WHERE producto_liquido = :producto_liquido AND tipo_etiqueta = :tipo_etiqueta";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':producto_liquido', $productoLiquido);
        $stmt->bindParam(':tipo_etiqueta', $tipoEtiqueta);
        $stmt->execute();
        
        $etiqueta = $stmt->fetch();
        if ($etiqueta) {
            $etiquetas[] = [
                'id' => $etiqueta['id'],
                'nombre' => $etiqueta['nombre'],
                'tipo' => $tipoEtiqueta,
                'cantidad_necesaria' => $cantidadBotellas,
                'stock_disponible' => $etiqueta['stock_cantidad']
            ];
        }
    }
    
    return $etiquetas;
}

/**
 * Descontar componentes del inventario
 */
function descontarComponentes($db, $validacion, $cantidadBotellas, $loteId) {
    $config = $validacion['configuracion'];
    $liquido = $validacion['liquido'];
    $componentes = $validacion['componentes_necesarios'];
    
    // Descontar líquido
    $queryLiquido = "UPDATE liquidos SET stock_ml = stock_ml - :ml WHERE id = :id";
    $stmtLiquido = $db->prepare($queryLiquido);
    $stmtLiquido->bindParam(':ml', $componentes['liquido_ml']);
    $stmtLiquido->bindParam(':id', $liquido['id']);
    $stmtLiquido->execute();
    
    registrarMovimiento($db, 'liquido', $liquido['id'], 'embotellado', $liquido['stock_ml'], $componentes['liquido_ml'], $liquido['stock_ml'] - $componentes['liquido_ml'], "Embotellado lote #$loteId", $loteId);
    
    // Descontar botella
    $queryBotella = "UPDATE botellas SET stock_cantidad = stock_cantidad - :cantidad WHERE id = :id";
    $stmtBotella = $db->prepare($queryBotella);
    $stmtBotella->bindParam(':cantidad', $cantidadBotellas);
    $stmtBotella->bindParam(':id', $config['botella_id']);
    $stmtBotella->execute();
    
    registrarMovimiento($db, 'botella', $config['botella_id'], 'embotellado', $config['botella_stock'], $cantidadBotellas, $config['botella_stock'] - $cantidadBotellas, "Embotellado lote #$loteId", $loteId);
    
    // Descontar tapón
    $queryTapon = "UPDATE tapones SET stock_cantidad = stock_cantidad - :cantidad WHERE id = :id";
    $stmtTapon = $db->prepare($queryTapon);
    $stmtTapon->bindParam(':cantidad', $cantidadBotellas);
    $stmtTapon->bindParam(':id', $config['tapon_id']);
    $stmtTapon->execute();
    
    registrarMovimiento($db, 'tapon', $config['tapon_id'], 'embotellado', $config['tapon_stock'], $cantidadBotellas, $config['tapon_stock'] - $cantidadBotellas, "Embotellado lote #$loteId", $loteId);
    
    // Descontar cápsula (si aplica)
    if ($config['capsula_id'] && $componentes['capsulas'] > 0) {
        $queryCap = "UPDATE capsulas SET stock_cantidad = stock_cantidad - :cantidad WHERE id = :id";
        $stmtCap = $db->prepare($queryCap);
        $stmtCap->bindParam(':cantidad', $cantidadBotellas);
        $stmtCap->bindParam(':id', $config['capsula_id']);
        $stmtCap->execute();
        
        registrarMovimiento($db, 'capsula', $config['capsula_id'], 'embotellado', $config['capsula_stock'], $cantidadBotellas, $config['capsula_stock'] - $cantidadBotellas, "Embotellado lote #$loteId", $loteId);
    }
    
    // Descontar etiquetas
    foreach ($componentes['etiquetas'] as $etiqueta) {
        $queryEtiq = "UPDATE etiquetas SET stock_cantidad = stock_cantidad - :cantidad WHERE id = :id";
        $stmtEtiq = $db->prepare($queryEtiq);
        $stmtEtiq->bindParam(':cantidad', $etiqueta['cantidad_necesaria']);
        $stmtEtiq->bindParam(':id', $etiqueta['id']);
        $stmtEtiq->execute();
        
        registrarMovimiento($db, 'etiqueta', $etiqueta['id'], 'embotellado', $etiqueta['stock_disponible'], $etiqueta['cantidad_necesaria'], $etiqueta['stock_disponible'] - $etiqueta['cantidad_necesaria'], "Embotellado lote #$loteId", $loteId);
    }
}

/**
 * Generar código único de lote
 */
function generarCodigoLote($db, $fecha) {
    $fechaFormato = date('Ymd', strtotime($fecha));
    $base = "LOTE-$fechaFormato";
    
    $query = "SELECT COUNT(*) as total FROM lotes_embotellado WHERE codigo_lote LIKE :base";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':base', $base . '%');
    $stmt->execute();
    $result = $stmt->fetch();
    
    $numero = $result['total'] + 1;
    return $base . '-' . str_pad($numero, 3, '0', STR_PAD_LEFT);
}

/**
 * Obtener lotes de embotellado
 */
function getLotes($db) {
    try {
        $query = "SELECT le.*, l.nombre as liquido_nombre, ce.nombre_configuracion
                  FROM lotes_embotellado le
                  JOIN liquidos l ON le.liquido_id = l.id
                  JOIN configuracion_embotellado ce ON le.configuracion_id = ce.id
                  ORDER BY le.fecha_embotellado DESC, le.created_at DESC
                  LIMIT 100";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $lotes = $stmt->fetchAll();
        
        ApiResponse::success($lotes, "Lotes obtenidos exitosamente");
        
    } catch (Exception $e) {
        error_log("Error obteniendo lotes: " . $e->getMessage());
        ApiResponse::serverError();
    }
}

/**
 * Registrar movimiento de inventario
 */
function registrarMovimiento($db, $tipoComponente, $componenteId, $tipoMovimiento, $cantidadAnterior, $cantidadMovimiento, $cantidadNueva, $motivo, $referenciaEmbotellado = null) {
    try {
        $query = "INSERT INTO movimientos_inventario (tipo_componente, componente_id, tipo_movimiento, cantidad_anterior, cantidad_movimiento, cantidad_nueva, motivo, referencia_embotellado) 
                  VALUES (:tipo_componente, :componente_id, :tipo_movimiento, :cantidad_anterior, :cantidad_movimiento, :cantidad_nueva, :motivo, :referencia_embotellado)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':tipo_componente', $tipoComponente);
        $stmt->bindParam(':componente_id', $componenteId);
        $stmt->bindParam(':tipo_movimiento', $tipoMovimiento);
        $stmt->bindParam(':cantidad_anterior', $cantidadAnterior);
        $stmt->bindParam(':cantidad_movimiento', $cantidadMovimiento);
        $stmt->bindParam(':cantidad_nueva', $cantidadNueva);
        $stmt->bindParam(':motivo', $motivo);
        $stmt->bindParam(':referencia_embotellado', $referenciaEmbotellado);
        
        $stmt->execute();
    } catch (Exception $e) {
        error_log("Error registrando movimiento: " . $e->getMessage());
    }
}
?>
