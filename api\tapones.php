<?php
/**
 * API para gestión de tapones
 */

require_once 'config/Database.php';
require_once 'config/ApiResponse.php';

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $path_info = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
    $id = $path_info ? (int)ltrim($path_info, '/') : null;
    
    switch ($method) {
        case 'GET':
            if ($id) {
                getTapon($db, $id);
            } else {
                getTapones($db);
            }
            break;
        case 'POST':
            createTapon($db);
            break;
        case 'PUT':
            if (!$id) ApiResponse::error("ID requerido", 400);
            updateTapon($db, $id);
            break;
        case 'DELETE':
            if (!$id) ApiResponse::error("ID requerido", 400);
            deleteTapon($db, $id);
            break;
        default:
            ApiResponse::error("Método no permitido", 405);
    }
    
} catch (Exception $e) {
    error_log("Error en API tapones: " . $e->getMessage());
    ApiResponse::serverError();
}

function getTapones($db) {
    try {
        $query = "SELECT * FROM tapones ORDER BY tipo, nombre";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $tapones = $stmt->fetchAll();
        
        foreach ($tapones as &$tapon) {
            $tapon['stock_status'] = $tapon['stock_cantidad'] <= $tapon['stock_minimo'] ? 'bajo' : 'normal';
            $tapon['stock_cantidad'] = (int)$tapon['stock_cantidad'];
            $tapon['stock_minimo'] = (int)$tapon['stock_minimo'];
            $tapon['costo_unitario'] = (float)$tapon['costo_unitario'];
        }
        
        ApiResponse::success($tapones);
    } catch (Exception $e) {
        ApiResponse::serverError();
    }
}

function getTapon($db, $id) {
    try {
        $query = "SELECT * FROM tapones WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        
        $tapon = $stmt->fetch();
        if (!$tapon) ApiResponse::notFound();
        
        $tapon['stock_cantidad'] = (int)$tapon['stock_cantidad'];
        $tapon['stock_minimo'] = (int)$tapon['stock_minimo'];
        $tapon['costo_unitario'] = (float)$tapon['costo_unitario'];
        $tapon['stock_status'] = $tapon['stock_cantidad'] <= $tapon['stock_minimo'] ? 'bajo' : 'normal';
        
        ApiResponse::success($tapon);
    } catch (Exception $e) {
        ApiResponse::serverError();
    }
}

function createTapon($db) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        ApiResponse::validateRequired($data, ['nombre', 'tipo']);
        
        if (!in_array($data['tipo'], ['corcho_sintetico', 'tapa_rosca_negra', 'tapa_porron'])) {
            ApiResponse::validationError(['tipo' => 'Tipo inválido']);
        }
        
        $query = "INSERT INTO tapones (nombre, tipo, compatible_con, stock_cantidad, stock_minimo, costo_unitario, activo) 
                  VALUES (:nombre, :tipo, :compatible_con, :stock_cantidad, :stock_minimo, :costo_unitario, :activo)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':nombre', $data['nombre']);
        $stmt->bindParam(':tipo', $data['tipo']);
        $stmt->bindParam(':compatible_con', $data['compatible_con'] ?? '');
        $stmt->bindParam(':stock_cantidad', $data['stock_cantidad'] ?? 0, PDO::PARAM_INT);
        $stmt->bindParam(':stock_minimo', $data['stock_minimo'] ?? 100, PDO::PARAM_INT);
        $stmt->bindParam(':costo_unitario', $data['costo_unitario'] ?? 0);
        $stmt->bindParam(':activo', $data['activo'] ?? true, PDO::PARAM_BOOL);
        
        if ($stmt->execute()) {
            $newId = $db->lastInsertId();
            if (($data['stock_cantidad'] ?? 0) > 0) {
                registrarMovimiento($db, 'tapon', $newId, 'entrada', 0, $data['stock_cantidad'], $data['stock_cantidad'], 'Creación inicial');
            }
            ApiResponse::success(['id' => $newId], "Tapón creado exitosamente", 201);
        } else {
            ApiResponse::serverError();
        }
    } catch (Exception $e) {
        ApiResponse::serverError();
    }
}

function updateTapon($db, $id) {
    try {
        $data = ApiResponse::getJsonInput();
        $data = ApiResponse::sanitizeInput($data);
        
        $checkQuery = "SELECT stock_cantidad FROM tapones WHERE id = :id";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->bindParam(':id', $id, PDO::PARAM_INT);
        $checkStmt->execute();
        $current = $checkStmt->fetch();
        
        if (!$current) ApiResponse::notFound();
        
        $fields = [];
        $params = [':id' => $id];
        
        $allowedFields = ['nombre', 'tipo', 'compatible_con', 'stock_cantidad', 'stock_minimo', 'costo_unitario', 'activo'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = :$field";
                $params[":$field"] = $data[$field];
            }
        }
        
        if (empty($fields)) ApiResponse::error("No hay campos para actualizar", 400);
        
        $query = "UPDATE tapones SET " . implode(', ', $fields) . " WHERE id = :id";
        $stmt = $db->prepare($query);
        
        if ($stmt->execute($params)) {
            if (isset($data['stock_cantidad']) && $data['stock_cantidad'] != $current['stock_cantidad']) {
                $diferencia = $data['stock_cantidad'] - $current['stock_cantidad'];
                $tipoMovimiento = $diferencia > 0 ? 'entrada' : 'salida';
                registrarMovimiento($db, 'tapon', $id, $tipoMovimiento, $current['stock_cantidad'], abs($diferencia), $data['stock_cantidad'], 'Ajuste manual');
            }
            ApiResponse::success(null, "Tapón actualizado exitosamente");
        } else {
            ApiResponse::serverError();
        }
    } catch (Exception $e) {
        ApiResponse::serverError();
    }
}

function deleteTapon($db, $id) {
    try {
        $query = "UPDATE tapones SET activo = FALSE WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        
        if ($stmt->execute() && $stmt->rowCount() > 0) {
            ApiResponse::success(null, "Tapón eliminado exitosamente");
        } else {
            ApiResponse::notFound();
        }
    } catch (Exception $e) {
        ApiResponse::serverError();
    }
}

function registrarMovimiento($db, $tipoComponente, $componenteId, $tipoMovimiento, $cantidadAnterior, $cantidadMovimiento, $cantidadNueva, $motivo) {
    try {
        $query = "INSERT INTO movimientos_inventario (tipo_componente, componente_id, tipo_movimiento, cantidad_anterior, cantidad_movimiento, cantidad_nueva, motivo) 
                  VALUES (:tipo_componente, :componente_id, :tipo_movimiento, :cantidad_anterior, :cantidad_movimiento, :cantidad_nueva, :motivo)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':tipo_componente', $tipoComponente);
        $stmt->bindParam(':componente_id', $componenteId);
        $stmt->bindParam(':tipo_movimiento', $tipoMovimiento);
        $stmt->bindParam(':cantidad_anterior', $cantidadAnterior);
        $stmt->bindParam(':cantidad_movimiento', $cantidadMovimiento);
        $stmt->bindParam(':cantidad_nueva', $cantidadNueva);
        $stmt->bindParam(':motivo', $motivo);
        
        $stmt->execute();
    } catch (Exception $e) {
        error_log("Error registrando movimiento: " . $e->getMessage());
    }
}
?>
